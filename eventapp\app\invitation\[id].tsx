import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  ScrollView,
  TouchableOpacity,
  Share,
  Alert,
  SafeAreaView,
  Platform,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import * as FileSystem from 'expo-file-system';

interface InvitationData {
  id: string;
  title: string;
  date: string;
  time: string;
  location: string;
  message: string;
  templateId: string;
  backgroundImage: string;
  colors: string[];
  createdAt: string;
}

export default function SharedInvitation() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const [invitation, setInvitation] = useState<InvitationData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadInvitation();
  }, [id]);

  const loadInvitation = async () => {
    try {
      const invitations = await FileSystem.readAsStringAsync(
        FileSystem.documentDirectory + 'invitations.json'
      ).catch(() => '[]');
      
      const invitationsArray = JSON.parse(invitations);
      const foundInvitation = invitationsArray.find((inv: InvitationData) => inv.id === id);
      
      if (foundInvitation) {
        setInvitation(foundInvitation);
      } else {
        Alert.alert('Error', 'Invitation not found');
        router.back();
      }
    } catch (error) {
      console.error('Error loading invitation:', error);
      Alert.alert('Error', 'Failed to load invitation');
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const handleShare = async () => {
    if (!invitation) return;
    
    try {
      const shareableLink = `eventapp://invitation/${invitation.id}`;
      await Share.share({
        message: `Check out this invitation: ${shareableLink}`,
        url: shareableLink,
      });
    } catch (error) {
      console.error('Error sharing:', error);
      Alert.alert('Error', 'Failed to share invitation');
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.container}>
          <Text>Loading invitation...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!invitation) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.container}>
          <Text>Invitation not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Invitation</Text>
        </View>

        <View style={styles.invitationContainer}>
          <Image
            source={{ uri: invitation.backgroundImage }}
            style={styles.backgroundImage}
          />
          <View style={[styles.overlay, { backgroundColor: 'rgba(0,0,0,0.3)' }]}>
            <Text style={[styles.title, { color: invitation.colors[1] }]}>
              {invitation.title}
            </Text>
            <Text style={[styles.date, { color: invitation.colors[1] }]}>
              {invitation.date}
            </Text>
            <Text style={[styles.time, { color: invitation.colors[1] }]}>
              {invitation.time}
            </Text>
            <Text style={[styles.location, { color: invitation.colors[1] }]}>
              {invitation.location}
            </Text>
            <Text style={[styles.message, { color: invitation.colors[1] }]}>
              {invitation.message}
            </Text>
          </View>
        </View>

        <View style={styles.actions}>
          <TouchableOpacity 
            style={[styles.button, { backgroundColor: invitation.colors[0] }]} 
            onPress={handleShare}
          >
            <Text style={styles.buttonText}>Share Invitation</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    marginRight: 16,
  },
  backButtonText: {
    fontSize: 16,
    color: '#007AFF',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  invitationContainer: {
    margin: 16,
    borderRadius: 12,
    overflow: 'hidden',
    aspectRatio: 9/16,
  },
  backgroundImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  overlay: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  date: {
    fontSize: 24,
    textAlign: 'center',
    marginBottom: 10,
  },
  time: {
    fontSize: 24,
    textAlign: 'center',
    marginBottom: 10,
  },
  location: {
    fontSize: 20,
    textAlign: 'center',
    marginBottom: 20,
  },
  message: {
    fontSize: 18,
    textAlign: 'center',
    marginTop: 20,
  },
  actions: {
    padding: 16,
    gap: 12,
    paddingBottom: Platform.OS === 'ios' ? 34 : 16, // Add extra padding for iOS home indicator
  },
  button: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
}); 