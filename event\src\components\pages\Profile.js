import React, { useState, useEffect, useRef } from 'react';
import { auth, storage } from '../../lib/firebase';
import { onAuthStateChanged, updateProfile } from 'firebase/auth';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { Camera, Calendar, MapPin, Phone, User, Settings, LogOut, Edit2, ChevronRight } from 'lucide-react';
import PageWrapper from '../layout/PageWrapper';

const Profile = () => {
  const [user, setUser] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [displayName, setDisplayName] = useState('');
  const [photoURL, setPhotoURL] = useState('');
  const [error, setError] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [location, setLocation] = useState('');
  const [upcomingEvents] = useState([]);
  const fileInputRef = useRef(null);
  const [isSigningOut, setIsSigningOut] = useState(false);
  const [signOutError, setSignOutError] = useState('');

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (user) {
        setUser(user);
        setDisplayName(user.displayName || '');
        setPhotoURL(user.photoURL || '');
        const savedPhone = localStorage.getItem('userPhone');
        const savedLocation = localStorage.getItem('userLocation');
        if (savedPhone) setPhoneNumber(savedPhone);
        if (savedLocation) setLocation(savedLocation);
      }
    });

    return () => unsubscribe();
  }, []);

  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      setError('Please upload an image file');
      return;
    }

    setIsUploading(true);
    setError('');

    try {
      const storageRef = ref(storage, `profile-pictures/${user.uid}/${file.name}`);
      await uploadBytes(storageRef, file);
      const downloadURL = await getDownloadURL(storageRef);
      await updateProfile(auth.currentUser, {
        photoURL: downloadURL
      });
      setPhotoURL(downloadURL);
    } catch (error) {
      console.error('Error uploading image:', error);
      setError('Failed to upload image. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleUpdateProfile = async (e) => {
    e.preventDefault();
    setError('');

    try {
      await updateProfile(auth.currentUser, {
        displayName: displayName
      });
      localStorage.setItem('userPhone', phoneNumber);
      localStorage.setItem('userLocation', location);
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating profile:', error);
      setError('Failed to update profile. Please try again.');
    }
  };

  const handleSignOut = async () => {
    setIsSigningOut(true);
    setSignOutError('');
    
    try {
      await auth.signOut();
      // Clear any stored user data
      localStorage.removeItem('userPhone');
      localStorage.removeItem('userLocation');
      // Redirect to home page
      window.location.href = '/';
    } catch (error) {
      console.error('Error signing out:', error);
      setSignOutError('Failed to sign out. Please try again.');
    } finally {
      setIsSigningOut(false);
    }
  };

  if (!user) {
    return (
      <PageWrapper title="My Profile">
        <div className="bg-gray-50 dark:bg-gray-800 p-8 rounded-xl shadow-sm max-w-2xl mx-auto">
          <p className="text-center text-gray-700 dark:text-gray-300">
            Please sign in to view your profile.
          </p>
        </div>
      </PageWrapper>
    );
  }

  return (
  <PageWrapper title="My Profile" titleClassName="text-center">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Profile Card */}
          <div className="lg:col-span-1">
            <div className="bg-gray-50 dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden">
              <div className="relative h-40 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800">
                <div className="absolute -bottom-20 left-1/2 transform -translate-x-1/2">
                  <div className="relative group">
                    <img 
                      src={photoURL || `https://api.dicebear.com/7.x/avataaars/svg?seed=${user.email}&backgroundColor=b6e3f4`}
          alt="Profile" 
                      className="w-40 h-40 rounded-full border-4 border-white dark:border-gray-700 object-cover shadow-md"
        />
                    {isEditing && (
                      <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 rounded-full opacity-0 group-hover:opacity-100 transition-opacity">
                        <button
                          onClick={() => fileInputRef.current?.click()}
                          className="p-3 bg-white dark:bg-gray-800 rounded-full shadow-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                          disabled={isUploading}
                        >
                          <Camera className="w-6 h-6 text-gray-700 dark:text-gray-300" />
                        </button>
                      </div>
                    )}
                  </div>
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleImageUpload}
                    accept="image/*"
                    className="hidden"
                  />
                </div>
              </div>
              <div className="pt-24 pb-6 px-6">
                <h3 className="text-2xl font-bold text-center text-gray-800 dark:text-white">
                  {user.displayName || user.email?.split('@')[0] || 'User'}
                </h3>
                <p className="text-center text-gray-600 dark:text-gray-400 mt-1">
                  {user.email}
                </p>
                <div className="mt-6 space-y-4">
                  <div className="flex items-center text-gray-600 dark:text-gray-400">
                    <Phone className="w-5 h-5 mr-3 text-gray-500 dark:text-gray-400" />
                    <span>{phoneNumber || 'Not set'}</span>
                  </div>
                  <div className="flex items-center text-gray-600 dark:text-gray-400">
                    <MapPin className="w-5 h-5 mr-3 text-gray-500 dark:text-gray-400" />
                    <span>{location || 'Not set'}</span>
                  </div>
                </div>
                <div className="mt-6 flex justify-center space-x-4">
                  <button
                    onClick={() => setIsEditing(true)}
                    className="px-4 py-2 bg-gray-800 text-white rounded-lg 
                      hover:bg-gray-700 transition-all duration-300 shadow-sm hover:shadow-md"
                  >
                    <Edit2 className="w-4 h-4 inline-block mr-2" />
                    Edit Profile
                  </button>
                  <button
                    onClick={handleSignOut}
                    disabled={isSigningOut}
                    className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg 
                      hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors
                      disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSigningOut ? (
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Signing out...
                      </span>
                    ) : (
                      <>
                        <LogOut className="w-4 h-4 inline-block mr-2" />
                        Sign Out
                      </>
                    )}
                  </button>
                  {signOutError && (
                    <p className="mt-2 text-sm text-red-600 dark:text-red-400">{signOutError}</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-2">
            {isEditing ? (
              <div className="bg-gray-50 dark:bg-gray-800 rounded-xl shadow-sm p-8">
                <h3 className="text-2xl font-semibold text-gray-800 dark:text-white mb-6">
                  Edit Profile
                </h3>
                <form onSubmit={handleUpdateProfile} className="space-y-6">
                  {error && (
                    <div className="p-4 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 rounded-lg">
                      {error}
                    </div>
                  )}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Display Name
                    </label>
                    <input
                      type="text"
                      value={displayName}
                      onChange={(e) => setDisplayName(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg 
                        focus:outline-none focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-400 
                        bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      value={phoneNumber}
                      onChange={(e) => setPhoneNumber(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg 
                        focus:outline-none focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-400 
                        bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Location
                    </label>
                    <input
                      type="text"
                      value={location}
                      onChange={(e) => setLocation(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg 
                        focus:outline-none focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-400 
                        bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <div className="flex space-x-4">
                    <button
                      type="submit"
                      className="flex-1 py-3 bg-gray-800 text-white rounded-lg 
                        hover:bg-gray-700 transition-all duration-300 shadow-sm hover:shadow-md"
                    >
                      Save Changes
                    </button>
                    <button
                      type="button"
                      onClick={() => setIsEditing(false)}
                      className="flex-1 py-3 border border-gray-300 dark:border-gray-600 rounded-lg 
                        hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Upcoming Events */}
                <div className="bg-gray-50 dark:bg-gray-800 rounded-xl shadow-sm p-8">
                  <h3 className="text-2xl font-semibold text-gray-800 dark:text-white mb-6">
                    Upcoming Events
                  </h3>
                  {upcomingEvents.length > 0 ? (
                    <div className="space-y-4">
                      {upcomingEvents.map((event, index) => (
                        <div key={index} className="flex items-center justify-between p-4 bg-white dark:bg-gray-700 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                          <div className="flex items-center">
                            <Calendar className="w-6 h-6 text-gray-500 dark:text-gray-400 mr-4" />
                            <div>
                              <h4 className="font-medium text-gray-800 dark:text-white">{event.title}</h4>
                              <p className="text-sm text-gray-600 dark:text-gray-400">{event.date}</p>
                            </div>
                          </div>
                          <ChevronRight className="w-5 h-5 text-gray-400" />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 dark:text-gray-400">
                        No upcoming events
                      </p>
                    </div>
                  )}
                </div>

                {/* Quick Actions */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <button className="flex items-center justify-between p-6 bg-gray-50 dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                    <div className="flex items-center">
                      <User className="w-6 h-6 text-gray-500 dark:text-gray-400 mr-3" />
                      <span className="text-gray-800 dark:text-white font-medium">My Bookings</span>
                    </div>
                    <ChevronRight className="w-5 h-5 text-gray-400" />
                  </button>
                  <button className="flex items-center justify-between p-6 bg-gray-50 dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                    <div className="flex items-center">
                      <Settings className="w-6 h-6 text-gray-500 dark:text-gray-400 mr-3" />
                      <span className="text-gray-800 dark:text-white font-medium">Settings</span>
                    </div>
                    <ChevronRight className="w-5 h-5 text-gray-400" />
                  </button>
                </div>
              </div>
            )}
          </div>
      </div>
    </div>
  </PageWrapper>
);
};

export default Profile;