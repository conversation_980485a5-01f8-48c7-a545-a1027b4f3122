import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, useColorScheme } from 'react-native';
import { router } from 'expo-router';
import { Event } from '@/types';
import Card from '@/components/ui/Card';
import Colors from '@/constants/Colors';
import { Calendar, MapPin, Clock } from 'lucide-react-native';

interface EventCardProps {
  event: Event;
  compact?: boolean;
}

export default function EventCard({ event, compact = false }: EventCardProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const colors = Colors[colorScheme];

  const handlePress = () => {
    router.push(`/event/${event.id}`);
  };

  // Format date string to more readable format
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  if (compact) {
    return (
      <TouchableOpacity activeOpacity={0.8} onPress={handlePress}>
        <Card 
          style={styles.compactCard} 
          padding="none" 
          elevation="low"
          borderRadius="small"
        >
          <Image source={{ uri: event.image }} style={styles.compactImage} />
          <View style={styles.compactContent}>
            <Text 
              style={[styles.compactTitle, { color: colors.text }]}
              numberOfLines={1}
            >
              {event.title}
            </Text>
            <View style={styles.compactDetails}>
              <View style={styles.compactDetailItem}>
                <Calendar size={14} color={colors.primary} />
                <Text 
                  style={[styles.compactDetailText, { color: colors.darkGray }]}
                  numberOfLines={1}
                >
                  {formatDate(event.date)}
                </Text>
              </View>
            </View>
          </View>
        </Card>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity activeOpacity={0.8} onPress={handlePress}>
      <Card style={styles.card} padding="none" elevation="medium">
        <Image source={{ uri: event.image }} style={styles.image} />
        
        {event.featured && (
          <View style={[styles.featuredBadge, { backgroundColor: colors.primary }]}>
            <Text style={styles.featuredText}>Featured</Text>
          </View>
        )}
        
        <View style={styles.content}>
          <Text style={[styles.category, { color: colors.primary }]}>
            {event.category.charAt(0).toUpperCase() + event.category.slice(1)}
          </Text>
          
          <Text style={[styles.title, { color: colors.text }]}>
            {event.title}
          </Text>
          
          <View style={styles.details}>
            <View style={styles.detailItem}>
              <Calendar size={16} color={colors.primary} />
              <Text style={[styles.detailText, { color: colors.darkGray }]}>
                {formatDate(event.date)}
              </Text>
            </View>
            
            <View style={styles.detailItem}>
              <Clock size={16} color={colors.primary} />
              <Text style={[styles.detailText, { color: colors.darkGray }]}>
                {event.time}
              </Text>
            </View>
            
            <View style={styles.detailItem}>
              <MapPin size={16} color={colors.primary} />
              <Text 
                style={[styles.detailText, { color: colors.darkGray }]}
                numberOfLines={1}
              >
                {event.location}
              </Text>
            </View>
          </View>
          
          <View style={styles.footer}>
            <Text style={[styles.price, { color: colors.text }]}>
              ${event.price.toFixed(2)}
            </Text>
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 20,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: 180,
    resizeMode: 'cover',
  },
  featuredBadge: {
    position: 'absolute',
    top: 12,
    left: 12,
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 50,
  },
  featuredText: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  content: {
    padding: 16,
  },
  category: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    marginBottom: 4,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 10,
  },
  details: {
    marginBottom: 12,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
  },
  detailText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    marginLeft: 8,
  },
  footer: {
    marginTop: 4,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  price: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
  },
  // Compact styles
  compactCard: {
    width: 160,
    marginRight: 12,
    overflow: 'hidden',
  },
  compactImage: {
    width: '100%',
    height: 100,
    resizeMode: 'cover',
  },
  compactContent: {
    padding: 10,
  },
  compactTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 6,
  },
  compactDetails: {
    marginBottom: 4,
  },
  compactDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  compactDetailText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    marginLeft: 4,
  },
});