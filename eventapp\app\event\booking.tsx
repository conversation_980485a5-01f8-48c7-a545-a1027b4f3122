import React, { useState } from 'react';
import { View, StyleSheet, Platform, Alert } from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import EventQuestionnaire from '../../components/EventQuestionnaire';
import BookingSummary from '../../components/BookingSummary';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface EventDetails {
  eventType: string;
  guestCount: string;
  eventDate: Date;
  eventTime: Date;
  location: string;
  services: string[];
  budget: string;
  serviceDetails?: Record<string, any>;
}

export default function BookingPage() {
  const [eventDetails, setEventDetails] = useState<EventDetails | null>(null);
  const [showSummary, setShowSummary] = useState(false);

  const handleQuestionnaireComplete = (answers: Record<string, any>) => {
    setEventDetails(answers as EventDetails);
    setShowSummary(true);
  };

  const handleEdit = () => {
    setShowSummary(false);
  };

  const handleConfirm = async () => {
    if (!eventDetails) return;
    try {
      const booking = {
        ...eventDetails,
        status: 'Confirmed',
        createdAt: new Date().toISOString(),
      };

      // Handle storage based on platform
      if (Platform.OS === 'web') {
        // Web storage
        const existingBookingsStr = localStorage.getItem('bookings');
        const existingBookings = existingBookingsStr ? JSON.parse(existingBookingsStr) : [];
        localStorage.setItem('bookings', JSON.stringify([...existingBookings, booking]));
      } else {
        // Mobile storage
        const existingBookingsStr = await AsyncStorage.getItem('bookings');
        const existingBookings = existingBookingsStr ? JSON.parse(existingBookingsStr) : [];
        await AsyncStorage.setItem('bookings', JSON.stringify([...existingBookings, booking]));
      }

      // Navigate based on platform
      if (Platform.OS === 'web') {
        router.replace('/bookings');
      } else {
        router.replace('/(tabs)');
      }
    } catch (error) {
      console.error('Error confirming booking:', error);
      Alert.alert('Error', 'Failed to confirm booking. Please try again.');
    }
  };

  if (!showSummary) {
    return (
      <SafeAreaView style={{ flex: 1 }}>
        <EventQuestionnaire onComplete={handleQuestionnaireComplete} />
      </SafeAreaView>
    );
  }

  // Prepare props for BookingSummary
  const basicDetails = {
    eventDate: eventDetails?.eventDate instanceof Date ? eventDetails.eventDate.toLocaleDateString() : String(eventDetails?.eventDate),
    eventTime: eventDetails?.eventTime instanceof Date ? eventDetails.eventTime.toLocaleTimeString() : String(eventDetails?.eventTime),
    eventLocation: eventDetails?.location ? String(eventDetails.location) : '',
    numberOfPersons: eventDetails?.guestCount ? String(eventDetails.guestCount) : '',
    services: {
      Decoration: eventDetails?.services?.includes('Decoration') || false,
      Catering: eventDetails?.services?.includes('Catering') || false,
      Priest: eventDetails?.services?.includes('Priest Services') || false,
      Photography: eventDetails?.services?.includes('Photography') || false,
      Videography: eventDetails?.services?.includes('Videography') || false,
      Music: eventDetails?.services?.includes('Music/DJ') || false,
      Transportation: eventDetails?.services?.includes('Transportation') || false,
      Invitation: eventDetails?.services?.includes('Invitation Cards') || false,
      Gifts: eventDetails?.services?.includes('Gifts & Favors') || false,
      Lighting: eventDetails?.services?.includes('Lighting') || false,
    },
    additionalNotes: '',
  };

  const selections = eventDetails?.serviceDetails || {};

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <BookingSummary
        selections={selections}
        basicDetails={basicDetails}
        eventType={eventDetails?.eventType || ''}
        onEdit={handleEdit}
        onConfirm={handleConfirm}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  }
}); 