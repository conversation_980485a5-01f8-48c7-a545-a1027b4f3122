{"name": "@types/eslint", "version": "9.6.1", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus"}, {"name": "<PERSON>", "githubUsername": "j-f1", "url": "https://github.com/j-f1"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/saadq"}, {"name": "<PERSON>", "githubUsername": "JasonHK", "url": "https://github.com/JasonHK"}, {"name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>", "url": "https://github.com/bradzacher"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}, {"name": "<PERSON>", "githubUsername": "bmish", "url": "https://github.com/bmish"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "bc2620143f844d291da2d199e7b8e2605e3277f1941a508dc72ac92843b149b6", "typeScriptVersion": "4.8"}