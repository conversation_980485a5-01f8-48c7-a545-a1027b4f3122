export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
}

export interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  location: string;
  venue: string;
  price: number;
  category: string;
  image: string;
  organizer: string;
  featured?: boolean;
  attendees?: number;
  rating?: number;
}

export interface Booking {
  id: string;
  eventId: string;
  userId: string;
  ticketCount: number;
  totalAmount: number;
  bookingDate: string;
  status: 'confirmed' | 'pending' | 'cancelled';
}

export interface Category {
  id: string;
  name: string;
  icon: string;
}