import React from 'react';
import { ScrollView, TouchableOpacity, Text, StyleSheet, View, useColorScheme } from 'react-native';
import { Category } from '@/types';
import Colors from '@/constants/Colors';
import { 
  Music, Presentation, Utensils, Heart, Palette, Trophy, Briefcase, Film, 
  GraduationCap, Users, CircleUser, Circle 
} from 'lucide-react-native';

interface CategoryListProps {
  categories: Category[];
  selectedCategory: string | null;
  onSelectCategory: (categoryId: string | null) => void;
}

export default function CategoryList({ 
  categories, 
  selectedCategory, 
  onSelectCategory 
}: CategoryListProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const colors = Colors[colorScheme];

  const renderIcon = (iconName: string, isSelected: boolean) => {
    const color = isSelected ? 'white' : colors.primary;
    const size = 20;

    switch (iconName.toLowerCase()) {
      case 'music':
        return <Music size={size} color={color} />;
      case 'presentation':
        return <Presentation size={size} color={color} />;
      case 'utensils':
        return <Utensils size={size} color={color} />;
      case 'heart':
        return <Heart size={size} color={color} />;
      case 'palette':
        return <Palette size={size} color={color} />;
      case 'trophy':
        return <Trophy size={size} color={color} />;
      case 'briefcase':
        return <Briefcase size={size} color={color} />;
      case 'film':
        return <Film size={size} color={color} />;
      case 'education':
        return <GraduationCap size={size} color={color} />;
      case 'community':
        return <Users size={size} color={color} />;
      case 'personal':
        return <CircleUser size={size} color={color} />;
      default:
        return <Circle size={size} color={color} />;
    }
  };

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.container}
    >
      <TouchableOpacity
        style={[
          styles.categoryItem,
          {
            backgroundColor: selectedCategory === null 
              ? colors.primary 
              : colorScheme === 'dark' ? colors.lightGray : '#F3F4F6',
          },
        ]}
        onPress={() => onSelectCategory(null)}
      >
        <Text 
          style={[
            styles.categoryText, 
            { 
              color: selectedCategory === null 
                ? 'white' 
                : colors.text 
            }
          ]}
        >
          All
        </Text>
      </TouchableOpacity>

      {categories.map((category) => {
        const isSelected = selectedCategory === category.id;
        
        return (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryItem,
              {
                backgroundColor: isSelected 
                  ? colors.primary 
                  : colorScheme === 'dark' ? colors.lightGray : '#F3F4F6',
              },
            ]}
            onPress={() => onSelectCategory(category.id)}
          >
            <View style={styles.iconContainer}>
              {renderIcon(category.icon, isSelected)}
            </View>
            <Text 
              style={[
                styles.categoryText, 
                { color: isSelected ? 'white' : colors.text }
              ]}
            >
              {category.name}
            </Text>
          </TouchableOpacity>
        );
      })}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 10,
    borderRadius: 50,
  },
  iconContainer: {
    marginRight: 8,
  },
  categoryText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
});