import React from 'react';
import PageWrapper from '../layout/PageWrapper';

const EInvites = () => (
  <PageWrapper
    title="Digital Invitations"
    subtitle="Create beautiful digital invitations for your events"
  >
    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
      {['Wedding', 'Birthday', 'Baby Shower'].map((template, index) => (
        <div key={index} className="bg-light-secondary dark:bg-dark-secondary p-6 rounded-lg shadow-md">
          <h4 className="text-xl font-semibold text-light-primary dark:text-white mb-3">
            {template} Template
          </h4>
          <p className="text-light-secondary dark:text-gray-400 mb-4">
            Elegant and customizable design
          </p>
          <button className="btn btn-primary">
            Use Template
          </button>
        </div>
      ))}
    </div>
  </PageWrapper>
);

export default EInvites;