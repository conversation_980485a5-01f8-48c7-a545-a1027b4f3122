import React from 'react';
import { View, StyleSheet, Dimensions, Platform } from 'react-native';
import { WebView as RNWebView } from 'react-native-webview';

interface WebViewProps {
  uri: string;
  style?: any;
}

export default function WebView({ uri, style }: WebViewProps) {
  const screenWidth = Dimensions.get('window').width;
  const isWeb = Platform.OS === 'web';
  
  // Calculate dimensions based on Instagram's 4:5 aspect ratio
  const maxWidth = Math.min(screenWidth * 0.9, 600); // Max width of 600px or 90% of screen width
  const height = maxWidth * (5/4); // 4:5 aspect ratio

  return (
    <View style={[styles.container, { width: maxWidth, height }, style]}>
      <RNWebView
        source={{ uri }}
        style={styles.webview}
        scrollEnabled={true}
        bounces={false}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignSelf: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
    marginVertical: 16,
  },
  webview: {
    flex: 1,
  },
}); 