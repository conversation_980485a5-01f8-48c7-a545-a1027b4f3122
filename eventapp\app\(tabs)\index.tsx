import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, useColorScheme, TouchableOpacity, SafeAreaView, Image, Modal, Dimensions } from 'react-native';
import { router } from 'expo-router';
import Colors from '@/constants/Colors';
import Button from '@/components/ui/Button';
import { Bell, ArrowRight } from 'lucide-react-native';
import BookingForm from '@/components/BookingForm';
import { LinearGradient } from 'expo-linear-gradient';
import Svg, { Text as SvgText, Defs, LinearGradient as SvgLinearGradient, Stop } from 'react-native-svg';

const eventTypes = [
  {
    id: '1',
    title: 'Corporate Events',
    description: 'Conferences, seminars, team building, and business meetings',
    image: 'https://images.pexels.com/photos/2774556/pexels-photo-2774556.jpeg'
  },
  {
    id: '2',
    title: 'Social Gatherings',
    description: 'Birthday parties, anniversaries, reunions, and celebrations',
    image: 'https://images.pexels.com/photos/7180617/pexels-photo-7180617.jpeg'
  },
  {
    id: '3',
    title: 'Weddings',
    description: 'Wedding ceremonies, receptions, and engagement parties',
    image: 'https://images.pexels.com/photos/1114425/pexels-photo-1114425.jpeg'
  },
  {
    id: '4',
    title: 'Educational Events',
    description: 'Workshops, training sessions, and educational seminars',
    image: 'https://images.pexels.com/photos/7092613/pexels-photo-7092613.jpeg'
  }
];

// SVG GradientText component
function SvgGradientText({ text, fontSize = 32, fontWeight = 'bold', width = 220, height = 40, style }: { text: string; fontSize?: number; fontWeight?: string; width?: number; height?: number; style?: any }) {
  return (
    <Svg height={height} width={width} style={style}>
      <Defs>
        <SvgLinearGradient id="grad" x1="0" y1="0" x2="1" y2="0">
          <Stop offset="0" stopColor="#f5225a" />
          <Stop offset="0.5" stopColor="#db3e43" />
          <Stop offset="1" stopColor="#f28b57" />
        </SvgLinearGradient>
      </Defs>
      <SvgText
        fill="url(#grad)"
        fontSize={fontSize}
        fontWeight={fontWeight}
        x={0}
        y={fontSize}
        fontFamily="Inter-Bold"
      >
        {text}
      </SvgText>
    </Svg>
  );
}

export default function HomeScreen() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const screenWidth = Dimensions.get('window').width;
  const isSmallScreen = screenWidth < 600;

  const handleOpenModal = () => setIsModalOpen(true);
  const handleCloseModal = () => setIsModalOpen(false);

  // Scroll to How It Works section (native workaround: scrollTo or ref)
  // For now, just a placeholder
  const handleHowItWorks = () => {
    // You can implement scroll-to-section if you use a ScrollView ref
  };

  const colorScheme = useColorScheme() ?? 'light';
  const colors = Colors[colorScheme];

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Welcome Text */}
        <View style={styles.welcomeContainer}>
          <Text style={styles.welcomeText}>Welcome to</Text>
          <SvgGradientText text="EaseMyEvent" fontSize={32} width={220} height={40} style={{ alignSelf: 'center' }} />
        </View>
        {/* Hero Section */}
        <LinearGradient
          colors={['#FFF7E6', '#FFFDE4', '#FFFFFF']}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          style={[
            styles.heroSection,
            { width: '100%', minHeight: 260 },
          ]}
        >
          <View style={styles.heroContent}>
            <View style={styles.heroImageContainer}>
              <View style={styles.heroImageBg} />
              <Image
                source={require('@/assets/images/photo.jpg')}
                style={styles.heroImage}
                resizeMode="cover"
                onError={e => console.log('Image failed to load', e.nativeEvent)}
              />
            </View>
            <View style={styles.heroTextContainer}>
              <View style={styles.heroTitleRow}> 
                <Text style={styles.heroTitle}>
                  Celebrate Your Special Moments
                </Text>
                <SvgGradientText 
                  text="Hassle-Free" 
                  fontSize={34} 
                  width={220} 
                  height={40} 
                  style={{ alignSelf: 'flex-end' }} 
                />
              </View>
              <Text style={styles.heroSubtitle}>
                From birthdays to house warmings to traditional Hindu poojas, we make organizing events just a few taps away—at prices lower than traditional planners.
              </Text>
            </View>
          </View>
        </LinearGradient>
        <Modal
          visible={isModalOpen}
          animationType="slide"
          onRequestClose={handleCloseModal}
          transparent
        >
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Book Your Event</Text>
            <BookingForm 
              step={{
                title: "Event Details",
                category: "BasicDetails",
                options: [],
                type: "form" // Added required type property based on lint error
              }}
              selectedOption={null}
              onSelect={() => {}}
              onNext={handleCloseModal}
              onPrevious={() => {}}
              isLastStep={true}
              isFirstStep={true}
            />
            <TouchableOpacity onPress={handleCloseModal} style={styles.closeModalButton}>
              <Text style={styles.closeModalButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </Modal>
        {/* Header */}

        {/* Event Types */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Event Types
          </Text>
          <Text style={[styles.sectionSubtitle, { color: colors.darkGray }]}>
            Choose from our variety of event categories
          </Text>
          
          {eventTypes.map((type) => (
            <TouchableOpacity 
              key={type.id}
              style={[styles.eventTypeCard, { backgroundColor: colors.card }]}
              onPress={() => router.push('/events')}
            >
              <Image 
                source={{ uri: type.image }} 
                style={styles.eventTypeImage}
              />
              <View style={styles.eventTypeContent}>
                <View style={styles.eventTypeHeader}>
                  <Text style={[styles.eventTypeTitle, { color: colors.text }]}>
                    {type.title}
                  </Text>
                  <ArrowRight size={20} color={colors.primary} />
                </View>
                <Text style={[styles.eventTypeDescription, { color: colors.darkGray }]}>
                  {type.description}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Why Choose Us */}
        <View style={[styles.section, styles.whyChooseUs]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Why Choose Us
          </Text>
          <View style={styles.features}>
            <View style={styles.featureItem}>
              <Text style={[styles.featureTitle, { color: colors.text }]}>
                Expert Planning
              </Text>
              <Text style={[styles.featureDescription, { color: colors.darkGray }]}>
                Professional event planning tools and guidance
              </Text>
            </View>
            <View style={styles.featureItem}>
              <Text style={[styles.featureTitle, { color: colors.text }]}>
                Vendor Network
              </Text>
              <Text style={[styles.featureDescription, { color: colors.darkGray }]}>
                Access to trusted vendors and service providers
              </Text>
            </View>
            <View style={styles.featureItem}>
              <Text style={[styles.featureTitle, { color: colors.text }]}>
                Budget Management
              </Text>
              <Text style={[styles.featureDescription, { color: colors.darkGray }]}>
                Easy budget tracking and cost optimization
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    marginTop: 4,
  },
  notificationButton: {
    padding: 10,
    borderRadius: 50,
  },
  heroSection: {
    paddingVertical: 40,
    paddingHorizontal: 24,
    borderBottomLeftRadius: 32,
    borderBottomRightRadius: 32,
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: '#FFF7E6',
  },
  heroContent: {
    flexDirection: 'column',
    alignItems: 'center',
    maxWidth: 1200,
    marginHorizontal: 'auto',
    width: '100%',
  },
  heroImageContainer: {
    width: '100%',
    maxWidth: 800,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    minHeight: 400,
    marginBottom: 32,
    overflow: 'hidden',
    borderRadius: 32,
  },
  heroImageBg: {
    display: 'none', // Hide the background circle for full-width image
  },
  heroImage: {
    width: '100%',
    height: 400,
    borderRadius: 32,
    shadowColor: '#000',
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 6,
    zIndex: 2,
    backgroundColor: '#eee',
  },
  heroTextContainer: {
    width: '100%',
    maxWidth: 800,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 2,
    padding: 16,
  },
  heroTitleRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    flexWrap: 'wrap',
    marginBottom: 12,
    gap: 8,
    justifyContent: 'center',
  },
  heroTitle: {
    fontSize: 38,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
    color: '#1a1a1a',
    lineHeight: 44,
    textShadowColor: 'rgba(255, 255, 255, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  heroSubtitle: {
    fontSize: 17,
    textAlign: 'center',
    marginBottom: 32,
    color: '#333333',
    maxWidth: 600,
    lineHeight: 24,
    textShadowColor: 'rgba(255, 255, 255, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 24,
    width: '90%',
    maxWidth: 400,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  closeModalButton: {
    marginTop: 16,
    padding: 10,
    backgroundColor: '#ff5a5f',
    borderRadius: 8,
  },
  closeModalButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  section: {
    padding: 16,
    marginTop: 32,
  },
  sectionTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    marginBottom: 24,
  },
  eventTypeCard: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 16,
    marginBottom: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    padding: 12,
  },
  eventTypeImage: {
    width: 140,
    height: 140,
    borderRadius: 12,
  },
  eventTypeContent: {
    flex: 1,
    paddingLeft: 16,
    justifyContent: 'center',
  },
  eventTypeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  eventTypeTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 8,
  },
  eventTypeDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  whyChooseUs: {
    marginBottom: 32,
  },
  features: {
    marginTop: 24,
  },
  featureItem: {
    marginBottom: 24,
  },
  featureTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 8,
  },
  featureDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
  },
  welcomeContainer: {
    alignItems: 'center',
    marginTop: 24,
    marginBottom: 8,
  },
  welcomeText: {
    fontSize: 18,
    color: '#888',
    fontFamily: 'Inter-Regular',
    marginBottom: 2,
  },
});