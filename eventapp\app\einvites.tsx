import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Modal, TextInput, FlatList } from 'react-native';
import * as Contacts from 'expo-contacts';
import { Linking } from 'react-native';
import { useRouter } from 'expo-router';

interface Template {
  id: string;
  name: string;
  description: string;
  colors: [string, string];
  defaultMessage: string;
  backgroundImage: string;
}

interface Contact {
  id: string;
  name: string;
  phoneNumbers?: Array<{
    number: string;
    label: string;
  }>;
}

const templates: Template[] = [
  {
    id: 'wedding',
    name: 'Wedding',
    description: 'Elegant and romantic design for your special day',
    colors: ['#FFD1DC', '#FFB6C1'],
    defaultMessage: 'Join us in celebrating our wedding!',
    backgroundImage: 'https://images.unsplash.com/photo-1519741497674-611481863552',
  },
  {
    id: 'birthday',
    name: 'Birthday',
    description: 'Fun and festive design for birthday celebrations',
    colors: ['#FFD700', '#FFA500'],
    defaultMessage: 'You\'re invited to my birthday party!',
    backgroundImage: 'https://images.unsplash.com/photo-1530103862676-de8c9debad1d',
  },
  {
    id: 'babyshower',
    name: 'Baby Shower',
    description: 'Sweet and adorable design for baby showers',
    colors: ['#87CEEB', '#ADD8E6'],
    defaultMessage: 'Join us for a baby shower celebration!',
    backgroundImage: 'https://images.unsplash.com/photo-1519689680058-324335c77eba',
  },
  {
    id: 'graduation',
    name: 'Graduation',
    description: 'Professional and celebratory design for graduations',
    colors: ['#4169E1', '#1E90FF'],
    defaultMessage: 'Celebrate my graduation with me!',
    backgroundImage: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1',
  },
  {
    id: 'anniversary',
    name: 'Anniversary',
    description: 'Classic and timeless design for anniversaries',
    colors: ['#FF69B4', '#FF1493'],
    defaultMessage: 'Join us in celebrating our anniversary!',
    backgroundImage: 'https://images.unsplash.com/photo-1511285560929-80b456fea0bc',
  },
  {
    id: 'housewarming',
    name: 'House Warming',
    description: 'Warm and welcoming design for new homes',
    colors: ['#98FB98', '#90EE90'],
    defaultMessage: 'Come celebrate our new home!',
    backgroundImage: 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6',
  },
];

export default function EInvitesPage() {
  const router = useRouter();
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [selectedContacts, setSelectedContacts] = useState<Contact[]>([]);
  const [customMessage, setCustomMessage] = useState('');
  const [showContactModal, setShowContactModal] = useState(false);
  const [showTemplateModal, setShowTemplateModal] = useState(false);

  const handleTemplateSelect = (template: Template) => {
    router.push({
      pathname: '/template-editor',
      params: { 
        templateId: template.id,
        templateName: template.name,
        defaultMessage: template.defaultMessage,
        backgroundImage: template.backgroundImage,
        colors: JSON.stringify(template.colors)
      }
    });
  };

  const requestContactsPermission = async () => {
    const { status } = await Contacts.requestPermissionsAsync();
    if (status === 'granted') {
      const { data } = await Contacts.getContactsAsync({
        fields: [Contacts.Fields.PhoneNumbers, Contacts.Fields.Name],
      });
      setContacts(data as Contact[]);
      setShowContactModal(true);
    }
  };

  const sendWhatsAppInvite = async (phoneNumber: string) => {
    if (!selectedTemplate) return;
    const message = customMessage || selectedTemplate.defaultMessage;
    const whatsappUrl = `whatsapp://send?phone=${phoneNumber}&text=${encodeURIComponent(message)}`;
    try {
      await Linking.openURL(whatsappUrl);
    } catch (error) {
      console.error('Error opening WhatsApp:', error);
    }
  };

  const renderTemplateModal = () => (
    <Modal
      visible={showTemplateModal}
      animationType="slide"
      transparent={true}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>Customize Your Invitation</Text>
          <TextInput
            style={styles.messageInput}
            multiline
            placeholder="Enter your custom message"
            value={customMessage}
            onChangeText={setCustomMessage}
          />
          <TouchableOpacity
            style={styles.button}
            onPress={() => {
              setShowTemplateModal(false);
              requestContactsPermission();
            }}
          >
            <Text style={styles.buttonText}>Select Contacts</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, styles.secondaryButton]}
            onPress={() => setShowTemplateModal(false)}
          >
            <Text style={styles.buttonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  const renderContactModal = () => (
    <Modal
      visible={showContactModal}
      animationType="slide"
      transparent={true}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>Select Contacts</Text>
          <FlatList
            data={contacts}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.contactItem}
                onPress={() => {
                  if (item.phoneNumbers && item.phoneNumbers[0]) {
                    sendWhatsAppInvite(item.phoneNumbers[0].number);
                  }
                }}
              >
                <Text style={styles.contactName}>{item.name}</Text>
                {item.phoneNumbers && item.phoneNumbers[0] && (
                  <Text style={styles.contactPhone}>{item.phoneNumbers[0].number}</Text>
                )}
              </TouchableOpacity>
            )}
          />
          <TouchableOpacity
            style={[styles.button, styles.secondaryButton]}
            onPress={() => setShowContactModal(false)}
          >
            <Text style={styles.buttonText}>Close</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.header}>Digital Invitations</Text>
      <Text style={styles.subheader}>Create beautiful digital invitations for your events</Text>
      <View style={styles.grid}>
        {templates.map((template) => (
          <TouchableOpacity
            key={template.id}
            style={[styles.card, { backgroundColor: template.colors[0] }]}
            onPress={() => handleTemplateSelect(template)}
          >
            <Text style={styles.title}>{template.name} Template</Text>
            <Text style={styles.description}>{template.description}</Text>
            <View style={[styles.button, { backgroundColor: template.colors[1] }]}>
              <Text style={styles.buttonText}>Use Template</Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>
      {renderTemplateModal()}
      {renderContactModal()}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subheader: {
    fontSize: 16,
    color: '#555',
    marginBottom: 16,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  card: {
    width: '48%',
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 6,
    color: '#333',
  },
  description: {
    fontSize: 14,
    color: '#444',
    marginBottom: 12,
  },
  button: {
    paddingVertical: 10,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    width: '90%',
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  messageInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 10,
    marginBottom: 15,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  secondaryButton: {
    backgroundColor: '#666',
    marginTop: 10,
  },
  contactItem: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  contactName: {
    fontSize: 16,
    fontWeight: '500',
  },
  contactPhone: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
}); 