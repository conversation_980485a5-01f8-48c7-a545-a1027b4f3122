import React from 'react';

const BookingSummary = ({ selections, eventType }) => {
  // No longer calculating total price as per user request

  // Helper function to get emoji for service category
  const getCategoryEmoji = (category) => {
    const emojiMap = {
      'Decoration': '🎨',
      'Catering': '🍽️',
      'Priest': '🙏',
      'Photography': '📸',
      'Videography': '🎥',
      'Music': '🎵',
      'Transportation': '🚗',
      'Invitation': '💌',
      'Gifts': '🎁',
      'Lighting': '💡',
      'Date': '📅',
      'TimeSlot': '⏰'
    };
    return emojiMap[category] || '✨';
  };

  // Helper function to render service details
  const renderServiceDetails = (selection) => {
    if (!selection.serviceDetails) return null;

    return (
      <div className="mt-2 ml-4 text-sm">
        {Object.entries(selection.serviceDetails).map(([key, value], i) => {
          // Skip empty values
          if (!value || (Array.isArray(value) && value.length === 0)) return null;

          // Format the key for display
          const formattedKey = key
            .replace(/([A-Z])/g, ' $1') // Add space before capital letters
            .replace(/^./, str => str.toUpperCase()); // Capitalize first letter

          return (
            <div key={i} className="mb-1">
              <span className="text-gray-500 dark:text-gray-400">{formattedKey}: </span>
              <span className="font-medium">
                {Array.isArray(value) ? value.join(', ') : value}
              </span>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold text-light-primary dark:text-white mb-6">
        Booking Summary
      </h3>

      {/* Basic Details Section */}
      <div className="bg-white dark:bg-dark-secondary p-6 rounded-lg shadow-md">
        <h4 className="text-lg font-semibold text-light-primary dark:text-white mb-4 flex items-center">
          <span className="mr-2">📋</span>
          Event Details
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Event Type</p>
            <p className="font-medium">{eventType.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Date</p>
            <p className="font-medium">{selections.BasicDetails?.eventDate || 'Not specified'}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Time</p>
            <p className="font-medium">{selections.BasicDetails?.eventTime || 'Not specified'}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Location</p>
            <p className="font-medium">{selections.BasicDetails?.eventLocation || 'Not specified'}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Number of Guests</p>
            <p className="font-medium">{selections.BasicDetails?.numberOfPersons || 'Not specified'}</p>
          </div>
        </div>
        {selections.BasicDetails?.additionalNotes && (
          <div className="mt-4">
            <p className="text-sm text-gray-500 dark:text-gray-400">Additional Notes</p>
            <p className="text-sm">{selections.BasicDetails.additionalNotes}</p>
          </div>
        )}
      </div>

      {/* Selected Services Section */}
      <div className="bg-white dark:bg-dark-secondary p-6 rounded-lg shadow-md">
        <h4 className="text-lg font-semibold text-light-primary dark:text-white mb-4 flex items-center">
          <span className="mr-2">✅</span>
          Selected Services
        </h4>
        <div className="space-y-4">
          {Object.entries(selections)
            .filter(([category]) => category !== 'BasicDetails') // Skip BasicDetails in this section
            .map(([category, selection], index) => (
              <div key={index} className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-4">
                <div className="flex items-center">
                  <span className="text-xl mr-2">{getCategoryEmoji(category)}</span>
                  <h5 className="text-md font-semibold text-light-primary dark:text-white">
                    {category}
                  </h5>
                </div>

                {renderServiceDetails(selection)}

                {/* Individual service prices removed as per user request */}
              </div>
            ))}

          {/* Total Price removed as per user request */}
        </div>
      </div>
    </div>
  );
};

export default BookingSummary;