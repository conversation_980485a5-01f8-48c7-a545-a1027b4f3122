import React from 'react';

const Services = () => {
  const services = [
    {
      title: "Event Planning",
      description: "Complete event planning and coordination services",
      features: ["Venue Selection", "Vendor Management", "Timeline Planning"]
    },
    {
      title: "Decoration",
      description: "Custom decoration and theme setup",
      features: ["Theme Design", "Floral Arrangements", "Lighting Setup"]
    },
    {
      title: "Catering",
      description: "Professional catering services",
      features: ["Custom Menus", "Dietary Options", "Professional Service"]
    }
  ];

  return (
    <div className="py-8">
      <h2 className="text-3xl font-bold text-light-primary dark:text-white mb-6">
        Our Services
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {services.map((service, index) => (
          <div 
            key={index}
            className="bg-light-secondary dark:bg-dark-secondary rounded-lg shadow-md p-6"
          >
            <h3 className="text-xl font-semibold text-light-primary dark:text-white mb-3">
              {service.title}
            </h3>
            <p className="text-light-secondary dark:text-gray-400 mb-4">
              {service.description}
            </p>
            <ul className="space-y-2">
              {service.features.map((feature, idx) => (
                <li 
                  key={idx}
                  className="text-light-secondary dark:text-gray-400 flex items-center"
                >
                  <span className="w-2 h-2 bg-[#ff5a5f] rounded-full mr-2"></span>
                  {feature}
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Services;