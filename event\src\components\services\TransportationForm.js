import React, { useState, useEffect, useCallback } from 'react';
import { Check, Settings, Sparkles } from 'lucide-react';

const TransportationForm = ({ onSubmit, previousData, validateRef, useContinueButton = false }) => {
  const [formData, setFormData] = useState({
    vehicleTypes: [],
    numberOfVehicles: '',
    duration: '',
    pickupLocation: '',
    dropLocation: '',
    decorationRequired: '',
    specialRequests: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [showSuccess, setShowSuccess] = useState(false);

  // Initialize form data with previous data if available
  useEffect(() => {
    if (previousData) {
      setFormData(prevData => ({
        ...prevData,
        ...previousData
      }));
    }
  }, [previousData]);

  // Validate form and submit silently (for continue button)
  // Using useCallback to memoize the function and avoid circular dependencies
  const validateAndSubmitSilent = useCallback(() => {
    const errors = {};

    // Check required fields
    if (!formData.vehicleTypes || formData.vehicleTypes.length === 0) {
      errors.vehicleTypes = 'Please select at least one vehicle type';
    }
    if (!formData.numberOfVehicles) errors.numberOfVehicles = 'Please enter the number of vehicles';
    if (!formData.duration) errors.duration = 'Please select a duration';

    if (Object.keys(errors).length === 0) {
      // Form is valid, submit
      onSubmit({
        serviceDetails: formData,
        category: 'Transportation',
        name: 'Transportation Service'
      });

      // Return true to indicate validation passed
      return true;
    } else {
      // Form has errors
      setFormErrors(errors);

      // Return false to indicate validation failed
      return false;
    }
  }, [formData, onSubmit, setFormErrors]);

  // Register the validation function with the parent component
  useEffect(() => {
    if (validateRef && useContinueButton) {
      validateRef(() => {
        // Return the result of validateAndSubmit
        return validateAndSubmitSilent();
      });
    }
  }, [validateRef, useContinueButton, validateAndSubmitSilent]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (type === 'checkbox') {
      if (checked) {
        setFormData({
          ...formData,
          [name]: [...(formData[name] || []), value]
        });
      } else {
        setFormData({
          ...formData,
          [name]: (formData[name] || []).filter(item => item !== value)
        });
      }
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };



  // Validate form and submit with visual feedback
  const validateAndSubmit = () => {
    const isValid = validateAndSubmitSilent();

    if (isValid) {
      // Show success message
      setShowSuccess(true);

      // Hide success message after 3 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }

    return isValid;
  };

  return (
    <div className="bg-white dark:bg-dark-secondary p-6 rounded-lg shadow-md">
      {showSuccess && (
        <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg text-green-700 dark:text-green-300 flex items-center">
          <Check className="mr-2" size={18} />
          Transportation details saved successfully!
        </div>
      )}

      <p className="mb-6 text-light-secondary dark:text-gray-400 flex items-center">
        <Settings className="mr-2 text-[#ff5a5f]" size={16} />
        Please provide details for your transportation requirements
      </p>

      <div className="space-y-6">
        {/* Vehicle Types */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Vehicle Types <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {["Sedan", "SUV", "Luxury Car", "Vintage Car", "Mini Bus", "Coach Bus", "Decorated Vehicle"].map((option, i) => (
              <div key={i} className="flex items-start">
                <input
                  type="checkbox"
                  id={`vehicleTypes-${i}`}
                  name="vehicleTypes"
                  value={option}
                  checked={(formData.vehicleTypes || []).includes(option)}
                  onChange={handleInputChange}
                  className="h-4 w-4 mt-1 text-[#ff5a5f] focus:ring-[#ff5a5f] border-gray-300 rounded"
                />
                <label htmlFor={`vehicleTypes-${i}`} className="ml-2 block text-sm text-light-primary dark:text-white">
                  {option}
                </label>
              </div>
            ))}
          </div>
          {formErrors.vehicleTypes && (
            <p className="mt-1 text-sm text-red-500">{formErrors.vehicleTypes}</p>
          )}
        </div>

        {/* Number of Vehicles */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Number of Vehicles <span className="text-[#ff5a5f]">*</span>
          </label>
          <input
            type="number"
            name="numberOfVehicles"
            value={formData.numberOfVehicles || ''}
            onChange={handleInputChange}
            placeholder="How many vehicles needed"
            min="1"
            className={`w-full p-3 rounded-lg border ${formErrors.numberOfVehicles ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                     bg-white dark:bg-dark-primary text-light-primary dark:text-white
                     focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent`}
          />
          {formErrors.numberOfVehicles && (
            <p className="mt-1 text-sm text-red-500">{formErrors.numberOfVehicles}</p>
          )}
        </div>

        {/* Duration */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Duration <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="relative">
            <select
              name="duration"
              value={formData.duration || ''}
              onChange={handleInputChange}
              className={`w-full p-3 rounded-lg border ${formErrors.duration ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none`}
            >
              <option value="">Select Duration</option>
              <option value="One-way transfer">One-way transfer</option>
              <option value="Return transfer">Return transfer</option>
              <option value="4-6 hours">4-6 hours</option>
              <option value="Full day (8-10 hours)">Full day (8-10 hours)</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
            {formErrors.duration && (
              <p className="mt-1 text-sm text-red-500">{formErrors.duration}</p>
            )}
          </div>
        </div>

        {/* Pickup Location */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Pickup Location
          </label>
          <input
            type="text"
            name="pickupLocation"
            value={formData.pickupLocation || ''}
            onChange={handleInputChange}
            placeholder="Enter pickup location"
            className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                     bg-white dark:bg-dark-primary text-light-primary dark:text-white
                     focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent"
          />
        </div>

        {/* Drop Location */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Drop Location
          </label>
          <input
            type="text"
            name="dropLocation"
            value={formData.dropLocation || ''}
            onChange={handleInputChange}
            placeholder="Enter drop location"
            className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                     bg-white dark:bg-dark-primary text-light-primary dark:text-white
                     focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent"
          />
        </div>

        {/* Decoration Required */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Decoration Required
          </label>
          <div className="relative">
            <select
              name="decorationRequired"
              value={formData.decorationRequired || ''}
              onChange={handleInputChange}
              className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none"
            >
              <option value="">Select Option</option>
              <option value="Yes, for all vehicles">Yes, for all vehicles</option>
              <option value="Yes, only for main vehicle">Yes, only for main vehicle</option>
              <option value="No decoration needed">No decoration needed</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
          </div>
        </div>

        {/* Special Requests */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Special Requests
          </label>
          <textarea
            name="specialRequests"
            value={formData.specialRequests || ''}
            onChange={handleInputChange}
            placeholder="Any specific transportation requirements..."
            rows="3"
            className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                     bg-white dark:bg-dark-primary text-light-primary dark:text-white
                     focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent"
          />
        </div>
      </div>

      {/* Only show the save button if not using the continue button */}
      {!useContinueButton && (
        <div className="mt-8 flex justify-end">
          <button
            type="button"
            onClick={validateAndSubmit}
            className="px-6 py-3 rounded-lg bg-gradient-to-r from-[#ff5a5f] to-[#ff8c8f] text-white font-medium hover:shadow-md transition-all duration-300 flex items-center"
          >
            {previousData ? (
              <>
                <Check className="mr-2" size={18} />
                Update Transportation Details
              </>
            ) : (
              <>
                <Sparkles className="mr-2" size={18} />
                Save Transportation Details
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default TransportationForm;
