import React from 'react';
import { View, StyleSheet, ViewStyle, useColorScheme } from 'react-native';
import Colors from '@/constants/Colors';

interface CardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  elevation?: 'none' | 'low' | 'medium' | 'high';
  padding?: 'none' | 'small' | 'medium' | 'large';
  borderRadius?: 'none' | 'small' | 'medium' | 'large';
}

export default function Card({
  children,
  style,
  elevation = 'medium',
  padding = 'medium',
  borderRadius = 'medium',
}: CardProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const colors = Colors[colorScheme];

  const getElevationStyle = () => {
    const isDark = colorScheme === 'dark';
    
    switch (elevation) {
      case 'none':
        return {};
      case 'low':
        return isDark 
          ? { shadowOpacity: 0.2, shadowRadius: 2, elevation: 2 } 
          : { shadowOpacity: 0.1, shadowRadius: 2, elevation: 2 };
      case 'high':
        return isDark 
          ? { shadowOpacity: 0.4, shadowRadius: 6, elevation: 10 }
          : { shadowOpacity: 0.2, shadowRadius: 10, elevation: 10 };
      default: // medium
        return isDark 
          ? { shadowOpacity: 0.3, shadowRadius: 4, elevation: 5 }
          : { shadowOpacity: 0.15, shadowRadius: 5, elevation: 5 };
    }
  };

  const getPaddingStyle = () => {
    switch (padding) {
      case 'none':
        return { padding: 0 };
      case 'small':
        return { padding: 8 };
      case 'large':
        return { padding: 24 };
      default: // medium
        return { padding: 16 };
    }
  };

  const getBorderRadiusStyle = () => {
    switch (borderRadius) {
      case 'none':
        return { borderRadius: 0 };
      case 'small':
        return { borderRadius: 8 };
      case 'large':
        return { borderRadius: 16 };
      default: // medium
        return { borderRadius: 12 };
    }
  };

  return (
    <View
      style={[
        styles.card,
        getElevationStyle(),
        getPaddingStyle(),
        getBorderRadiusStyle(),
        { backgroundColor: colors.card },
        style,
      ]}
    >
      {children}
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
  },
});