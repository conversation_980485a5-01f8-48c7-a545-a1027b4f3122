import { initializeApp } from '@firebase/app';
import { getAuth, initializeAuth, getReactNativePersistence, GoogleAuthProvider, OAuthProvider } from '@firebase/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyBZwpGOJ9YeqKHfKYLOGufMc559J3DIavc",
  authDomain: "easemyevent-27729.firebaseapp.com",
  projectId: "easemyevent-27729",
  storageBucket: "easemyevent-27729.appspot.com",
  messagingSenderId: "223110254349",
  appId: "1:223110254349:web:887f56f3321af59ee12a44",
  measurementId: "G-Q6F9J3YWLD"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Auth with AsyncStorage persistence
const auth = initializeAuth(app, {
  persistence: getReactNativePersistence(AsyncStorage)
});

// Initialize providers
const googleProvider = new GoogleAuthProvider();
const appleProvider = new OAuthProvider('apple.com');

export { auth, googleProvider, appleProvider }; 