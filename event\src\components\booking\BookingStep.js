import React from 'react';
import ServiceForms from '../services';
import LightingForm from '../services/LightingForm';
import MusicForm from '../services/MusicForm';
import TransportationForm from '../services/TransportationForm';
import GiftsForm from '../services/GiftsForm';

const BookingStep = ({ step, selectedOption, onOptionSelect, previousSelections, validateRef }) => {
  // Log step information for debugging
  console.log("Step:", step);
  console.log("Available service forms:", Object.keys(ServiceForms));
  console.log("ServiceForms object:", ServiceForms);
  console.log("LightingForm directly:", ServiceForms.Lighting);

  // Render the appropriate service form based on the category
  const renderServiceForm = () => {
    // Create a mapping of category names to their respective form components
    const categoryToFormMap = {
      'lighting': LightingForm,
      'music': MusicForm,
      'transportation': TransportationForm,
      'gifts': GiftsForm
    };

    // Get the category name in lowercase for case-insensitive matching
    const categoryLower = step.category.toLowerCase();

    // Check if we have a direct import for this category
    if (categoryToFormMap[categoryLower]) {
      const DirectForm = categoryToFormMap[categoryLower];
      console.log(`Using direct ${step.category} form import`);
      return (
        <DirectForm
          onSubmit={onOptionSelect}
          previousData={previousSelections[step.category]?.serviceDetails}
          validateRef={validateRef}
          useContinueButton={true}
        />
      );
    }

    // Try to get the service form with exact case match first
    let ServiceForm = ServiceForms[step.category];
    console.log(`Trying to get ServiceForm for "${step.category}"`, ServiceForm);

    // If not found, try case-insensitive match
    if (!ServiceForm) {
      const categoryLower = step.category.toLowerCase();
      ServiceForm = ServiceForms[categoryLower];
      console.log("Trying lowercase category:", categoryLower, ServiceForm ? "Found" : "Not found");

      // Try all possible case variations
      const allKeys = Object.keys(ServiceForms);
      console.log("Checking all keys for a match:");
      allKeys.forEach(key => {
        if (key.toLowerCase() === categoryLower) {
          console.log(`Found match with key "${key}"`);
          ServiceForm = ServiceForms[key];
        }
      });
    }

    console.log("Final ServiceForm for category:", step.category, ServiceForm ? "Found" : "Not found");

    if (ServiceForm) {
      console.log("Rendering ServiceForm component:", ServiceForm);
      return (
        <ServiceForm
          onSubmit={onOptionSelect}
          previousData={previousSelections[step.category]?.serviceDetails}
          validateRef={validateRef}
          useContinueButton={true}
        />
      );
    }

    return (
      <div className="bg-white dark:bg-dark-secondary p-6 rounded-lg shadow-md">
        <p className="text-center text-light-secondary dark:text-gray-400 py-8">
          No form available for {step.category}. Please contact support.
        </p>
        <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg text-red-700 dark:text-red-300">
          <p><strong>Debug info:</strong></p>
          <p>Step category: {step.category}</p>
          <p>Available forms: {Object.keys(ServiceForms).join(', ')}</p>
          <p>Is LightingForm imported directly: {LightingForm ? "Yes" : "No"}</p>
          <p>Is MusicForm imported directly: {MusicForm ? "Yes" : "No"}</p>
          <p>Is TransportationForm imported directly: {TransportationForm ? "Yes" : "No"}</p>
          <p>Is GiftsForm imported directly: {GiftsForm ? "Yes" : "No"}</p>
          <p>Is Lighting in ServiceForms: {ServiceForms.Lighting ? "Yes" : "No"}</p>
          <p>Is lighting (lowercase) in ServiceForms: {ServiceForms.lighting ? "Yes" : "No"}</p>
        </div>
      </div>
    );
  };

  // Render service-specific input form
  if (step.type === "service-input") {
    console.log("Rendering service input form for category:", step.category);
    console.log("Step object:", step);

    // No special cases needed here anymore, all handled in renderServiceForm

    return (
      <div className="space-y-6">
        <h3 className="text-2xl font-bold text-light-primary dark:text-white flex items-center">
          <span className="mr-2 text-[#ff5a5f]">
            {step.category === "Decoration" && <span>🎨</span>}
            {step.category === "Catering" && <span>🍽️</span>}
            {step.category === "Priest" && <span>🙏</span>}
            {step.category === "Photography" && <span>📸</span>}
            {step.category === "Videography" && <span>🎥</span>}
            {step.category === "Music" && <span>🎵</span>}
            {step.category === "Transportation" && <span>🚗</span>}
            {step.category === "Invitation" && <span>💌</span>}
            {step.category === "Gifts" && <span>🎁</span>}
            {step.category === "Lighting" && <span>💡</span>}
          </span>
          {step.title}
        </h3>

        {renderServiceForm()}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold text-light-primary dark:text-white flex items-center">
        <span className="mr-2 text-[#ff5a5f]">
          {step.category === "Decoration" && <span>🎨</span>}
          {step.category === "Catering" && <span>🍽️</span>}
          {step.category === "Priest" && <span>🙏</span>}
          {step.category === "Photography" && <span>📸</span>}
          {step.category === "Videography" && <span>🎥</span>}
          {step.category === "Music" && <span>🎵</span>}
          {step.category === "Transportation" && <span>🚗</span>}
          {step.category === "Invitation" && <span>💌</span>}
          {step.category === "Gifts" && <span>🎁</span>}
          {step.category === "Lighting" && <span>💡</span>}
        </span>
        {step.title}
      </h3>

      <div className="bg-white dark:bg-dark-secondary p-6 rounded-lg shadow-md">
        <p className="text-center text-light-secondary dark:text-gray-400 py-8">
          This component type is not supported. Please contact support.
        </p>
      </div>
    </div>
  );
};

export default BookingStep;
