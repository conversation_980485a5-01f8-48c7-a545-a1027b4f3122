import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';

const templates = [
  { name: 'Wedding', description: 'Elegant and customizable design' },
  { name: 'Birthday', description: 'Elegant and customizable design' },
  { name: 'Baby Shower', description: 'Elegant and customizable design' },
];

export default function EInvitesPage() {
  return (
    <ScrollView style={styles.container}>
      <Text style={styles.header}>Digital Invitations</Text>
      <Text style={styles.subheader}>Create beautiful digital invitations for your events</Text>
      <View style={styles.grid}>
        {templates.map((template, idx) => (
          <View key={idx} style={styles.card}>
            <Text style={styles.title}>{template.name} Template</Text>
            <Text style={styles.description}>{template.description}</Text>
            <TouchableOpacity style={styles.button}>
              <Text style={styles.buttonText}>Use Template</Text>
            </TouchableOpacity>
          </View>
        ))}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subheader: {
    fontSize: 16,
    color: '#555',
    marginBottom: 16,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  card: {
    width: '48%',
    backgroundColor: '#f7f7f7',
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 6,
  },
  description: {
    fontSize: 15,
    color: '#555',
    marginBottom: 12,
  },
  button: {
    backgroundColor: '#ff5a5f',
    paddingVertical: 10,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
}); 