import { motion } from "framer-motion";
import { Calendar, Clock, DollarSign, UserX } from "lucide-react";

const ProblemStatement = () => {
  const problems = [
    {
      icon: <Clock className="w-10 h-10 text-light-secondary dark:text-dark-secondary" />,
      title: "Time-Consuming",
      description: "Traditional event planning requires weeks of research, multiple phone calls, and endless negotiations."
    },
    {
      icon: <DollarSign className="w-10 h-10 text-light-secondary dark:text-dark-secondary" />,
      title: "Expensive",
      description: "Middlemen and event planners add substantial markups, increasing the overall cost of your event."
    },
    {
      icon: <UserX className="w-10 h-10 text-light-secondary dark:text-dark-secondary" />,
      title: "Stressful Coordination",
      description: "Coordinating multiple vendors independently creates confusion and miscommunication."
    },
    {
      icon: <Calendar className="w-10 h-10 text-light-secondary dark:text-dark-secondary" />,
      title: "Limited Transparency",
      description: "Lack of clear pricing and availability information makes comparison difficult."
    }
  ];

  return (
    <section id="problem" className="py-20 bg-white relative overflow-hidden">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 font-display">
            The <span className="gradient-text-solid-end">Problem</span> We're Solving
          </h2>
          <p className="text-light-secondary dark:text-dark-secondary max-w-2xl mx-auto text-lg">
            Event planning shouldn't be a full-time job. Here's why the traditional process is broken:
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="relative overflow-hidden rounded-xl shadow-lg">
            <img
              src="https://images.unsplash.com/photo-1556761175-5973dc0f32e7?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&q=80&w=600"
              alt="Stressed event planner"
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex flex-col justify-end p-6 text-white">
              <h3 className="text-white font-semibold mb-2 font-display">The Old Way</h3>
              <p className="text-white/80">Stress, confusion, and wasted time</p>
            </div>
          </div>

          <div className="bg-light-primary dark:bg-dark-primary p-8 rounded-xl shadow-lg">
            <h3 className="text-2xl font-bold mb-6 text-center font-display">Common Pain Points</h3>

            <div className="grid grid-cols-1 gap-6">
              {problems.map((problem, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex gap-4 items-start"
                >
                  <div className="p-3 bg-light-secondary dark:bg-dark-secondary rounded-full">
                    {problem.icon}
                  </div>
                  <div>
                    <h4 className="text-xl font-semibold mb-1 font-display">{problem.title}</h4>
                    <p className="text-light-secondary dark:text-dark-secondary">{problem.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        <div className="text-center mt-16">
          <h3 className="text-2xl font-bold mb-3 font-display">
            Our <span className="gradient-text-solid-end">Solution</span>
          </h3>
          <p className="text-light-secondary dark:text-dark-secondary max-w-3xl mx-auto text-lg">
            EaseMyEvent connects you directly with pre-vetted vendors, eliminating middlemen,
            saving you time and money, and making event planning a joyful experience rather than a burden.
          </p>
        </div>
      </div>
    </section>
  );
};

export default ProblemStatement;
