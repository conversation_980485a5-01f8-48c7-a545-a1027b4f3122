import React from 'react';
import PageWrapper from '../layout/PageWrapper';

const Settings = () => (
  <PageWrapper title="Settings">
    <div className="max-w-2xl mx-auto space-y-6">
      <div className="bg-light-secondary dark:bg-dark-secondary p-6 rounded-lg shadow-md">
        <h3 className="text-xl font-semibold text-light-primary dark:text-white mb-4">
          Account Settings
        </h3>
        <div className="space-y-4">
          <div>
            <label className="block text-light-primary dark:text-white mb-2">Email Notifications</label>
            <label className="flex items-center">
              <input type="checkbox" className="form-checkbox" />
              <span className="ml-2 text-light-secondary dark:text-gray-400">
                Receive booking updates
              </span>
            </label>
          </div>
          <div>
            <label className="block text-light-primary dark:text-white mb-2">Language</label>
            <select className="w-full p-2 rounded border bg-light-primary dark:bg-dark-primary">
              <option>English</option>
              <option>Spanish</option>
              <option>French</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  </PageWrapper>
);

export default Settings;