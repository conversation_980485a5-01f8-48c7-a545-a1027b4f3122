/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./public/index.html"
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: '#ff5a5f',
        secondary: '#00a699',
        accent: '#fc642d',
        success: '#3fb27f',
        warning: '#ffb400',
        info: '#007bff',
        muted: '#767676',
        'brand': {
          DEFAULT: '#FF5A5F',
          'light': '#FF7A7F',
          'dark': '#E0484E'
        },
        'warm': {
          'orange': '#FF8C00',
          'coral': '#FF6347',
          'pink': '#FF4D8D'
        },
        'celebrate': {
          'cream': '#FFF8E1',
          'gold': '#FFD700',
          'orange': '#FF5A5F',
          'red': '#E53935',
          'purple': '#7B1FA2',
          'blue': '#1976D2',
          'teal': '#00897B'
        },
        'gradient': {
          'start': '#FF9D6C',
          'mid': '#FF5A5F',
          'end': '#FF3D71',
          'light': '#FFF8E1',
          'dark': '#FF3D71'
        }
      },
      backgroundColor: {
        'dark-primary': '#1a1a1a',
        'light-primary': '#ffffff',
        'dark-secondary': '#2a2a2a',
        'light-secondary': '#f3f4f6',
      },
      textColor: {
        'dark-primary': '#ffffff',
        'light-primary': '#000000',
        'dark-secondary': '#a1a1aa',
        'light-secondary': '#4b5563',
      },
      boxShadow: {
        'custom': '0 4px 20px rgba(0, 0, 0, 0.08)',
        'hover': '0 8px 30px rgba(0, 0, 0, 0.12)',
      },
      borderRadius: {
        'xl': '1rem',
        '2xl': '1.5rem',
      },
      animation: {
        'bounce-slow': 'bounce 3s infinite',
        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'fade-in': 'fadeIn 0.5s ease-in forwards',
        'fade-in-right': 'fadeInRight 0.5s ease-out forwards',
        'fade-in-left': 'fadeInLeft 0.5s ease-out forwards'
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        fadeInRight: {
          '0%': { opacity: '0', transform: 'translateX(-20px)' },
          '100%': { opacity: '1', transform: 'translateX(0)' }
        },
        fadeInLeft: {
          '0%': { opacity: '0', transform: 'translateX(20px)' },
          '100%': { opacity: '1', transform: 'translateX(0)' }
        }
      },
      backgroundImage: {
        'heading-gradient': 'linear-gradient(90deg, #FF5A5F 0%, #FF7A7F 100%)',
        'purple-gradient': 'linear-gradient(90deg, #8B5CF6 0%, #6366F1 100%)',
        'warm-gradient': 'linear-gradient(90deg, #FF8C00 0%, #FF6347 50%, #FF4D8D 100%)'
      },
      fontFamily: {
        'sans': ['Inter', 'Roboto', 'system-ui', 'sans-serif'],
        'display': ['Poppins', 'system-ui', 'sans-serif']
      },
      transitionProperty: {
        'height': 'height',
        'spacing': 'margin, padding'
      },
      gradientColorStops: theme => ({
        ...theme('colors'),
      }),
    },
  },
  plugins: [],
}
