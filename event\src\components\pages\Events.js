import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

const Events = () => {
  const [currentImageIndex, setCurrentImageIndex] = useState({});
  const [isPaused, setIsPaused] = useState({});
  const timersRef = useRef({});

  const eventTypes = [
    {
      title: "House Warming",
      description: "Traditional house warming ceremony planning",
      path: "/booking/house-warming",
      images: [
        "https://images.unsplash.com/photo-1600585154340-be6161a56a0c",
        "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c",
        "https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea"
      ]
    },
    {
      title: "Weddings",
      description: "Create your perfect wedding day",
      path: "/booking/wedding",
      images: [
        "https://images.unsplash.com/photo-1519741497674-611481863552",
        "https://images.unsplash.com/photo-1511795409834-ef04bbd61622",
        "https://images.unsplash.com/photo-1511285560929-80b456fea0bc"
      ]
    },
    {
      title: "Corporate Events",
      description: "Professional business event planning",
      path: "/booking/corporate",
      images: [
        "https://images.unsplash.com/photo-1540575467063-178a50c2df87",
        "https://images.unsplash.com/photo-1540575467063-178a50c2df87",
        "https://images.unsplash.com/photo-1540575467063-178a50c2df87"
      ]
    },
    {
      title: "Birthday Parties",
      description: "Memorable birthday celebrations",
      path: "/booking/birthday",
      images: [
        "https://images.unsplash.com/photo-1530103862676-de8c9debad1d",
        "https://images.unsplash.com/photo-1530103862676-de8c9debad1d",
        "https://images.unsplash.com/photo-1530103862676-de8c9debad1d"
      ]
    },
    {
      title: "Conferences",
      description: "Large-scale conference organization",
      path: "/booking/conference",
      images: [
        "https://images.unsplash.com/photo-1540575467063-178a50c2df87",
        "https://images.unsplash.com/photo-1540575467063-178a50c2df87",
        "https://images.unsplash.com/photo-1540575467063-178a50c2df87"
      ]
    }
  ];

  useEffect(() => {
    // Cleanup timers on component unmount
    const currentTimers = timersRef.current;
    return () => {
      Object.values(currentTimers).forEach(timer => clearInterval(timer));
    };
  }, []);

  const startAutoRotation = (eventIndex) => {
    // Clear existing timer if any
    if (timersRef.current[eventIndex]) {
      clearInterval(timersRef.current[eventIndex]);
    }

    // Start new timer
    timersRef.current[eventIndex] = setInterval(() => {
      if (!isPaused[eventIndex]) {
        handleNextImage(eventIndex);
      }
    }, 1500); // Rotate every 1.5 seconds
  };

  const stopAutoRotation = (eventIndex) => {
    if (timersRef.current[eventIndex]) {
      clearInterval(timersRef.current[eventIndex]);
      timersRef.current[eventIndex] = null;
    }
  };

  const handlePrevImage = (eventIndex) => {
    setIsPaused(prev => ({ ...prev, [eventIndex]: true }));
    setCurrentImageIndex(prev => ({
      ...prev,
      [eventIndex]: ((prev[eventIndex] || 0) - 1 + eventTypes[eventIndex].images.length) % eventTypes[eventIndex].images.length
    }));
    // Resume auto-rotation after 5 seconds of inactivity
    setTimeout(() => {
      setIsPaused(prev => ({ ...prev, [eventIndex]: false }));
    }, 5000);
  };

  const handleNextImage = (eventIndex) => {
    setIsPaused(prev => ({ ...prev, [eventIndex]: true }));
    setCurrentImageIndex(prev => ({
      ...prev,
      [eventIndex]: ((prev[eventIndex] || 0) + 1) % eventTypes[eventIndex].images.length
    }));
    // Resume auto-rotation after 5 seconds of inactivity
    setTimeout(() => {
      setIsPaused(prev => ({ ...prev, [eventIndex]: false }));
    }, 5000);
  };

  return (
    <div className="py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl md:text-4xl font-bold mb-4 font-display">
          Browse <span className="gradient-text-solid-end">Events</span>
        </h2>
        <p className="text-light-secondary dark:text-gray-400 max-w-2xl text-lg mb-8">
          Discover and book your perfect event with our curated selection of experiences
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {eventTypes.map((event, index) => (
            <motion.div 
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-dark-secondary rounded-xl shadow-custom hover:shadow-hover transition-all duration-300 overflow-hidden hover:-translate-y-1"
              onMouseEnter={() => startAutoRotation(index)}
              onMouseLeave={() => stopAutoRotation(index)}
            >
              <div className="p-6">
                {/* Single Image with Navigation */}
                <div className="relative mb-4">
                  <div className="w-full h-48 rounded-lg overflow-hidden relative">
                    <img
                      src={event.images[currentImageIndex[index] || 0]}
                      alt={`${event.title}`}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 flex items-center justify-between px-2">
                      <button
                        onClick={() => handlePrevImage(index)}
                        className="bg-black/50 text-white p-2 rounded-full hover:bg-black/75 transition"
                      >
                        ←
                      </button>
                      <button
                        onClick={() => handleNextImage(index)}
                        className="bg-black/50 text-white p-2 rounded-full hover:bg-black/75 transition"
                      >
                        →
                      </button>
                    </div>
                  </div>
                  <div className="flex justify-center mt-2 gap-1">
                    {event.images.map((_, imgIndex) => (
                      <div
                        key={imgIndex}
                        className={`w-2 h-2 rounded-full ${
                          (currentImageIndex[index] || 0) === imgIndex
                            ? 'bg-primary'
                            : 'bg-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                </div>

                <h3 className="text-xl font-semibold text-light-primary dark:text-white mb-2 font-display">
                  {event.title}
                </h3>
                <p className="text-light-secondary dark:text-gray-400 mb-4">
                  {event.description}
                </p>

                <Link
                  to={event.path}
                  className="btn btn-primary w-full text-center"
                >
                  Book Now
                </Link>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Events;
