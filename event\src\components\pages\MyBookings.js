import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import PageWrapper from '../layout/PageWrapper';

const MyBookings = () => {
  const [bookings, setBookings] = useState([]);
  const location = useLocation();
  const [successMessage, setSuccessMessage] = useState('');

  useEffect(() => {
    // Load bookings from localStorage
    const savedBookings = JSON.parse(localStorage.getItem('bookings') || '[]');
    setBookings(savedBookings);

    // Show success message if booking was just created
    if (location.state?.bookingSuccess) {
      setSuccessMessage('Booking confirmed successfully!');
      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(''), 3000);
    }
  }, [location]);

  if (bookings.length === 0) {
    return (
      <PageWrapper title="My Bookings">
        <div className="text-center py-8">
          <p className="text-gray-500">No bookings found.</p>
        </div>
      </PageWrapper>
    );
  }

  return (
    <PageWrapper title="My Bookings">
      {successMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6">
          {successMessage}
        </div>
      )}
      <div className="space-y-6">
        {bookings.map((booking) => (
          <div
            key={booking.id}
            className="bg-light-secondary dark:bg-dark-secondary p-6 rounded-lg shadow-md"
          >
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-xl font-semibold text-light-primary dark:text-white">
                  {booking.eventType}
                </h3>
                <p className="text-light-secondary dark:text-gray-400 mt-1">
                  Guests: {booking.numberOfPersons || 'Not specified'}
                </p>
                <p className="text-light-secondary dark:text-gray-400 mt-1">
                  Date: {booking.date} at {booking.time}
                </p>
                <p className="text-light-secondary dark:text-gray-400 mt-1">
                  Total Amount: ₹{booking.totalAmount.toLocaleString()}
                </p>
              </div>
              <span className={`px-3 py-1 rounded-full text-sm ${
                booking.status === 'Confirmed'
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
              }`}>
                {booking.status}
              </span>
            </div>
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <h4 className="font-semibold mb-2">Booking Details:</h4>
              <ul className="text-sm text-light-secondary dark:text-gray-400">
                {Object.entries(booking.details).map(([key, value]) => {
                  // Skip basicDetails as it's shown above
                  if (key === 'basicDetails') return null;

                  // Format the service name
                  const serviceName = key.charAt(0).toUpperCase() + key.slice(1);

                  // Check if the service has serviceDetails
                  if (value.serviceDetails) {
                    // Get a summary of the service details
                    const detailsSummary = Object.entries(value.serviceDetails)
                      .filter(([_, val]) => val && (typeof val === 'string' || (Array.isArray(val) && val.length > 0)))
                      .slice(0, 2) // Take only first 2 details for summary
                      .map(([detailKey, detailVal]) => {
                        const formattedKey = detailKey
                          .replace(/([A-Z])/g, ' $1')
                          .replace(/^./, str => str.toUpperCase());

                        return `${formattedKey}: ${Array.isArray(detailVal) ? detailVal.join(', ') : detailVal}`;
                      })
                      .join(', ');

                    return (
                      <li key={key}>
                        <span className="font-medium">{serviceName}:</span> {detailsSummary}
                      </li>
                    );
                  }

                  // For backward compatibility with old booking format
                  return (
                    <li key={key}>
                      <span className="font-medium">{serviceName}:</span> {value.name || JSON.stringify(value)}
                    </li>
                  );
                })}
              </ul>
            </div>
          </div>
        ))}
      </div>
    </PageWrapper>
  );
};

export default MyBookings;
