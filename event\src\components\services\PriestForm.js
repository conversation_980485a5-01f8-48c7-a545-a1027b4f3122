import React, { useState, useEffect } from 'react';
import { Check, Setting<PERSON>, Sparkles } from 'lucide-react';

const PriestForm = ({ onSubmit, previousData }) => {
  const [formData, setFormData] = useState({
    ceremonyType: '',
    language: '',
    duration: '',
    poojaItems: '',
    additionalRituals: [],
    specialRequests: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [showSuccess, setShowSuccess] = useState(false);

  // Initialize form data with previous data if available
  useEffect(() => {
    if (previousData) {
      setFormData(prevData => ({
        ...prevData,
        ...previousData
      }));
    }
  }, [previousData]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (type === 'checkbox') {
      if (checked) {
        setFormData({
          ...formData,
          [name]: [...(formData[name] || []), value]
        });
      } else {
        setFormData({
          ...formData,
          [name]: (formData[name] || []).filter(item => item !== value)
        });
      }
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // Validate form and submit
  const validateAndSubmit = () => {
    const errors = {};

    // Check required fields
    if (!formData.ceremonyType) errors.ceremonyType = 'Please select a ceremony type';
    if (!formData.language) errors.language = 'Please select a preferred language';
    if (!formData.duration) errors.duration = 'Please select an expected duration';
    if (!formData.poojaItems) errors.poojaItems = 'Please select a pooja items option';

    if (Object.keys(errors).length === 0) {
      // Form is valid, submit
      onSubmit({
        serviceDetails: formData,
        category: 'Priest',
        name: 'Priest Service'
      });
      
      // Show success message
      setShowSuccess(true);
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    } else {
      // Form has errors
      setFormErrors(errors);
    }
  };

  return (
    <div className="bg-white dark:bg-dark-secondary p-6 rounded-lg shadow-md">
      {showSuccess && (
        <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg text-green-700 dark:text-green-300 flex items-center">
          <Check className="mr-2" size={18} />
          Priest service details saved successfully!
        </div>
      )}
      
      <p className="mb-6 text-light-secondary dark:text-gray-400 flex items-center">
        <Settings className="mr-2 text-[#ff5a5f]" size={16} />
        Please provide details for your priest service requirements
      </p>

      <div className="space-y-6">
        {/* Ceremony Type */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Ceremony Type <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="relative">
            <select
              name="ceremonyType"
              value={formData.ceremonyType || ''}
              onChange={handleInputChange}
              className={`w-full p-3 rounded-lg border ${formErrors.ceremonyType ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none`}
            >
              <option value="">Select Ceremony Type</option>
              <option value="Griha Pravesh (House Warming)">Griha Pravesh (House Warming)</option>
              <option value="Satyanarayan Puja">Satyanarayan Puja</option>
              <option value="Ganesh Puja">Ganesh Puja</option>
              <option value="Lakshmi Puja">Lakshmi Puja</option>
              <option value="Navgraha Shanti">Navgraha Shanti</option>
              <option value="Vastu Shanti">Vastu Shanti</option>
              <option value="Wedding Ceremony">Wedding Ceremony</option>
              <option value="Baby Naming Ceremony">Baby Naming Ceremony</option>
              <option value="Other">Other</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
            {formErrors.ceremonyType && (
              <p className="mt-1 text-sm text-red-500">{formErrors.ceremonyType}</p>
            )}
          </div>
        </div>

        {/* Preferred Language */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Preferred Language <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="relative">
            <select
              name="language"
              value={formData.language || ''}
              onChange={handleInputChange}
              className={`w-full p-3 rounded-lg border ${formErrors.language ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none`}
            >
              <option value="">Select Preferred Language</option>
              <option value="Sanskrit with Hindi explanation">Sanskrit with Hindi explanation</option>
              <option value="Sanskrit with English explanation">Sanskrit with English explanation</option>
              <option value="Hindi">Hindi</option>
              <option value="Tamil">Tamil</option>
              <option value="Telugu">Telugu</option>
              <option value="Kannada">Kannada</option>
              <option value="Malayalam">Malayalam</option>
              <option value="Bengali">Bengali</option>
              <option value="Gujarati">Gujarati</option>
              <option value="Marathi">Marathi</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
            {formErrors.language && (
              <p className="mt-1 text-sm text-red-500">{formErrors.language}</p>
            )}
          </div>
        </div>

        {/* Expected Duration */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Expected Duration <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="relative">
            <select
              name="duration"
              value={formData.duration || ''}
              onChange={handleInputChange}
              className={`w-full p-3 rounded-lg border ${formErrors.duration ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none`}
            >
              <option value="">Select Expected Duration</option>
              <option value="1-2 hours">1-2 hours</option>
              <option value="2-3 hours">2-3 hours</option>
              <option value="3-4 hours">3-4 hours</option>
              <option value="4+ hours">4+ hours</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
            {formErrors.duration && (
              <p className="mt-1 text-sm text-red-500">{formErrors.duration}</p>
            )}
          </div>
        </div>

        {/* Pooja Items */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Pooja Items <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="relative">
            <select
              name="poojaItems"
              value={formData.poojaItems || ''}
              onChange={handleInputChange}
              className={`w-full p-3 rounded-lg border ${formErrors.poojaItems ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none`}
            >
              <option value="">Select Pooja Items Option</option>
              <option value="We will arrange">We will arrange</option>
              <option value="Need complete package">Need complete package</option>
              <option value="Need partial assistance">Need partial assistance</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
            {formErrors.poojaItems && (
              <p className="mt-1 text-sm text-red-500">{formErrors.poojaItems}</p>
            )}
          </div>
        </div>

        {/* Additional Rituals */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Additional Rituals
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {["Havan/Homam", "Kalash Sthapana", "Rudrabhishek", "Kanya/Brahmin Bhojan", "Astrological Consultation", "Pre-ceremony Consultation"].map((option, i) => (
              <div key={i} className="flex items-start">
                <input
                  type="checkbox"
                  id={`additionalRituals-${i}`}
                  name="additionalRituals"
                  value={option}
                  checked={(formData.additionalRituals || []).includes(option)}
                  onChange={handleInputChange}
                  className="h-4 w-4 mt-1 text-[#ff5a5f] focus:ring-[#ff5a5f] border-gray-300 rounded"
                />
                <label htmlFor={`additionalRituals-${i}`} className="ml-2 block text-sm text-light-primary dark:text-white">
                  {option}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Special Requests */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Special Requests
          </label>
          <textarea
            name="specialRequests"
            value={formData.specialRequests || ''}
            onChange={handleInputChange}
            placeholder="Any specific rituals or requirements..."
            rows="3"
            className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                     bg-white dark:bg-dark-primary text-light-primary dark:text-white
                     focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent"
          />
        </div>
      </div>

      <div className="mt-8 flex justify-end">
        <button
          type="button"
          onClick={validateAndSubmit}
          className="px-6 py-3 rounded-lg bg-gradient-to-r from-[#ff5a5f] to-[#ff8c8f] text-white font-medium hover:shadow-md transition-all duration-300 flex items-center"
        >
          {previousData ? (
            <>
              <Check className="mr-2" size={18} />
              Update Priest Details
            </>
          ) : (
            <>
              <Sparkles className="mr-2" size={18} />
              Save Priest Details
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default PriestForm;
