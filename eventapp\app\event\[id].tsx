import React, { useState } from 'react';
import { 
  View, Text, StyleSheet, Image, ScrollView, useColorScheme, 
  SafeAreaView, TouchableOpacity, Dimensions
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import Colors from '@/constants/Colors';
import events from '@/assets/data/events';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { 
  Calendar, Clock, MapPin, Users, Star, Share2, 
  ChevronLeft, Heart, ArrowLeft
} from 'lucide-react-native';

const { width } = Dimensions.get('window');

export default function EventDetailScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const colors = Colors[colorScheme];
  const { id } = useLocalSearchParams<{ id: string }>();
  const [isSaved, setIsSaved] = useState(false);
  
  // Find the event with the matching ID
  const event = events.find(e => e.id === id);
  
  if (!event) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.notFoundContainer}>
          <Text style={[styles.notFoundText, { color: colors.text }]}>Event not found</Text>
          <Button title="Go Back" onPress={() => router.back()} variant="primary" />
        </View>
      </SafeAreaView>
    );
  }

  // Format date string to more readable format
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Toggle save event
  const toggleSave = () => {
    setIsSaved(!isSaved);
  };

  // Book event
  const bookEvent = () => {
    // In a real app, this would navigate to a checkout screen
    router.push('/bookings');
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Event Image and Header */}
        <View style={styles.imageContainer}>
          <Image 
            source={{ uri: event.image }} 
            style={styles.image}
            resizeMode="cover"
          />
          <SafeAreaView style={styles.headerButtons}>
            <TouchableOpacity 
              style={[styles.iconButton, { backgroundColor: colors.card }]}
              onPress={() => router.back()}
            >
              <ArrowLeft size={22} color={colors.text} />
            </TouchableOpacity>
            
            <View style={styles.rightButtons}>
              <TouchableOpacity 
                style={[styles.iconButton, { backgroundColor: colors.card }]}
                onPress={() => {}}
              >
                <Share2 size={22} color={colors.text} />
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.iconButton, { backgroundColor: colors.card }]}
                onPress={toggleSave}
              >
                <Heart 
                  size={22} 
                  color={isSaved ? colors.error : colors.text}
                  fill={isSaved ? colors.error : 'none'}
                />
              </TouchableOpacity>
            </View>
          </SafeAreaView>
        </View>

        {/* Event Details */}
        <View style={styles.contentContainer}>
          <Text style={[styles.category, { color: colors.primary }]}>
            {event.category.charAt(0).toUpperCase() + event.category.slice(1)}
          </Text>
          
          <Text style={[styles.title, { color: colors.text }]}>
            {event.title}
          </Text>
          
          <View style={styles.orgInfo}>
            <Text style={[styles.organizer, { color: colors.darkGray }]}>
              By {event.organizer}
            </Text>
            
            {event.rating && (
              <View style={styles.rating}>
                <Star size={16} color={colors.accent} fill={colors.accent} />
                <Text style={[styles.ratingText, { color: colors.text }]}>
                  {event.rating.toFixed(1)}
                </Text>
              </View>
            )}
          </View>

          <Card style={styles.infoCard}>
            <View style={styles.infoItem}>
              <Calendar size={20} color={colors.primary} />
              <View style={styles.infoTextContainer}>
                <Text style={[styles.infoLabel, { color: colors.darkGray }]}>Date</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {formatDate(event.date)}
                </Text>
              </View>
            </View>
            
            <View style={[styles.divider, { backgroundColor: colors.border }]} />
            
            <View style={styles.infoItem}>
              <Clock size={20} color={colors.primary} />
              <View style={styles.infoTextContainer}>
                <Text style={[styles.infoLabel, { color: colors.darkGray }]}>Time</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {event.time}
                </Text>
              </View>
            </View>
            
            <View style={[styles.divider, { backgroundColor: colors.border }]} />
            
            <View style={styles.infoItem}>
              <MapPin size={20} color={colors.primary} />
              <View style={styles.infoTextContainer}>
                <Text style={[styles.infoLabel, { color: colors.darkGray }]}>Location</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {event.venue}, {event.location}
                </Text>
              </View>
            </View>
            
            {event.attendees && (
              <>
                <View style={[styles.divider, { backgroundColor: colors.border }]} />
                <View style={styles.infoItem}>
                  <Users size={20} color={colors.primary} />
                  <View style={styles.infoTextContainer}>
                    <Text style={[styles.infoLabel, { color: colors.darkGray }]}>Attendees</Text>
                    <Text style={[styles.infoValue, { color: colors.text }]}>
                      {event.attendees.toLocaleString()} people attending
                    </Text>
                  </View>
                </View>
              </>
            )}
          </Card>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              About Event
            </Text>
            <Text style={[styles.description, { color: colors.darkGray }]}>
              {event.description}
            </Text>
          </View>
        </View>
      </ScrollView>

      {/* Booking Bar */}
      <SafeAreaView style={[styles.bookingBar, { backgroundColor: colors.card }]}>
        <View>
          <Text style={[styles.priceLabel, { color: colors.darkGray }]}>Price</Text>
          <Text style={[styles.price, { color: colors.text }]}>${event.price.toFixed(2)}</Text>
        </View>
        
        <Button 
          title="Book Now" 
          onPress={bookEvent} 
          style={styles.bookButton}
        />
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  imageContainer: {
    position: 'relative',
    width: '100%',
    height: 300,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  headerButtons: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  rightButtons: {
    flexDirection: 'row',
  },
  contentContainer: {
    padding: 16,
  },
  category: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    marginBottom: 4,
  },
  title: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    marginBottom: 8,
  },
  orgInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  organizer: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  rating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    marginLeft: 4,
  },
  infoCard: {
    marginBottom: 24,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  infoTextContainer: {
    marginLeft: 12,
  },
  infoLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  divider: {
    height: 1,
    width: '100%',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 12,
  },
  description: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 22,
  },
  bookingBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  priceLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  price: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
  },
  bookButton: {
    width: width * 0.5,
  },
  notFoundContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  notFoundText: {
    fontSize: 18,
    fontFamily: 'Inter-Medium',
    marginBottom: 24,
  }
});