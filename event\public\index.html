<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/event-logo.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#FF5A5F" />
    <meta
      name="description"
      content="Professional event planning and booking services"
    />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <title>Event Booking</title>
    <!-- Start Single Page Apps for GitHub Pages -->
    <script type="text/javascript">
      // Single Page Apps for GitHub Pages
      // MIT License
      // https://github.com/rafgraph/spa-github-pages
      (function(l) {
        if (l.search[1] === '/' ) {
          var decoded = l.search.slice(1).split('&').map(function(s) {
            return s.replace(/~and~/g, '&')
          }).join('?');
          window.history.replaceState(null, null,
              l.pathname.slice(0, -1) + decoded + l.hash
          );
        }
      }(window.location))
    </script>
    <!-- End Single Page Apps for GitHub Pages -->
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>

    <!-- Hidden forms for Netlify Forms detection -->
    <form name="vendor-application" netlify netlify-honeypot="bot-field" hidden>
      <input type="text" name="fullName" />
      <input type="email" name="email" />
      <input type="tel" name="phone" />
      <input type="text" name="businessName" />
      <input type="text" name="location" />
      <select name="serviceType"></select>
      <select name="experience"></select>
      <input type="url" name="website" />
      <textarea name="description"></textarea>
    </form>

    <form name="event-booking" netlify netlify-honeypot="bot-field" hidden>
      <input type="text" name="fullName" />
      <input type="email" name="email" />
      <input type="tel" name="phone" />
      <select name="eventType"></select>
      <input type="date" name="eventDate" />
      <input type="number" name="guestCount" />
      <input type="text" name="location" />
      <textarea name="additionalInfo"></textarea>
    </form>

    <form name="event-booking-confirmation" netlify netlify-honeypot="bot-field" hidden>
      <input type="text" name="eventType" />
      <input type="date" name="eventDate" />
      <input type="time" name="eventTime" />
      <input type="number" name="numberOfPersons" />
      <input type="number" name="totalAmount" />
      <textarea name="bookingDetails"></textarea>
    </form>
  </body>
</html>
