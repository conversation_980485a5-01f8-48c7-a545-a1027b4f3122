import React from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import { ThemeProvider } from "./ThemeContext";

// Layout Components
import Header from './components/layout/Header';
import Footer from './components/layout/Footer';

// Page Components
import Home from './components/pages/Home';
import Events from './components/pages/Events';
import Services from './components/pages/Services';
import Blogs from './components/pages/Blogs';
import EInvites from './components/pages/EInvites';
import Planner from './components/pages/Planner';
import Profile from './components/pages/Profile';
import Settings from './components/pages/Settings';
import MyBookings from './components/pages/MyBookings';
import BookingFlow from './components/pages/BookingFlow';
import PrivacyPolicy from './components/pages/PrivacyPolicy';
import CookiePolicy from './components/pages/CookiePolicy';
import TermsOfService from './components/pages/TermsOfService';

const App = () => (
  <ThemeProvider>
    <Router>
      <div className="min-h-screen flex flex-col bg-light-primary dark:bg-dark-primary font-sans">
        <Header />
        <main className="flex-1 w-full">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/events" element={<Events />} />
            <Route path="/services" element={<Services />} />
            <Route path="/blogs" element={<Blogs />} />
            <Route path="/e-invites" element={<EInvites />} />
            <Route path="/planner" element={<Planner />} />
            <Route path="/profile" element={<Profile />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="/my-bookings" element={<MyBookings />} />
            <Route path="/booking/:eventType" element={<BookingFlow />} />
            <Route path="/privacy-policy" element={<PrivacyPolicy />} />
            <Route path="/cookie-policy" element={<CookiePolicy />} />
            <Route path="/terms-of-service" element={<TermsOfService />} />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  </ThemeProvider>
);

export default App;
