import DecorationForm from './DecorationForm';
import CateringForm from './CateringForm';
import PriestForm from './PriestForm';
import PhotographyForm from './PhotographyForm';
import VideographyForm from './VideographyForm';
import MusicForm from './MusicForm';
import TransportationForm from './TransportationForm';
import InvitationForm from './InvitationForm';
import GiftsForm from './GiftsForm';
import LightingForm from './LightingForm';

// Map of service categories to their respective form components
const ServiceForms = {
  Decoration: DecorationForm,
  Catering: CateringForm,
  Priest: PriestForm,
  Photography: PhotographyForm,
  Videography: VideographyForm,
  Music: MusicForm,
  Transportation: TransportationForm,
  Invitation: InvitationForm,
  Gifts: GiftsForm,
  Lighting: LightingForm,
  // Add lowercase versions for case-insensitive matching
  decoration: DecorationForm,
  catering: CateringForm,
  priest: PriestForm,
  photography: PhotographyForm,
  videography: VideographyForm,
  music: MusicForm,
  transportation: TransportationForm,
  invitation: InvitationForm,
  gifts: GiftsForm,
  lighting: LightingForm
};

export default ServiceForms;
