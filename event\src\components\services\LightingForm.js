import React, { useState, useEffect, useCallback } from 'react';
import { Check, Settings, Sparkles } from 'lucide-react';

const LightingForm = ({ onSubmit, previousData, validateRef, useContinueButton = false }) => {
  const [formData, setFormData] = useState({
    lightingType: [],
    colorTheme: '',
    specialEffects: [],
    powerRequirements: '',
    specialRequests: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [showSuccess, setShowSuccess] = useState(false);

  // Initialize form data with previous data if available
  useEffect(() => {
    if (previousData) {
      setFormData(prevData => ({
        ...prevData,
        ...previousData
      }));
    }
  }, [previousData]);

  // Validate form and submit silently (for continue button)
  // Using useCallback to memoize the function and avoid circular dependencies
  const validateAndSubmitSilent = useCallback(() => {
    const errors = {};

    // Check required fields
    if (!formData.lightingType || formData.lightingType.length === 0) {
      errors.lightingType = 'Please select at least one lighting type';
    }
    if (!formData.colorTheme) errors.colorTheme = 'Please select a color theme';

    if (Object.keys(errors).length === 0) {
      // Form is valid, submit
      onSubmit({
        serviceDetails: formData,
        category: 'Lighting',
        name: 'Lighting Service'
      });

      // Return true to indicate validation passed
      return true;
    } else {
      // Form has errors
      setFormErrors(errors);

      // Return false to indicate validation failed
      return false;
    }
  }, [formData, onSubmit, setFormErrors]);

  // Register the validation function with the parent component
  useEffect(() => {
    if (validateRef && useContinueButton) {
      validateRef(() => {
        // Return the result of validateAndSubmit
        return validateAndSubmitSilent();
      });
    }
  }, [validateRef, useContinueButton, validateAndSubmitSilent]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (type === 'checkbox') {
      if (checked) {
        setFormData({
          ...formData,
          [name]: [...(formData[name] || []), value]
        });
      } else {
        setFormData({
          ...formData,
          [name]: (formData[name] || []).filter(item => item !== value)
        });
      }
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };



  // Validate form and submit with visual feedback
  const validateAndSubmit = () => {
    const isValid = validateAndSubmitSilent();

    if (isValid) {
      // Show success message
      setShowSuccess(true);

      // Hide success message after 3 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }

    return isValid;
  };

  return (
    <div className="bg-white dark:bg-dark-secondary p-6 rounded-lg shadow-md">
      {showSuccess && (
        <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg text-green-700 dark:text-green-300 flex items-center">
          <Check className="mr-2" size={18} />
          Lighting details saved successfully!
        </div>
      )}

      <p className="mb-6 text-light-secondary dark:text-gray-400 flex items-center">
        <Settings className="mr-2 text-[#ff5a5f]" size={16} />
        Please provide details for your lighting requirements
      </p>

      <div className="space-y-6">
        {/* Lighting Type */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Lighting Type <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {["Ambient Lighting", "Stage Lighting", "Decorative Lighting", "LED Walls/Screens", "Special Effects Lighting", "Outdoor Lighting", "Custom"].map((option, i) => (
              <div key={i} className="flex items-start">
                <input
                  type="checkbox"
                  id={`lightingType-${i}`}
                  name="lightingType"
                  value={option}
                  checked={(formData.lightingType || []).includes(option)}
                  onChange={handleInputChange}
                  className="h-4 w-4 mt-1 text-[#ff5a5f] focus:ring-[#ff5a5f] border-gray-300 rounded"
                />
                <label htmlFor={`lightingType-${i}`} className="ml-2 block text-sm text-light-primary dark:text-white">
                  {option}
                </label>
              </div>
            ))}
          </div>
          {formErrors.lightingType && (
            <p className="mt-1 text-sm text-red-500">{formErrors.lightingType}</p>
          )}
        </div>

        {/* Color Theme */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Color Theme <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="relative">
            <select
              name="colorTheme"
              value={formData.colorTheme || ''}
              onChange={handleInputChange}
              className={`w-full p-3 rounded-lg border ${formErrors.colorTheme ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none`}
            >
              <option value="">Select Color Theme</option>
              <option value="Warm White">Warm White</option>
              <option value="Cool White">Cool White</option>
              <option value="RGB/Multicolor">RGB/Multicolor</option>
              <option value="Event Theme Colors">Event Theme Colors</option>
              <option value="Dynamic/Changing">Dynamic/Changing</option>
              <option value="Custom">Custom</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
            {formErrors.colorTheme && (
              <p className="mt-1 text-sm text-red-500">{formErrors.colorTheme}</p>
            )}
          </div>
        </div>

        {/* Power Requirements */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Power Requirements
          </label>
          <div className="relative">
            <select
              name="powerRequirements"
              value={formData.powerRequirements || ''}
              onChange={handleInputChange}
              className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none"
            >
              <option value="">Select Power Requirements</option>
              <option value="Standard (up to 5kW)">Standard (up to 5kW)</option>
              <option value="Medium (5-10kW)">Medium (5-10kW)</option>
              <option value="High (10-20kW)">High (10-20kW)</option>
              <option value="Very High (20kW+)">Very High (20kW+)</option>
              <option value="Need consultation">Need consultation</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
          </div>
        </div>

        {/* Special Effects */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Special Effects
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {["Fog/Haze", "Laser Lights", "Moving Heads", "Projection Mapping", "Spotlights", "Strobes", "Flame Effects", "Water Features"].map((option, i) => (
              <div key={i} className="flex items-start">
                <input
                  type="checkbox"
                  id={`specialEffects-${i}`}
                  name="specialEffects"
                  value={option}
                  checked={(formData.specialEffects || []).includes(option)}
                  onChange={handleInputChange}
                  className="h-4 w-4 mt-1 text-[#ff5a5f] focus:ring-[#ff5a5f] border-gray-300 rounded"
                />
                <label htmlFor={`specialEffects-${i}`} className="ml-2 block text-sm text-light-primary dark:text-white">
                  {option}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Special Requests */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Special Requests
          </label>
          <textarea
            name="specialRequests"
            value={formData.specialRequests || ''}
            onChange={handleInputChange}
            placeholder="Any specific lighting requirements or effects..."
            rows="3"
            className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                     bg-white dark:bg-dark-primary text-light-primary dark:text-white
                     focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent"
          />
        </div>
      </div>

      {/* Only show the save button if not using the continue button */}
      {!useContinueButton && (
        <div className="mt-8 flex justify-end">
          <button
            type="button"
            onClick={validateAndSubmit}
            className="px-6 py-3 rounded-lg bg-gradient-to-r from-[#ff5a5f] to-[#ff8c8f] text-white font-medium hover:shadow-md transition-all duration-300 flex items-center"
          >
            {previousData ? (
              <>
                <Check className="mr-2" size={18} />
                Update Lighting Details
              </>
            ) : (
              <>
                <Sparkles className="mr-2" size={18} />
                Save Lighting Details
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default LightingForm;
