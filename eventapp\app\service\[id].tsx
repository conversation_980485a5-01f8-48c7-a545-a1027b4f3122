import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Image, TouchableOpacity, SafeAreaView } from 'react-native';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import serviceData from '../../data/ServiceData.json';
import { Service, ServiceData } from '../../types/service';

export default function ServiceDetailPage() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  console.log('Service Data:', serviceData); // Debug log
  console.log('Looking for service with ID:', id); // Debug log
  
  const services = (serviceData as ServiceData).services;
  const service = services.find((s: Service) => s.id === id);
  console.log('Found service:', service); // Debug log
  
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null);

  if (!service) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen 
          options={{
            title: 'Service Details',
            headerShown: true,
          }} 
        />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Service not found</Text>
          <Text style={styles.errorSubText}>Please check the service ID and try again</Text>
        </View>
      </SafeAreaView>
    );
  }

  const toggleFaq = (index: number) => {
    setExpandedFaq(expandedFaq === index ? null : index);
  };

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen 
        options={{
          title: service.title,
          headerShown: true,
        }} 
      />
      <ScrollView style={styles.scrollView}>
        <Text style={styles.description}>{service.longDescription}</Text>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Features</Text>
          <View style={styles.featuresList}>
            {service.features.map((feature: string, index: number) => (
              <View key={index} style={styles.featureItem}>
                <Text style={styles.featureText}>• {feature}</Text>
              </View>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Packages</Text>
          {service.packages.map((pkg, index: number) => (
            <View key={index} style={styles.packageCard}>
              <Text style={styles.packageName}>{pkg.name}</Text>
              <Text style={styles.packagePrice}>{pkg.price}</Text>
              <View style={styles.includesList}>
                {pkg.includes.map((item: string, idx: number) => (
                  <Text key={idx} style={styles.includesItem}>✓ {item}</Text>
                ))}
              </View>
              <TouchableOpacity 
                style={styles.bookButton}
                onPress={() => {
                  // TODO: Implement booking functionality
                  console.log('Book now pressed for package:', pkg.name);
                }}
              >
                <Text style={styles.bookButtonText}>Book Now</Text>
              </TouchableOpacity>
            </View>
          ))}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Frequently Asked Questions</Text>
          {service.faq.map((item, index: number) => (
            <TouchableOpacity
              key={index}
              style={styles.faqItem}
              onPress={() => toggleFaq(index)}
            >
              <View style={styles.faqHeader}>
                <Text style={styles.faqQuestion}>{item.question}</Text>
                <Text style={styles.faqToggle}>
                  {expandedFaq === index ? '−' : '+'}
                </Text>
              </View>
              {expandedFaq === index && (
                <Text style={styles.faqAnswer}>{item.answer}</Text>
              )}
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#666',
    marginBottom: 8,
  },
  errorSubText: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#666',
    marginBottom: 24,
    lineHeight: 24,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  featuresList: {
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    padding: 16,
  },
  featureItem: {
    marginBottom: 8,
  },
  featureText: {
    fontSize: 16,
    color: '#444',
  },
  packageCard: {
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  packageName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  packagePrice: {
    fontSize: 16,
    color: '#007AFF',
    marginBottom: 12,
  },
  includesList: {
    marginBottom: 16,
  },
  includesItem: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  bookButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  bookButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  faqItem: {
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    marginBottom: 8,
    overflow: 'hidden',
  },
  faqHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  faqQuestion: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    flex: 1,
    marginRight: 16,
  },
  faqToggle: {
    fontSize: 20,
    color: '#007AFF',
    fontWeight: 'bold',
  },
  faqAnswer: {
    fontSize: 14,
    color: '#666',
    padding: 16,
    paddingTop: 0,
    lineHeight: 20,
  },
}); 