{"name": "@types/http-proxy", "version": "1.17.16", "description": "TypeScript definitions for http-proxy", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-proxy", "license": "MIT", "contributors": [{"name": "Maxime LUCE", "githubUsername": "SomaticIT", "url": "https://github.com/SomaticIT"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "Raigen", "url": "https://github.com/Raigen"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON>", "url": "https://github.com/<PERSON>"}, {"name": "<PERSON>", "githubUsername": "jabreu610", "url": "https://github.com/jabreu610"}, {"name": "<PERSON>", "githubUsername": "bod<PERSON><PERSON><PERSON>", "url": "https://github.com/bodinsamuel"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/http-proxy"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "9485ba96f8d1f5becbf3fb9727c6870f2ee1e47b103bc22d93989a5c33bdd8d7", "typeScriptVersion": "5.0"}