import React from 'react';
import { Tabs } from 'expo-router';
import { useColorScheme, Platform, View } from 'react-native';
import Colors from '@/constants/Colors';
import { 
  Home,
  Calendar,
  PartyPopper,
  Mail,
  UserCircle
} from 'lucide-react-native';
import { useSafeAreaInsets, SafeAreaView } from 'react-native-safe-area-context';

export default function TabLayout() {
  const colorScheme = useColorScheme() ?? 'light';
  const colors = Colors[colorScheme];
  const insets = useSafeAreaInsets();
  const isWeb = Platform.OS === 'web';

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background }} edges={['top', 'bottom']}>
      <Tabs
        screenOptions={{
          tabBarActiveTintColor: colors.primary,
          tabBarInactiveTintColor: colors.tabIconDefault,
          tabBarStyle: {
            backgroundColor: colors.card,
            borderTopColor: colors.border,
            elevation: 0,
            shadowOpacity: 0,
            height: isWeb ? 70 : (Platform.OS === 'ios' ? 28 + insets.bottom : 28),
            paddingBottom: isWeb ? 4 : (Platform.OS === 'ios' ? insets.bottom : 0),
            paddingTop: isWeb ? 8 : 0,
            marginBottom: isWeb ? 0 : -20,
          },
          tabBarLabelStyle: {
            fontFamily: 'Inter-Medium',
            fontSize: isWeb ? 12 : 10,
            marginTop: isWeb ? 2 : 0,
            marginBottom: isWeb ? 2 : -12,
            display: 'flex',
          },
          tabBarIconStyle: {
            marginBottom: 0,
          },
          headerShown: false,
        }}
      >
        <Tabs.Screen
          name="index"
          options={{
            title: 'Home',
            tabBarLabel: 'Home',
            tabBarIcon: ({ color, size }) => <Home size={isWeb ? size + 4 : size + 2} strokeWidth={2} color={color} />,
          }}
        />
        <Tabs.Screen
          name="events"
          options={{
            title: 'Events',
            tabBarLabel: 'Events',
            tabBarIcon: ({ color, size }) => <Calendar size={isWeb ? size + 4 : size + 2} strokeWidth={2} color={color} />,
          }}
        />
        <Tabs.Screen
          name="services"
          options={{
            title: 'Services',
            tabBarLabel: 'Services',
            tabBarIcon: ({ color, size }) => <PartyPopper size={isWeb ? size + 4 : size + 2} strokeWidth={2} color={color} />,
          }}
        />
        {/*
        <Tabs.Screen
          name="einvites"
          options={{
            title: 'E-Invites',
            tabBarLabel: 'E-Invites',
            tabBarIcon: ({ color, size }) => <Mail size={isWeb ? size + 4 : size + 2} strokeWidth={2} color={color} />, 
          }}
        />
        */}
        <Tabs.Screen
          name="profile"
          options={{
            title: 'Profile',
            tabBarLabel: 'Profile',
            tabBarIcon: ({ color, size }) => <UserCircle size={isWeb ? size + 4 : size + 2} strokeWidth={2} color={color} />,
          }}
        />
      </Tabs>
    </SafeAreaView>
  );
}