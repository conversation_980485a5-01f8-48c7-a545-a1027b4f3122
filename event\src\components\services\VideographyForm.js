import React, { useState, useEffect } from 'react';
import { Check, Setting<PERSON>, Sparkles } from 'lucide-react';

const VideographyForm = ({ onSubmit, previousData }) => {
  const [formData, setFormData] = useState({
    coverageType: '',
    duration: '',
    deliverables: [],
    additionalServices: [],
    numberOfCameras: '',
    specialRequests: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [showSuccess, setShowSuccess] = useState(false);

  // Initialize form data with previous data if available
  useEffect(() => {
    if (previousData) {
      setFormData(prevData => ({
        ...prevData,
        ...previousData
      }));
    }
  }, [previousData]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (type === 'checkbox') {
      if (checked) {
        setFormData({
          ...formData,
          [name]: [...(formData[name] || []), value]
        });
      } else {
        setFormData({
          ...formData,
          [name]: (formData[name] || []).filter(item => item !== value)
        });
      }
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // Validate form and submit
  const validateAndSubmit = () => {
    const errors = {};

    // Check required fields
    if (!formData.coverageType) errors.coverageType = 'Please select a coverage type';
    if (!formData.duration) errors.duration = 'Please select a duration';
    if (!formData.deliverables || formData.deliverables.length === 0) {
      errors.deliverables = 'Please select at least one deliverable';
    }

    if (Object.keys(errors).length === 0) {
      // Form is valid, submit
      onSubmit({
        serviceDetails: formData,
        category: 'Videography',
        name: 'Videography Service'
      });
      
      // Show success message
      setShowSuccess(true);
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    } else {
      // Form has errors
      setFormErrors(errors);
    }
  };

  return (
    <div className="bg-white dark:bg-dark-secondary p-6 rounded-lg shadow-md">
      {showSuccess && (
        <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg text-green-700 dark:text-green-300 flex items-center">
          <Check className="mr-2" size={18} />
          Videography details saved successfully!
        </div>
      )}
      
      <p className="mb-6 text-light-secondary dark:text-gray-400 flex items-center">
        <Settings className="mr-2 text-[#ff5a5f]" size={16} />
        Please provide details for your videography requirements
      </p>

      <div className="space-y-6">
        {/* Coverage Type */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Coverage Type <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="relative">
            <select
              name="coverageType"
              value={formData.coverageType || ''}
              onChange={handleInputChange}
              className={`w-full p-3 rounded-lg border ${formErrors.coverageType ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none`}
            >
              <option value="">Select Coverage Type</option>
              <option value="Event Highlights">Event Highlights</option>
              <option value="Full Ceremony Recording">Full Ceremony Recording</option>
              <option value="Cinematic Film">Cinematic Film</option>
              <option value="Documentary Style">Documentary Style</option>
              <option value="Complete Coverage">Complete Coverage</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
            {formErrors.coverageType && (
              <p className="mt-1 text-sm text-red-500">{formErrors.coverageType}</p>
            )}
          </div>
        </div>

        {/* Duration */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Duration <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="relative">
            <select
              name="duration"
              value={formData.duration || ''}
              onChange={handleInputChange}
              className={`w-full p-3 rounded-lg border ${formErrors.duration ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none`}
            >
              <option value="">Select Duration</option>
              <option value="2-4 hours">2-4 hours</option>
              <option value="4-6 hours">4-6 hours</option>
              <option value="6-8 hours">6-8 hours</option>
              <option value="Full day">Full day</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
            {formErrors.duration && (
              <p className="mt-1 text-sm text-red-500">{formErrors.duration}</p>
            )}
          </div>
        </div>

        {/* Number of Cameras */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Number of Cameras
          </label>
          <div className="relative">
            <select
              name="numberOfCameras"
              value={formData.numberOfCameras || ''}
              onChange={handleInputChange}
              className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none"
            >
              <option value="">Select Number of Cameras</option>
              <option value="1 Camera">1 Camera</option>
              <option value="2 Cameras">2 Cameras</option>
              <option value="3 Cameras">3 Cameras</option>
              <option value="4+ Cameras">4+ Cameras</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
          </div>
        </div>

        {/* Deliverables */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Deliverables <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {["Highlight Reel (3-5 min)", "Short Film (5-10 min)", "Full Ceremony Video", "Raw Footage", "Social Media Clips", "Teaser Video", "USB/DVD of Videos"].map((option, i) => (
              <div key={i} className="flex items-start">
                <input
                  type="checkbox"
                  id={`deliverables-${i}`}
                  name="deliverables"
                  value={option}
                  checked={(formData.deliverables || []).includes(option)}
                  onChange={handleInputChange}
                  className="h-4 w-4 mt-1 text-[#ff5a5f] focus:ring-[#ff5a5f] border-gray-300 rounded"
                />
                <label htmlFor={`deliverables-${i}`} className="ml-2 block text-sm text-light-primary dark:text-white">
                  {option}
                </label>
              </div>
            ))}
          </div>
          {formErrors.deliverables && (
            <p className="mt-1 text-sm text-red-500">{formErrors.deliverables}</p>
          )}
        </div>

        {/* Additional Services */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Additional Services
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {["Drone Footage", "Same-day Edit", "Multiple Cameras", "Special Effects", "Interviews", "Live Streaming", "Pre-event Shoot"].map((option, i) => (
              <div key={i} className="flex items-start">
                <input
                  type="checkbox"
                  id={`additionalServices-${i}`}
                  name="additionalServices"
                  value={option}
                  checked={(formData.additionalServices || []).includes(option)}
                  onChange={handleInputChange}
                  className="h-4 w-4 mt-1 text-[#ff5a5f] focus:ring-[#ff5a5f] border-gray-300 rounded"
                />
                <label htmlFor={`additionalServices-${i}`} className="ml-2 block text-sm text-light-primary dark:text-white">
                  {option}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Special Requests */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Special Requests
          </label>
          <textarea
            name="specialRequests"
            value={formData.specialRequests || ''}
            onChange={handleInputChange}
            placeholder="Any specific videography requirements..."
            rows="3"
            className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                     bg-white dark:bg-dark-primary text-light-primary dark:text-white
                     focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent"
          />
        </div>
      </div>

      <div className="mt-8 flex justify-end">
        <button
          type="button"
          onClick={validateAndSubmit}
          className="px-6 py-3 rounded-lg bg-gradient-to-r from-[#ff5a5f] to-[#ff8c8f] text-white font-medium hover:shadow-md transition-all duration-300 flex items-center"
        >
          {previousData ? (
            <>
              <Check className="mr-2" size={18} />
              Update Videography Details
            </>
          ) : (
            <>
              <Sparkles className="mr-2" size={18} />
              Save Videography Details
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default VideographyForm;
