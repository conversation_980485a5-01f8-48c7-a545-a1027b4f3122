import React, { useState, useEffect } from 'react';
import { MapPin } from 'lucide-react';

const LocationDropdown = ({ isOpen, setIsOpen }) => {
  const [location, setLocation] = useState(null);

  const cities = [
    { name: "Hyderabad", state: "HYD" },
    { name: "Banglore", state: "BLR" },
  ];

  const handleLocationSelect = (city) => {
    setLocation(city);
    setIsOpen(false);
    localStorage.setItem('selectedLocation', JSON.stringify(city));
  };

  useEffect(() => {
    const savedLocation = localStorage.getItem('selectedLocation');
    if (savedLocation) {
      setLocation(JSON.parse(savedLocation));
    }
  }, []);

  return (
    <div className="relative location-dropdown">
      <button 
        className="flex items-center space-x-1 text-light-primary dark:text-white hover:text-[#ff5a5f]"
        onClick={() => setIsOpen(!isOpen)}
      >
        <MapPin size={20} />
        <span className="hidden md:inline">
          {location ? `${location.name}, ${location.state}` : "Set Location"}
        </span>
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-64 bg-light-primary dark:bg-dark-secondary shadow-lg rounded-lg p-2 z-50">
          <div className="max-h-60 overflow-y-auto">
            {cities.map((city, index) => (
              <button
                key={index}
                className="w-full text-left p-2 text-light-primary dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition"
                onClick={() => handleLocationSelect(city)}
              >
                <div className="flex items-center">
                  <MapPin size={16} className="mr-2" />
                  <span>{city.name}, {city.state}</span>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default LocationDropdown;