import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Share,
  Platform,
  Alert,
  SafeAreaView,
  KeyboardAvoidingView,
} from 'react-native';
import ViewShot from 'react-native-view-shot';
import { useLocalSearchParams, useRouter } from 'expo-router';
import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';
import * as Contacts from 'expo-contacts';
import { Linking } from 'react-native';
import { nanoid } from 'nanoid';

type TemplateParams = {
  templateId: string;
  templateName: string;
  defaultMessage: string;
  backgroundImage: string;
  colors: string;
};

interface InvitationData {
  id: string;
  title: string;
  date: string;
  time: string;
  location: string;
  message: string;
  templateId: string;
  backgroundImage: string;
  colors: string[];
  createdAt: string;
}

export default function TemplateEditor() {
  const params = useLocalSearchParams() as TemplateParams;
  const router = useRouter();
  const viewShotRef = useRef<ViewShot>(null);
  
  const [title, setTitle] = useState(params.templateName);
  const [date, setDate] = useState('');
  const [time, setTime] = useState('');
  const [location, setLocation] = useState('');
  const [message, setMessage] = useState(params.defaultMessage);
  const [showContactModal, setShowContactModal] = useState(false);
  const [contacts, setContacts] = useState<any[]>([]);
  const [isGeneratingLink, setIsGeneratingLink] = useState(false);

  const colors = JSON.parse(params.colors);

  const captureView = async () => {
    if (!viewShotRef.current) return null;
    try {
      return await viewShotRef.current.capture();
    } catch (error) {
      console.error('Error capturing view:', error);
      return null;
    }
  };

  const generateShareableLink = async () => {
    setIsGeneratingLink(true);
    try {
      const invitationData: InvitationData = {
        id: nanoid(),
        title,
        date,
        time,
        location,
        message,
        templateId: params.templateId,
        backgroundImage: params.backgroundImage,
        colors: colors,
        createdAt: new Date().toISOString(),
      };

      // In a real app, you would save this to your backend
      // For now, we'll store it in local storage
      const invitations = await FileSystem.readAsStringAsync(
        FileSystem.documentDirectory + 'invitations.json'
      ).catch(() => '[]');
      
      const invitationsArray = JSON.parse(invitations);
      invitationsArray.push(invitationData);
      
      await FileSystem.writeAsStringAsync(
        FileSystem.documentDirectory + 'invitations.json',
        JSON.stringify(invitationsArray)
      );

      // Generate a shareable link
      const shareableLink = `eventapp://invitation/${invitationData.id}`;
      
      await Share.share({
        message: `Check out my invitation: ${shareableLink}`,
        url: shareableLink,
      });
    } catch (error) {
      console.error('Error generating shareable link:', error);
      Alert.alert('Error', 'Failed to generate shareable link. Please try again.');
    } finally {
      setIsGeneratingLink(false);
    }
  };

  const handleSave = async () => {
    const uri = await captureView();
    if (uri) {
      try {
        const { status } = await MediaLibrary.requestPermissionsAsync();
        
        if (status === 'granted') {
          const asset = await MediaLibrary.createAssetAsync(uri);
          await MediaLibrary.createAlbumAsync('EventApp', asset, false);
          alert('Invitation saved to gallery!');
        }
      } catch (error) {
        console.error('Error saving:', error);
      }
    }
  };

  const handleWhatsAppShare = async () => {
    const uri = await captureView();
    if (uri) {
      try {
        const { status } = await Contacts.requestPermissionsAsync();
        
        if (status === 'granted') {
          const { data } = await Contacts.getContactsAsync({
            fields: [Contacts.Fields.PhoneNumbers, Contacts.Fields.Name],
          });
          setContacts(data);
          setShowContactModal(true);
        }
      } catch (error) {
        console.error('Error sharing to WhatsApp:', error);
      }
    }
  };

  const sendWhatsAppMessage = async (phoneNumber: string) => {
    const uri = await captureView();
    if (uri) {
      try {
        const whatsappUrl = `whatsapp://send?phone=${phoneNumber}&text=${encodeURIComponent(message)}`;
        await Linking.openURL(whatsappUrl);
      } catch (error) {
        console.error('Error sending WhatsApp message:', error);
      }
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView style={styles.container}>
          <View style={styles.header}>
            <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
              <Text style={styles.backButtonText}>← Back</Text>
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Customize Invitation</Text>
          </View>

          <ViewShot ref={viewShotRef} style={styles.invitationContainer}>
            <Image
              source={{ uri: params.backgroundImage }}
              style={styles.backgroundImage}
            />
            <View style={styles.overlay}>
              <TextInput
                style={[styles.titleInput, { color: colors[1] }]}
                value={title}
                onChangeText={setTitle}
                placeholder="Event Title"
                placeholderTextColor={colors[1]}
              />
              <TextInput
                style={[styles.dateInput, { color: colors[1] }]}
                value={date}
                onChangeText={setDate}
                placeholder="Date"
                placeholderTextColor={colors[1]}
              />
              <TextInput
                style={[styles.timeInput, { color: colors[1] }]}
                value={time}
                onChangeText={setTime}
                placeholder="Time"
                placeholderTextColor={colors[1]}
              />
              <TextInput
                style={[styles.locationInput, { color: colors[1] }]}
                value={location}
                onChangeText={setLocation}
                placeholder="Location"
                placeholderTextColor={colors[1]}
              />
              <TextInput
                style={[styles.messageInput, { color: colors[1] }]}
                value={message}
                onChangeText={setMessage}
                placeholder="Your Message"
                placeholderTextColor={colors[1]}
                multiline
              />
            </View>
          </ViewShot>

          <View style={styles.actions}>
            <TouchableOpacity style={[styles.button, { backgroundColor: colors[0] }]} onPress={handleSave}>
              <Text style={styles.buttonText}>Save to Gallery</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.button, { backgroundColor: colors[0] }]} 
              onPress={generateShareableLink}
              disabled={isGeneratingLink}
            >
              <Text style={styles.buttonText}>
                {isGeneratingLink ? 'Generating Link...' : 'Share Link'}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.button, { backgroundColor: colors[0] }]} onPress={handleWhatsAppShare}>
              <Text style={styles.buttonText}>Share via WhatsApp</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    marginRight: 16,
  },
  backButtonText: {
    fontSize: 16,
    color: '#007AFF',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  invitationContainer: {
    margin: 16,
    borderRadius: 12,
    overflow: 'hidden',
    aspectRatio: 9/16,
  },
  backgroundImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.3)',
    padding: 20,
    justifyContent: 'center',
  },
  titleInput: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  dateInput: {
    fontSize: 24,
    textAlign: 'center',
    marginBottom: 10,
  },
  timeInput: {
    fontSize: 24,
    textAlign: 'center',
    marginBottom: 10,
  },
  locationInput: {
    fontSize: 20,
    textAlign: 'center',
    marginBottom: 20,
  },
  messageInput: {
    fontSize: 18,
    textAlign: 'center',
    marginTop: 20,
  },
  actions: {
    padding: 16,
    gap: 12,
    paddingBottom: Platform.OS === 'ios' ? 34 : 16, // Add extra padding for iOS home indicator
  },
  button: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
}); 