@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom gradient text */
.gradient-text {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-gradient-start via-gradient-mid to-gradient-end;
}

/* Custom gradient text with less transparency on the right */
.gradient-text-solid-end {
  @apply bg-clip-text bg-gradient-to-r;
  background-image: linear-gradient(to right,rgba(245, 34, 90, 0.94), rgba(219, 62, 67, 0.85), rgba(242, 139, 87, 0.8));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.gradient-text-gold {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-celebrate-gold to-celebrate-orange;
}

.gradient-text-blue {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-celebrate-blue to-celebrate-teal;
}

/* Custom gradient backgrounds */
.gradient-bg-primary {
  @apply bg-gradient-to-r from-gradient-start via-gradient-mid to-gradient-end;
}

.gradient-bg-secondary {
  @apply bg-gradient-to-r from-gradient-light to-gradient-start;
}

.gradient-bg-gold {
  @apply bg-gradient-to-r from-celebrate-cream to-celebrate-gold;
}

/* Animations */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.appear-animation {
  opacity: 0;
  transform: translateY(20px);
  animation: appear 0.6s ease-out forwards;
}

@keyframes appear {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

body {
  margin: 0;
  font-family: 'Inter', 'Roboto', system-ui, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Home page font consistency */
.home-page-text {
  font-family: 'Inter', 'Roboto', system-ui, sans-serif;
}

/* Custom component classes */
@layer components {
  /* Section styling */
  .section-padding {
    @apply py-16 md:py-24;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .section-title {
    @apply text-3xl md:text-4xl font-bold text-center mb-2 text-light-primary dark:text-white;
  }

  .section-subtitle {
    @apply text-lg md:text-xl text-center text-muted mb-8 max-w-3xl mx-auto;
  }

  /* Card styling */
  .card {
    @apply bg-white dark:bg-dark-secondary rounded-xl shadow-custom transition-all duration-300 hover:shadow-hover;
  }

  /* Button styling */
  .btn-primary {
    @apply bg-primary text-white px-6 py-3 rounded-full font-medium transition-all duration-300 hover:bg-primary/90 hover:scale-105;
  }

  .btn-secondary {
    @apply bg-secondary text-white px-6 py-3 rounded-full font-medium transition-all duration-300 hover:bg-secondary/90 hover:scale-105;
  }

  .btn-outline {
    @apply bg-transparent border-2 border-primary text-primary px-6 py-3 rounded-full font-medium transition-all duration-300 hover:bg-primary hover:text-white hover:scale-105;
  }

  /* Timeline styling for HowItWorks */
  .timeline-card {
    @apply bg-white dark:bg-dark-secondary p-4 sm:p-6 rounded-xl shadow-custom hover:shadow-hover transition-all duration-300;
  }

  .timeline-icon-container {
    @apply bg-primary/10 rounded-full w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 flex items-center justify-center transition-all duration-300 hover:bg-primary/20;
  }

  .timeline-icon {
    @apply text-primary w-5 h-5 sm:w-6 sm:h-6 md:w-8 md:h-8;
  }

  .timeline-title {
    @apply text-lg sm:text-xl font-semibold mb-2 flex items-center;
  }

  .timeline-number {
    @apply text-primary font-bold mr-2;
  }

  .timeline-description {
    @apply text-muted dark:text-gray-400 text-sm sm:text-base;
  }

  /* Animation classes */
  .timeline-item.opacity-100 {
    @apply translate-y-0;
  }

  /* Responsive timeline spacing */
  @media (min-width: 640px) {
    .timeline-item {
      margin-bottom: 2rem;
    }
  }

  @media (min-width: 768px) {
    .timeline-item {
      margin-bottom: 3rem;
    }
  }

  .heading-gradient {
    @apply bg-clip-text text-transparent bg-heading-gradient animate-fade-in-left;
  }

  .purple-gradient {
    @apply bg-clip-text text-transparent bg-purple-gradient font-display font-extrabold animate-fade-in-left;
  }

  .warm-gradient {
    @apply bg-clip-text text-transparent bg-warm-gradient font-display font-extrabold animate-fade-in-left;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply text-light-primary dark:text-white transition-colors;
  }

  h1 {
    @apply text-3xl md:text-4xl lg:text-5xl font-bold;
  }

  h2 {
    @apply text-2xl md:text-3xl lg:text-4xl font-bold;
  }

  h3 {
    @apply text-xl md:text-2xl font-semibold;
  }

  h4 {
    @apply text-lg md:text-xl font-semibold;
  }

  /* Button styles */
  .btn {
    @apply px-6 py-2 rounded-full transition-all duration-300 font-medium;
  }

  .btn-primary {
    @apply bg-gradient-to-r from-warm-coral to-warm-pink text-white hover:from-warm-coral hover:to-warm-pink;
  }

  .btn-outline {
    @apply bg-transparent border-2 border-warm-coral text-warm-coral hover:bg-gradient-to-r hover:from-warm-orange hover:to-warm-pink hover:text-white hover:border-transparent;
  }

  /* Card styles */
  .card {
    @apply bg-light-secondary dark:bg-dark-secondary rounded-lg shadow-md p-6 transition-colors;
  }

  /* Animation utilities */
  .fade-right {
    @apply animate-fade-in-right;
  }

  .fade-left {
    @apply animate-fade-in-left;
  }
}
