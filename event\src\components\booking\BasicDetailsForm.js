import React from 'react';
import { Calendar, Users, FileText, Clock, MapPin, Sparkles } from 'lucide-react';

const BasicDetailsForm = ({ formData, setFormData, onSubmit, eventType, availableServices = [] }) => {
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (type === 'checkbox') {
      // For checkboxes, update the services array
      const updatedServices = {...formData.services};
      updatedServices[name] = checked;
      setFormData({
        ...formData,
        services: updatedServices
      });
    } else {
      // For other inputs
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit();
  };

  // Check if at least one service is selected
  const isServiceSelected = Object.values(formData.services).some(value => value);

  // Service categories with descriptions
  const serviceCategories = [
    {
      id: "Decoration",
      name: "Decoration",
      description: "Theme setup, venue styling, floral arrangements",
      icon: "🎨"
    },
    {
      id: "Catering",
      name: "Catering",
      description: "Food, beverages, service staff, menu planning",
      icon: "🍽️"
    },
    {
      id: "Priest",
      name: "Priest Services",
      description: "Religious ceremonies, rituals, consultation",
      icon: "🙏"
    },
    {
      id: "Photography",
      name: "Photography",
      description: "Professional event photography, photo editing",
      icon: "📸"
    },
    {
      id: "Videography",
      name: "Videography",
      description: "Event filming, highlight reels, full video coverage",
      icon: "🎥"
    },
    {
      id: "Music",
      name: "Music/DJ",
      description: "DJ services, live music, sound equipment",
      icon: "🎵"
    },
    {
      id: "Transportation",
      name: "Transportation",
      description: "Guest shuttles, luxury vehicles, coordination",
      icon: "🚗"
    },
    {
      id: "Invitation",
      name: "Invitations",
      description: "Digital/printed invitations, RSVP management",
      icon: "💌"
    },
    {
      id: "Gifts",
      name: "Gifts & Favors",
      description: "Return gifts, guest favors, packaging",
      icon: "🎁"
    },
    {
      id: "Lighting",
      name: "Lighting",
      description: "Ambient lighting, stage lighting, special effects",
      icon: "💡"
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-2xl font-bold text-light-primary dark:text-white flex items-center">
          <Sparkles className="mr-2 text-[#ff5a5f]" size={24} />
          Event Details
        </h3>
        <div className="text-sm text-light-secondary dark:text-gray-400">
          Step 1 of 3
        </div>
      </div>

      <form onSubmit={handleSubmit} className="bg-light-secondary dark:bg-dark-secondary p-6 rounded-lg shadow-md">
        <div className="space-y-8">
          {/* Event Basics Section */}
          <div>
            <h4 className="text-lg font-semibold text-light-primary dark:text-white mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
              Event Basics
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Date Selection */}
              <div>
                <label htmlFor="eventDate" className="block text-sm font-medium text-light-primary dark:text-white mb-2 flex items-center">
                  <Calendar className="mr-2 text-[#ff5a5f]" size={16} />
                  Event Date
                </label>
                <input
                  type="date"
                  id="eventDate"
                  name="eventDate"
                  value={formData.eventDate}
                  onChange={handleChange}
                  min={new Date().toISOString().split('T')[0]} // Set min date to today
                  className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                           bg-white dark:bg-dark-primary text-light-primary dark:text-white
                           focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent"
                  required
                />
              </div>

              {/* Event Time */}
              <div>
                <label htmlFor="eventTime" className="block text-sm font-medium text-light-primary dark:text-white mb-2 flex items-center">
                  <Clock className="mr-2 text-[#ff5a5f]" size={16} />
                  Event Time
                </label>
                <input
                  type="time"
                  id="eventTime"
                  name="eventTime"
                  value={formData.eventTime || ""}
                  onChange={handleChange}
                  className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                           bg-white dark:bg-dark-primary text-light-primary dark:text-white
                           focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent"
                  required
                />
              </div>

              {/* Number of Persons */}
              <div>
                <label htmlFor="numberOfPersons" className="block text-sm font-medium text-light-primary dark:text-white mb-2 flex items-center">
                  <Users className="mr-2 text-[#ff5a5f]" size={16} />
                  Number of Guests
                </label>
                <input
                  type="number"
                  id="numberOfPersons"
                  name="numberOfPersons"
                  value={formData.numberOfPersons}
                  onChange={handleChange}
                  min="1"
                  className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                           bg-white dark:bg-dark-primary text-light-primary dark:text-white
                           focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent"
                  required
                />
              </div>

              {/* Event Location */}
              <div>
                <label htmlFor="eventLocation" className="block text-sm font-medium text-light-primary dark:text-white mb-2 flex items-center">
                  <MapPin className="mr-2 text-[#ff5a5f]" size={16} />
                  Event Location
                </label>
                <input
                  type="text"
                  id="eventLocation"
                  name="eventLocation"
                  value={formData.eventLocation || ""}
                  onChange={handleChange}
                  placeholder="City, Venue, etc."
                  className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                           bg-white dark:bg-dark-primary text-light-primary dark:text-white
                           focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent"
                  required
                />
              </div>
            </div>
          </div>

          {/* Services Required Section */}
          <div>
            <h4 className="text-lg font-semibold text-light-primary dark:text-white mb-4 pb-2 border-b border-gray-200 dark:border-gray-700 flex items-center">
              <Sparkles className="mr-2 text-[#ff5a5f]" size={16} />
              Services Required for {eventType ? eventType.replace(/-/g, " ").replace(/\b\w/g, l => l.toUpperCase()) : "Event"}
            </h4>
            <p className="text-sm text-light-secondary dark:text-gray-400 mb-4">
              Select the services you need for your event. You'll be able to choose specific vendors in the next steps.
            </p>
            {availableServices.length > 0 && (
              <p className="text-sm text-[#ff5a5f] mb-4">
                Showing only services available for this event type.
              </p>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Debug information */}
              {console.log("BasicDetailsForm - availableServices:", availableServices)}
              {console.log("BasicDetailsForm - serviceCategories:", serviceCategories.map(s => s.id))}

              {/* Filter serviceCategories based on availableServices */}
              {serviceCategories
                .filter(service => {
                  // If no availableServices provided, show all services
                  if (!availableServices || availableServices.length === 0) {
                    console.log(`Service ${service.id} shown because no availableServices`);
                    return true;
                  }

                  // Check if this service is in the availableServices list (case-insensitive)
                  const isAvailable = availableServices.some(availableService =>
                    availableService && availableService.toLowerCase() === service.id.toLowerCase()
                  );

                  console.log(`Service ${service.id} ${isAvailable ? 'is' : 'is not'} available`);
                  return isAvailable;
                })
                .map(service => (
                <div
                  key={service.id}
                  className={`p-4 rounded-lg border transition-all cursor-pointer
                    ${formData.services[service.id]
                      ? 'border-[#ff5a5f] bg-red-50 dark:bg-red-900/10'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'}`}
                  onClick={() => {
                    const updatedServices = {...formData.services};
                    updatedServices[service.id] = !updatedServices[service.id];
                    setFormData({
                      ...formData,
                      services: updatedServices
                    });
                  }}
                >
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <input
                        type="checkbox"
                        id={service.id}
                        name={service.id}
                        checked={formData.services[service.id] || false}
                        onChange={handleChange}
                        className="h-5 w-5 mt-0.5 text-[#ff5a5f] focus:ring-[#ff5a5f] border-gray-300 rounded"
                      />
                    </div>
                    <div className="ml-3 flex-grow">
                      <div className="flex items-center">
                        <span className="mr-2 text-xl">{service.icon}</span>
                        <label htmlFor={service.id} className="font-medium text-light-primary dark:text-white cursor-pointer">
                          {service.name}
                        </label>
                      </div>
                      <p className="text-sm text-light-secondary dark:text-gray-400 mt-1">
                        {service.description}
                      </p>
                      {formData.services[service.id] && (
                        <div className="mt-2 text-xs text-[#ff5a5f] font-medium">
                          You'll be able to provide specific details in the next steps
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {!isServiceSelected && (
              <p className="mt-4 text-sm text-red-500 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                Please select at least one service
              </p>
            )}
          </div>

          {/* Additional Notes */}
          <div>
            <label htmlFor="additionalNotes" className="block text-sm font-medium text-light-primary dark:text-white mb-2 flex items-center">
              <FileText className="mr-2 text-[#ff5a5f]" size={16} />
              Additional Notes (Optional)
            </label>
            <textarea
              id="additionalNotes"
              name="additionalNotes"
              value={formData.additionalNotes}
              onChange={handleChange}
              rows="3"
              placeholder="Tell us more about your event requirements..."
              className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent"
            />
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              className={`px-6 py-3 rounded-lg bg-gradient-to-r from-[#ff5a5f] to-[#ff8c8f] text-white font-medium
                        hover:shadow-md transition-all duration-300
                        ${!isServiceSelected && 'opacity-50 cursor-not-allowed'}`}
              disabled={!isServiceSelected}
            >
              Continue to Service Selection
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default BasicDetailsForm;
