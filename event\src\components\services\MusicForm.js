import React, { useState, useEffect, useCallback } from 'react';
import { Check, Setting<PERSON>, Sparkles } from 'lucide-react';

const MusicForm = ({ onSubmit, previousData, validateRef, useContinueButton = false }) => {
  const [formData, setFormData] = useState({
    musicType: '',
    duration: '',
    musicGenres: [],
    equipmentNeeded: [],
    performanceType: '',
    specialRequests: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [showSuccess, setShowSuccess] = useState(false);

  // Initialize form data with previous data if available
  useEffect(() => {
    if (previousData) {
      setFormData(prevData => ({
        ...prevData,
        ...previousData
      }));
    }
  }, [previousData]);

  // Validate form and submit silently (for continue button)
  // Using useCallback to memoize the function and avoid circular dependencies
  const validateAndSubmitSilent = useCallback(() => {
    const errors = {};

    // Check required fields
    if (!formData.musicType) errors.musicType = 'Please select a music type';
    if (!formData.duration) errors.duration = 'Please select a duration';
    if (!formData.musicGenres || formData.musicGenres.length === 0) {
      errors.musicGenres = 'Please select at least one music genre';
    }

    if (Object.keys(errors).length === 0) {
      // Form is valid, submit
      onSubmit({
        serviceDetails: formData,
        category: 'Music',
        name: 'Music/DJ Service'
      });

      // Return true to indicate validation passed
      return true;
    } else {
      // Form has errors
      setFormErrors(errors);

      // Return false to indicate validation failed
      return false;
    }
  }, [formData, onSubmit, setFormErrors]);

  // Register the validation function with the parent component
  useEffect(() => {
    if (validateRef && useContinueButton) {
      validateRef(() => {
        // Return the result of validateAndSubmit
        return validateAndSubmitSilent();
      });
    }
  }, [validateRef, useContinueButton, validateAndSubmitSilent]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (type === 'checkbox') {
      if (checked) {
        setFormData({
          ...formData,
          [name]: [...(formData[name] || []), value]
        });
      } else {
        setFormData({
          ...formData,
          [name]: (formData[name] || []).filter(item => item !== value)
        });
      }
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };



  // Validate form and submit with visual feedback
  const validateAndSubmit = () => {
    const isValid = validateAndSubmitSilent();

    if (isValid) {
      // Show success message
      setShowSuccess(true);

      // Hide success message after 3 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }

    return isValid;
  };

  return (
    <div className="bg-white dark:bg-dark-secondary p-6 rounded-lg shadow-md">
      {showSuccess && (
        <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg text-green-700 dark:text-green-300 flex items-center">
          <Check className="mr-2" size={18} />
          Music/DJ details saved successfully!
        </div>
      )}

      <p className="mb-6 text-light-secondary dark:text-gray-400 flex items-center">
        <Settings className="mr-2 text-[#ff5a5f]" size={16} />
        Please provide details for your music/DJ requirements
      </p>

      <div className="space-y-6">
        {/* Music Type */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Music Type <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="relative">
            <select
              name="musicType"
              value={formData.musicType || ''}
              onChange={handleInputChange}
              className={`w-full p-3 rounded-lg border ${formErrors.musicType ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none`}
            >
              <option value="">Select Music Type</option>
              <option value="DJ">DJ</option>
              <option value="Live Band">Live Band</option>
              <option value="Classical Musicians">Classical Musicians</option>
              <option value="Instrumental">Instrumental</option>
              <option value="Vocalist">Vocalist</option>
              <option value="DJ + Live Music">DJ + Live Music</option>
              <option value="Custom">Custom</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
            {formErrors.musicType && (
              <p className="mt-1 text-sm text-red-500">{formErrors.musicType}</p>
            )}
          </div>
        </div>

        {/* Duration */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Duration <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="relative">
            <select
              name="duration"
              value={formData.duration || ''}
              onChange={handleInputChange}
              className={`w-full p-3 rounded-lg border ${formErrors.duration ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none`}
            >
              <option value="">Select Duration</option>
              <option value="2-3 hours">2-3 hours</option>
              <option value="3-4 hours">3-4 hours</option>
              <option value="4-6 hours">4-6 hours</option>
              <option value="Full event">Full event</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
            {formErrors.duration && (
              <p className="mt-1 text-sm text-red-500">{formErrors.duration}</p>
            )}
          </div>
        </div>

        {/* Performance Type */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Performance Type
          </label>
          <div className="relative">
            <select
              name="performanceType"
              value={formData.performanceType || ''}
              onChange={handleInputChange}
              className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none"
            >
              <option value="">Select Performance Type</option>
              <option value="Background Music">Background Music</option>
              <option value="Dance Floor Music">Dance Floor Music</option>
              <option value="Concert Style">Concert Style</option>
              <option value="Interactive Performance">Interactive Performance</option>
              <option value="Themed Performance">Themed Performance</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
          </div>
        </div>

        {/* Music Genres */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Music Genres <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {["Bollywood", "Classical Indian", "Western Pop", "EDM/Dance", "Rock", "Sufi", "Folk", "Fusion", "Hip Hop", "Retro"].map((option, i) => (
              <div key={i} className="flex items-start">
                <input
                  type="checkbox"
                  id={`musicGenres-${i}`}
                  name="musicGenres"
                  value={option}
                  checked={(formData.musicGenres || []).includes(option)}
                  onChange={handleInputChange}
                  className="h-4 w-4 mt-1 text-[#ff5a5f] focus:ring-[#ff5a5f] border-gray-300 rounded"
                />
                <label htmlFor={`musicGenres-${i}`} className="ml-2 block text-sm text-light-primary dark:text-white">
                  {option}
                </label>
              </div>
            ))}
          </div>
          {formErrors.musicGenres && (
            <p className="mt-1 text-sm text-red-500">{formErrors.musicGenres}</p>
          )}
        </div>

        {/* Equipment Needed */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Equipment Needed
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {["Sound System", "Lighting", "Microphones", "Instruments", "Dance Floor", "Karaoke Setup", "DJ Booth", "Fog Machine"].map((option, i) => (
              <div key={i} className="flex items-start">
                <input
                  type="checkbox"
                  id={`equipmentNeeded-${i}`}
                  name="equipmentNeeded"
                  value={option}
                  checked={(formData.equipmentNeeded || []).includes(option)}
                  onChange={handleInputChange}
                  className="h-4 w-4 mt-1 text-[#ff5a5f] focus:ring-[#ff5a5f] border-gray-300 rounded"
                />
                <label htmlFor={`equipmentNeeded-${i}`} className="ml-2 block text-sm text-light-primary dark:text-white">
                  {option}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Special Requests */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Special Requests
          </label>
          <textarea
            name="specialRequests"
            value={formData.specialRequests || ''}
            onChange={handleInputChange}
            placeholder="Any specific songs or music requirements..."
            rows="3"
            className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                     bg-white dark:bg-dark-primary text-light-primary dark:text-white
                     focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent"
          />
        </div>
      </div>

      {/* Only show the save button if not using the continue button */}
      {!useContinueButton && (
        <div className="mt-8 flex justify-end">
          <button
            type="button"
            onClick={validateAndSubmit}
            className="px-6 py-3 rounded-lg bg-gradient-to-r from-[#ff5a5f] to-[#ff8c8f] text-white font-medium hover:shadow-md transition-all duration-300 flex items-center"
          >
            {previousData ? (
              <>
                <Check className="mr-2" size={18} />
                Update Music/DJ Details
              </>
            ) : (
              <>
                <Sparkles className="mr-2" size={18} />
                Save Music/DJ Details
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default MusicForm;
