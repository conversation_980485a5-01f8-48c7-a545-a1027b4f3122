{"name": "easemyevent-app", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "cross-env EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "lint": "expo lint"}, "dependencies": {"@expo-google-fonts/inter": "^0.2.3", "@expo/ngrok": "^4.1.3", "@expo/vector-icons": "^14.1.0", "@firebase/app": "^0.13.0", "@firebase/auth": "^1.10.6", "@lucide/lab": "^0.1.2", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-native-masked-view/masked-view": "^0.3.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "expo": "^53.0.0", "expo-apple-authentication": "^7.2.4", "expo-auth-session": "^6.1.5", "expo-blur": "~14.1.3", "expo-camera": "~16.1.5", "expo-constants": "~17.1.3", "expo-contacts": "^14.2.4", "expo-font": "~13.2.2", "expo-haptics": "~14.1.3", "expo-linear-gradient": "~14.1.3", "expo-linking": "~7.1.3", "expo-media-library": "^17.1.6", "expo-router": "~5.0.2", "expo-splash-screen": "~0.30.6", "expo-status-bar": "~2.2.2", "expo-symbols": "~0.4.3", "expo-system-ui": "~5.0.5", "expo-updates": "~0.28.13", "expo-web-browser": "~14.1.5", "firebase": "^11.8.1", "lucide-react-native": "^0.475.0", "nanoid": "^5.1.5", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.1", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.3.0", "react-native-screens": "~4.10.0", "react-native-svg": "^15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-view-shot": "^4.0.3", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/webpack-config": "^19.0.1", "@svgr/cli": "^8.1.0", "@types/firebase": "^2.4.32", "@types/react": "~19.0.10", "cross-env": "^10.0.0", "typescript": "~5.8.3"}}