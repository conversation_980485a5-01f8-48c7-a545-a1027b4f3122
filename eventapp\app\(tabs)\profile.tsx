import React, { useState } from 'react';
import { 
  View, Text, StyleSheet, ScrollView, TouchableOpacity, 
  Image, useColorScheme, SafeAreaView, Switch, Alert 
} from 'react-native';
import { router } from 'expo-router';
import Colors from '@/constants/Colors';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { User, CreditCard, Bell, Moon, LogOut, ChevronRight, Ticket, Heart, Settings, CircleHelp as HelpCircle, Calendar } from 'lucide-react-native';
import { useAuth } from '@/context/AuthContext';

export default function ProfileScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const colors = Colors[colorScheme];
  const { signOut, user } = useAuth();

  const handleLogout = () => {
    Alert.alert(
      'Log Out',
      'Are you sure you want to log out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Log Out',
          onPress: async () => {
            try {
              await signOut();
              // The AuthContext will handle the navigation to login
            } catch (error) {
              Alert.alert('Error', 'Failed to log out. Please try again.');
            }
          },
          style: 'destructive',
        },
      ]
    );
  };

  const renderMenuItem = (
    icon: React.ReactNode,
    title: string,
    onPress: () => void,
    hasSwitch?: boolean,
    switchValue?: boolean,
    onSwitchChange?: (value: boolean) => void
  ) => {
    return (
      <TouchableOpacity
        style={[styles.menuItem, { borderBottomColor: colors.border }]}
        onPress={onPress}
        disabled={hasSwitch}
      >
        <View style={styles.menuItemLeft}>
          {icon}
          <Text style={[styles.menuItemText, { color: colors.text }]}>{title}</Text>
        </View>
        
        {hasSwitch ? (
          <Switch
            value={switchValue}
            onValueChange={onSwitchChange}
            trackColor={{ false: colors.lightGray, true: colors.primary }}
            thumbColor="white"
          />
        ) : (
          <ChevronRight size={20} color={colors.darkGray} />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Profile</Text>
        </View>

        {/* User Info */}
        <Card style={styles.profileCard}>
          <View style={styles.profileHeader}>
            <Image 
              source={{ uri: user?.photoURL || `https://ui-avatars.com/api/?name=${encodeURIComponent(user?.name || user?.email || 'User')}&background=random` }}
              style={styles.avatar}
            />
            <View style={styles.profileInfo}>
              <Text style={[styles.profileName, { color: colors.text }]}> 
                {user?.name || ''}
              </Text>
              <Text style={[styles.profileEmail, { color: colors.darkGray }]}> 
                {user?.email || ''}
              </Text>
            </View>
          </View>
          
          <Button 
            title="Edit Profile" 
            onPress={() => router.push('/edit-profile')}
            variant="outline"
            size="small"
            style={styles.editButton}
          />
        </Card>

        {/* My Account */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>My Account</Text>
          <Card padding="none">
            {renderMenuItem(
              <Calendar size={20} color={colors.primary} />,
              'My Bookings',
              () => router.push('/bookings')
            )}
            {renderMenuItem(
              <Heart size={20} color={colors.primary} />,
              'Saved Events',
              () => {}
            )}
            {renderMenuItem(
              <CreditCard size={20} color={colors.primary} />,
              'Payment Methods',
              () => {}
            )}
            {renderMenuItem(
              <User size={20} color={colors.primary} />,
              'Personal Information',
              () => {}
            )}
          </Card>
        </View>

        {/* Settings */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Settings</Text>
          <Card padding="none">
            {renderMenuItem(
              <Settings size={20} color={colors.primary} />,
              'App Settings',
              () => {}
            )}
          </Card>
        </View>

        {/* Support */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Support</Text>
          <Card padding="none">
            {renderMenuItem(
              <HelpCircle size={20} color={colors.primary} />,
              'Help Center',
              () => {}
            )}
            {renderMenuItem(
              <LogOut size={20} color={colors.error} />,
              'Log Out',
              handleLogout
            )}
          </Card>
        </View>

        <View style={styles.footer}>
          <Text style={[styles.footerText, { color: colors.darkGray }]}>
            Version 1.0.0
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 22,
    fontFamily: 'Inter-Bold',
  },
  profileCard: {
    marginHorizontal: 16,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
  },
  profileInfo: {
    marginLeft: 16,
  },
  profileName: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  editButton: {
    alignSelf: 'flex-start',
  },
  section: {
    marginTop: 24,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 12,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuItemText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    marginLeft: 12,
  },
  footer: {
    alignItems: 'center',
    marginTop: 24,
    marginBottom: 30,
  },
  footerText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
});