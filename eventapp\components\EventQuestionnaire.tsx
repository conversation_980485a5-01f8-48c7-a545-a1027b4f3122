import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Animated,
  Platform,
  Dimensions,
  BackHandler,
} from 'react-native';
import {
  GestureHandlerRootView,
  PanGestureHandler,
  PanGestureHandlerGestureEvent,
} from 'react-native-gesture-handler';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import { router } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import eventDataJson from '../data/EventData.json';

interface Question {
  id: string;
  text: string;
  type: 'text' | 'date' | 'time' | 'number' | 'multiselect' | 'select';
  options?: string[];
  placeholder?: string;
  required?: boolean;
}

interface EventData {
  questions: Question[];
  serviceQuestions: Record<string, Question[]>;
}

interface EventQuestionnaireProps {
  onComplete: (answers: Record<string, any>) => void;
  eventName?: string;
}

const SWIPE_THRESHOLD = 50;
const { width: SCREEN_WIDTH } = Dimensions.get('window');

const getEventKey = (eventName?: string): string => {
  if (!eventName) return 'Birthday';
  const keys = Object.keys(eventDataJson);
  const found = keys.find(
    k => k.toLowerCase() === eventName.toLowerCase()
  );
  return found || 'Birthday';
};

const EventQuestionnaire: React.FC<EventQuestionnaireProps> = ({ onComplete, eventName }) => {
  const eventKey = getEventKey(eventName);
  const currentEventData = (eventDataJson as Record<string, EventData>)[eventKey];
  
  if (!currentEventData) {
    console.error(`Event data not found for key: ${eventKey}`);
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Sorry, this event type is not available.</Text>
      </View>
    );
  }

  const eventQuestions = currentEventData.questions || [];
  const eventServiceQuestions = currentEventData.serviceQuestions || {};

  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [slideAnimation] = useState(new Animated.Value(0));
  const [progressAnimation] = useState(new Animated.Value(0));
  const [serviceQuestionFlow, setServiceQuestionFlow] = useState<{
    active: boolean;
    serviceIndex: number;
    questionIndex: number;
    selectedServices: string[];
    serviceAnswers: Record<string, Record<string, any>>;
    showServiceConfirmation: boolean;
  }>({
    active: false,
    serviceIndex: 0,
    questionIndex: 0,
    selectedServices: [],
    serviceAnswers: {},
    showServiceConfirmation: false,
  });
  const [serviceInputValue, setServiceInputValue] = useState<any>(null);
  const [serviceMultiSelectValue, setServiceMultiSelectValue] = useState<any[]>([]);
  const [tempDate, setTempDate] = useState<Date | null>(null);
  const [tempTime, setTempTime] = useState<Date | null>(null);

  // Calculate progress based on completed questions
  const calculateProgress = useCallback(() => {
    if (serviceQuestionFlow.active) {
      const { selectedServices, serviceIndex, questionIndex, serviceAnswers } = serviceQuestionFlow;
      const service = selectedServices[serviceIndex];
      const serviceQuestions = eventServiceQuestions[service] || [];
      const completedQuestions = Object.keys(serviceAnswers[service] || {}).length;
      return completedQuestions / (serviceQuestions.length || 1);
    } else {
      const completedQuestions = Object.keys(answers).length;
      return completedQuestions / (eventQuestions.length || 1);
    }
  }, [answers, serviceQuestionFlow, eventQuestions.length, eventServiceQuestions]);

  // Get the progress label
  const progressLabel = serviceQuestionFlow.active 
    ? serviceQuestionFlow.selectedServices[serviceQuestionFlow.serviceIndex]
    : eventKey || 'Event';

  // Update progress animation when answers or service answers change
  useEffect(() => {
    const progress = calculateProgress();
    Animated.timing(progressAnimation, {
      toValue: progress,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [answers, serviceQuestionFlow.serviceAnswers, calculateProgress]);

  // Handle hardware back button
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (currentQuestionIndex > 0) {
        handleBack();
        return true;
      } else {
        router.back();
        return true;
      }
    });

    return () => backHandler.remove();
  }, [currentQuestionIndex]);

  // Effect to handle service question input values
  useEffect(() => {
    if (serviceQuestionFlow.active) {
      const { selectedServices, serviceIndex, questionIndex, serviceAnswers } = serviceQuestionFlow;
      const service = selectedServices[serviceIndex];
      const serviceQuestions = eventServiceQuestions[service] || [];
      const currentServiceQuestion = serviceQuestions[questionIndex];
      
      if (currentServiceQuestion) {
        const currentAnswer = (serviceAnswers[service] || {})[currentServiceQuestion.id];
        if (currentServiceQuestion.type === 'number' || currentServiceQuestion.type === 'text') {
          setServiceInputValue(currentAnswer ?? '');
        } else if (currentServiceQuestion.type === 'multiselect') {
          setServiceMultiSelectValue(Array.isArray(currentAnswer) ? currentAnswer : []);
        }
      }
    }
  }, [serviceQuestionFlow.active, serviceQuestionFlow.serviceIndex, serviceQuestionFlow.questionIndex, serviceQuestionFlow.serviceAnswers]);

  const animateTransition = (forward: boolean) => {
    const toValue = forward ? -1 : 1;
    
    // Reset to starting position
    slideAnimation.setValue(0);
    
    // Animate slide out
    Animated.sequence([
      Animated.timing(slideAnimation, {
        toValue: toValue,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnimation, {
        toValue: -toValue,
        duration: 0,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnimation, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      })
    ]).start();
  };

  const handleAnswer = (value: any) => {
    const currentQuestion = eventQuestions[currentQuestionIndex];
    if (!currentQuestion) return;

    // Validate service selection
    if (currentQuestion.id === 'services') {
      if (!Array.isArray(value) || value.length === 0) {
        console.error('Invalid service selection');
        return;
      }
      
      // Validate that all selected services exist in serviceQuestions
      const invalidServices = value.filter(service => !eventServiceQuestions[service]);
      if (invalidServices.length > 0) {
        console.error(`Invalid services selected: ${invalidServices.join(', ')}`);
        return;
      }
    }

    setAnswers(prev => ({
      ...prev,
      [currentQuestion.id]: value
    }));

    // If answering the services question, start service question flow
    if (currentQuestion.id === 'services') {
      setServiceQuestionFlow({
        active: true,
        serviceIndex: 0,
        questionIndex: 0,
        selectedServices: value,
        serviceAnswers: {},
        showServiceConfirmation: false,
      });
      return;
    }

    // Only auto-advance for single select questions
    if (currentQuestion.type === 'select') {
      if (currentQuestionIndex < eventQuestions.length - 1) {
        setCurrentQuestionIndex(currentQuestionIndex + 1);
      } else {
        onComplete(answers);
      }
    }
  };

  const handleDateChange = (event: DateTimePickerEvent, date?: Date) => {
    if (event.type === 'set' && date) {
      handleAnswer(date);
    }
    if (Platform.OS === 'android' && event.type === 'dismissed') {
      setShowDatePicker(false);
    }
  };

  const handleTimeChange = (event: DateTimePickerEvent, date?: Date) => {
    if (event.type === 'set' && date) {
      handleAnswer(date);
    }
    if (Platform.OS === 'android' && event.type === 'dismissed') {
      setShowTimePicker(false);
    }
  };

  const handleBack = () => {
    if (currentQuestionIndex > 0) {
      animateTransition(false);
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    } else {
      // Navigate back to events page with a slide animation
      const exitAnimation = Animated.timing(slideAnimation, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      });

      exitAnimation.start(() => {
        router.back();
      });
    }
  };

  const handleConfirm = () => {
    const currentQuestion = eventQuestions[currentQuestionIndex];
    
    if (currentQuestion.id === 'services') {
      setServiceQuestionFlow({
        active: true,
        serviceIndex: 0,
        questionIndex: 0,
        selectedServices: answers['services'],
        serviceAnswers: {},
        showServiceConfirmation: false,
      });
      return;
    }

    if (currentQuestionIndex < eventQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      onComplete(answers);
    }
  };

  const handleServiceAnswer = (value: any, type?: string) => {
    const { selectedServices, serviceIndex, questionIndex, serviceAnswers } = serviceQuestionFlow;
    const service = selectedServices[serviceIndex];
    const serviceQuestions = eventServiceQuestions[service] || [];
    const currentServiceQuestion = serviceQuestions[questionIndex];
    const updatedServiceAnswers = {
      ...serviceAnswers,
      [service]: {
        ...(serviceAnswers[service] || {}),
        [currentServiceQuestion.id]: value,
      },
    };

    // For select type, auto-advance
    if (type === 'select' || currentServiceQuestion.type === 'select') {
      if (questionIndex < serviceQuestions.length - 1) {
        setServiceQuestionFlow({
          ...serviceQuestionFlow,
          questionIndex: questionIndex + 1,
          serviceAnswers: updatedServiceAnswers,
        });
      } else {
        setServiceQuestionFlow({
          ...serviceQuestionFlow,
          showServiceConfirmation: true,
          serviceAnswers: updatedServiceAnswers,
        });
      }
      return;
    }

    // For other types, just update the answer
    setServiceQuestionFlow({
      ...serviceQuestionFlow,
      serviceAnswers: updatedServiceAnswers,
    });
  };

  const handleServiceConfirm = () => {
    const { selectedServices, serviceIndex, questionIndex, serviceAnswers } = serviceQuestionFlow;
    const service = selectedServices[serviceIndex];
    const serviceQuestions = eventServiceQuestions[service] || [];
    
    if (questionIndex < serviceQuestions.length - 1) {
      // Move to next question in current service
      setServiceQuestionFlow({
        ...serviceQuestionFlow,
        questionIndex: questionIndex + 1,
      });
    } else if (serviceIndex < selectedServices.length - 1) {
      // Move to first question of next service
      setServiceQuestionFlow({
        ...serviceQuestionFlow,
        serviceIndex: serviceIndex + 1,
        questionIndex: 0,
        showServiceConfirmation: false,
      });
    } else {
      // All services completed
      const bookingData = {
        id: Date.now(),
        eventType: eventKey || 'Event',
        selections: {
          basicDetails: {
            eventDate: answers.eventDate instanceof Date ? answers.eventDate.toLocaleDateString() : String(answers.eventDate),
            eventTime: answers.eventTime instanceof Date ? answers.eventTime.toLocaleTimeString() : String(answers.eventTime),
            eventLocation: answers.location || '',
            numberOfPersons: answers.guestCount || '',
          },
          ...serviceAnswers
        },
        totalAmount: 0, // You can calculate this based on your pricing logic
        status: 'Confirmed',
        date: new Date().toISOString()
      };

      // Save to storage based on platform
      const saveBooking = async () => {
        try {
          if (Platform.OS === 'web') {
            const existingBookings = JSON.parse(localStorage.getItem('bookings') || '[]');
            localStorage.setItem('bookings', JSON.stringify([...existingBookings, bookingData]));
          } else {
            const existingBookingsStr = await AsyncStorage.getItem('bookings');
            const existingBookings = existingBookingsStr ? JSON.parse(existingBookingsStr) : [];
            await AsyncStorage.setItem('bookings', JSON.stringify([...existingBookings, bookingData]));
          }
        } catch (error) {
          console.error('Error saving booking:', error);
        }
      };

      saveBooking();
      onComplete({ ...answers, serviceDetails: serviceAnswers });
      router.push('/bookings');
    }
  };

  const handleServiceConfirmation = () => {
    const { selectedServices, serviceIndex, serviceAnswers } = serviceQuestionFlow;
    if (serviceIndex < selectedServices.length - 1) {
      // Move to next service
      setServiceQuestionFlow({
        ...serviceQuestionFlow,
        serviceIndex: serviceIndex + 1,
        questionIndex: 0,
        showServiceConfirmation: false,
      });
    } else {
      // All services completed
      setAnswers(prev => ({
        ...prev,
        serviceDetails: serviceAnswers,
      }));
      setServiceQuestionFlow({
        ...serviceQuestionFlow,
        active: false,
        showServiceConfirmation: false,
      });
      onComplete({ ...answers, serviceDetails: serviceAnswers });
      router.push('/bookings');
    }
  };

  const handleGesture = useCallback((event: PanGestureHandlerGestureEvent) => {
    const { translationX } = event.nativeEvent;

    if (event.nativeEvent.state === 4) { // State.END
      if (translationX > SWIPE_THRESHOLD) {
        // Swipe right - go back
        if (serviceQuestionFlow.active) {
          const { serviceIndex, questionIndex, selectedServices } = serviceQuestionFlow;
          if (questionIndex > 0) {
            setServiceQuestionFlow(flow => ({
              ...flow,
              questionIndex: flow.questionIndex - 1,
              showServiceConfirmation: false,
            }));
          } else if (serviceIndex > 0) {
            // Go to last question of previous service
            const prevService = selectedServices[serviceIndex - 1];
            const prevServiceQuestions = eventServiceQuestions[prevService] || [];
            setServiceQuestionFlow(flow => ({
              ...flow,
              serviceIndex: flow.serviceIndex - 1,
              questionIndex: prevServiceQuestions.length - 1,
              showServiceConfirmation: false,
            }));
          } // else: at very first service and question, do nothing
        } else {
          handleBack();
        }
      } else if (translationX < -SWIPE_THRESHOLD && currentQuestionIndex < eventQuestions.length - 1) {
        // Swipe left - go forward (only if current question is answered)
        const currentQuestion = eventQuestions[currentQuestionIndex];
        const currentAnswer = answers[currentQuestion.id];
        
        if (currentAnswer !== undefined && 
            (Array.isArray(currentAnswer) ? currentAnswer.length > 0 : currentAnswer !== '')) {
          handleAnswer(currentAnswer);
        }
      }
    }
  }, [currentQuestionIndex, answers, eventQuestions, serviceQuestionFlow]);

  const renderQuestion = () => {
    const currentQuestion = eventQuestions[currentQuestionIndex];
    if (!currentQuestion) return null;

    const currentAnswer = answers[currentQuestion.id];
    const showConfirmButton = currentQuestion.type !== 'select';
    const isAnswered = currentAnswer !== undefined && 
      (Array.isArray(currentAnswer) ? currentAnswer.length > 0 : currentAnswer !== '');

    switch (currentQuestion.type) {
      case 'select':
        return (
          <ScrollView style={styles.optionsContainer}>
            {currentQuestion.options?.map((option: string) => (
              <TouchableOpacity
                key={option}
                style={[
                  styles.optionButton,
                  answers[currentQuestion.id] === option && styles.selectedOption
                ]}
                onPress={() => {
                  handleAnswer(option);
                }}
              >
                <Text style={[
                  styles.optionText,
                  answers[currentQuestion.id] === option && styles.selectedOptionText
                ]}>
                  {option}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        );

      case 'multiselect':
        return (
          <View style={styles.content}>
            <ScrollView style={styles.optionsContainer}>
              {currentQuestion.options?.map((option: string) => (
                <TouchableOpacity
                  key={option}
                  style={[
                    styles.optionButton,
                    (answers[currentQuestion.id] || []).includes(option) && styles.selectedOption
                  ]}
                  onPress={() => {
                    const currentValues = answers[currentQuestion.id] || [];
                    const newValues = currentValues.includes(option)
                      ? currentValues.filter((v: string) => v !== option)
                      : [...currentValues, option];
                    setAnswers(prev => ({
                      ...prev,
                      [currentQuestion.id]: newValues
                    }));
                  }}
                >
                  <Text style={[
                    styles.optionText,
                    (answers[currentQuestion.id] || []).includes(option) && styles.selectedOptionText
                  ]}>
                    {option}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
            {showConfirmButton && (
              <TouchableOpacity
                style={[
                  styles.confirmButton,
                  (!answers[currentQuestion.id] || answers[currentQuestion.id].length === 0) && styles.disabledButton
                ]}
                onPress={handleConfirm}
                disabled={!answers[currentQuestion.id] || answers[currentQuestion.id].length === 0}
              >
                <Text style={styles.confirmButtonText}>
                  {currentQuestionIndex === eventQuestions.length - 1 ? 'Complete' : 'Next'}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        );

      case 'date':
        return (
          <View style={styles.content}>
            {Platform.OS === 'web' ? (
              <View style={styles.dateInputContainer}>
                <input
                  type="date"
                  value={currentAnswer ? currentAnswer.toISOString().split('T')[0] : ''}
                  onChange={(e) => {
                    const date = new Date(e.target.value);
                    handleAnswer(date);
                  }}
                  min={new Date().toISOString().split('T')[0]}
                  style={{
                    width: '100%',
                    padding: 16,
                    fontSize: 16,
                    borderRadius: 12,
                    borderWidth: 1,
                    borderColor: '#e0e0e0',
                    backgroundColor: '#f8f8f8',
                  }}
                />
              </View>
            ) : (
              <>
                <TouchableOpacity
                  style={styles.dateButton}
                  onPress={() => setShowDatePicker(true)}
                >
                  <Text style={styles.dateButtonText}>
                    {currentAnswer ? currentAnswer.toLocaleDateString() : 'Select Date'}
                  </Text>
                </TouchableOpacity>
                {showDatePicker && (
                  <View style={Platform.OS === 'ios' ? styles.pickerContainer : null}>
                    <DateTimePicker
                      value={currentAnswer || new Date()}
                      mode="date"
                      display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                      onChange={handleDateChange}
                      minimumDate={new Date()}
                      style={Platform.OS === 'ios' ? styles.picker : undefined}
                      textColor="#333"
                    />
                    {Platform.OS === 'ios' && (
                      <View style={styles.pickerButtons}>
                        <TouchableOpacity
                          style={styles.pickerButton}
                          onPress={() => setShowDatePicker(false)}
                        >
                          <Text style={styles.pickerButtonText}>Cancel</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={[styles.pickerButton, styles.pickerButtonPrimary]}
                          onPress={() => setShowDatePicker(false)}
                        >
                          <Text style={[styles.pickerButtonText, styles.pickerButtonTextPrimary]}>Done</Text>
                        </TouchableOpacity>
                      </View>
                    )}
                  </View>
                )}
              </>
            )}
            {showConfirmButton && (
              <TouchableOpacity
                style={[
                  styles.confirmButton,
                  !currentAnswer && styles.disabledButton
                ]}
                onPress={handleConfirm}
                disabled={!currentAnswer}
              >
                <Text style={styles.confirmButtonText}>
                  {currentQuestionIndex === eventQuestions.length - 1 ? 'Complete' : 'Next'}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        );

      case 'time':
        return (
          <View style={styles.content}>
            {Platform.OS === 'web' ? (
              <View style={styles.dateInputContainer}>
                <input
                  type="time"
                  value={currentAnswer ? currentAnswer.toTimeString().slice(0, 5) : ''}
                  onChange={(e) => {
                    const [hours, minutes] = e.target.value.split(':');
                    const date = new Date();
                    date.setHours(parseInt(hours), parseInt(minutes));
                    handleAnswer(date);
                  }}
                  style={{
                    width: '100%',
                    padding: 16,
                    fontSize: 16,
                    borderRadius: 12,
                    borderWidth: 1,
                    borderColor: '#e0e0e0',
                    backgroundColor: '#f8f8f8',
                  }}
                />
              </View>
            ) : (
              <>
                <TouchableOpacity
                  style={styles.dateButton}
                  onPress={() => setShowTimePicker(true)}
                >
                  <Text style={styles.dateButtonText}>
                    {currentAnswer ? currentAnswer.toLocaleTimeString() : 'Select Time'}
                  </Text>
                </TouchableOpacity>
                {showTimePicker && (
                  <View style={Platform.OS === 'ios' ? styles.pickerContainer : null}>
                    <DateTimePicker
                      value={currentAnswer || new Date()}
                      mode="time"
                      display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                      onChange={handleTimeChange}
                      style={Platform.OS === 'ios' ? styles.picker : undefined}
                      textColor="#333"
                    />
                    {Platform.OS === 'ios' && (
                      <View style={styles.pickerButtons}>
                        <TouchableOpacity
                          style={styles.pickerButton}
                          onPress={() => setShowTimePicker(false)}
                        >
                          <Text style={styles.pickerButtonText}>Cancel</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={[styles.pickerButton, styles.pickerButtonPrimary]}
                          onPress={() => setShowTimePicker(false)}
                        >
                          <Text style={[styles.pickerButtonText, styles.pickerButtonTextPrimary]}>Done</Text>
                        </TouchableOpacity>
                      </View>
                    )}
                  </View>
                )}
              </>
            )}
            {showConfirmButton && (
              <TouchableOpacity
                style={[
                  styles.confirmButton,
                  !currentAnswer && styles.disabledButton
                ]}
                onPress={handleConfirm}
                disabled={!currentAnswer}
              >
                <Text style={styles.confirmButtonText}>
                  {currentQuestionIndex === eventQuestions.length - 1 ? 'Complete' : 'Next'}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        );

      case 'text':
        return (
          <View style={styles.content}>
            <TextInput
              style={styles.textInput}
              placeholder={currentQuestion.placeholder}
              value={answers[currentQuestion.id]}
              onChangeText={(text) => handleAnswer(text)}
            />
            {showConfirmButton && (
              <TouchableOpacity
                style={[
                  styles.confirmButton,
                  !isAnswered && styles.disabledButton
                ]}
                onPress={handleConfirm}
                disabled={!isAnswered}
              >
                <Text style={styles.confirmButtonText}>
                  {currentQuestionIndex === eventQuestions.length - 1 ? 'Complete' : 'Next'}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        );

      default:
        return null;
    }
  };

  const renderServiceQuestion = () => {
    const { selectedServices, serviceIndex, questionIndex } = serviceQuestionFlow;
    const service = selectedServices[serviceIndex];
    const serviceQuestions = eventServiceQuestions[service] || [];
    const currentServiceQuestion = serviceQuestions[questionIndex];
    
    if (!currentServiceQuestion) {
      console.error(`Service question not found for service: ${service}, index: ${questionIndex}`);
      return (
        <View style={styles.content}>
          <Text style={styles.errorText}>Sorry, there was an error loading the service questions.</Text>
        </View>
      );
    }

    const currentAnswer = (serviceQuestionFlow.serviceAnswers[service] || {})[currentServiceQuestion.id];
    const isAnswered = serviceInputValue !== null && serviceInputValue !== undefined && serviceInputValue !== '';
    const showConfirmButton = currentServiceQuestion.type !== 'select';

    switch (currentServiceQuestion.type) {
      case 'select':
        return (
          <ScrollView style={styles.optionsContainer}>
            {currentServiceQuestion.options?.map((option: string) => (
              <TouchableOpacity
                key={option}
                style={[
                  styles.optionButton,
                  currentAnswer === option && styles.selectedOption
                ]}
                onPress={() => handleServiceAnswer(option, 'select')}
              >
                <Text style={[
                  styles.optionText,
                  currentAnswer === option && styles.selectedOptionText
                ]}>
                  {option}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        );
      case 'multiselect':
        return (
          <View style={styles.content}>
            <ScrollView style={styles.optionsContainer}>
              {currentServiceQuestion.options?.map((option: string) => (
                <TouchableOpacity
                  key={option}
                  style={[
                    styles.optionButton,
                    (serviceMultiSelectValue || []).includes(option) && styles.selectedOption
                  ]}
                  onPress={() => {
                    const currentValues = serviceMultiSelectValue || [];
                    const newValues = currentValues.includes(option)
                      ? currentValues.filter((v: string) => v !== option)
                      : [...currentValues, option];
                    setServiceMultiSelectValue(newValues);
                    handleServiceAnswer(newValues);
                  }}
                >
                  <Text style={[
                    styles.optionText,
                    (serviceMultiSelectValue || []).includes(option) && styles.selectedOptionText
                  ]}>
                    {option}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
            {showConfirmButton && (
              <TouchableOpacity
                style={[
                  styles.confirmButton,
                  (!serviceMultiSelectValue || serviceMultiSelectValue.length === 0) && styles.disabledButton
                ]}
                onPress={handleServiceConfirm}
                disabled={!serviceMultiSelectValue || serviceMultiSelectValue.length === 0}
              >
                <Text style={styles.confirmButtonText}>
                  {serviceIndex === selectedServices.length - 1 && questionIndex === serviceQuestions.length - 1 ? 'Complete' : 'Next'}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        );
      case 'number':
        return (
          <View style={styles.content}>
            <TextInput
              style={styles.textInput}
              placeholder={currentServiceQuestion.placeholder}
              value={serviceInputValue !== null && serviceInputValue !== undefined ? String(serviceInputValue) : ''}
              keyboardType="numeric"
              onChangeText={(text) => {
                setServiceInputValue(text);
                handleServiceAnswer(text);
              }}
            />
            {showConfirmButton && (
              <TouchableOpacity
                style={[
                  styles.confirmButton,
                  (!isAnswered) && styles.disabledButton
                ]}
                onPress={handleServiceConfirm}
                disabled={!isAnswered}
              >
                <Text style={styles.confirmButtonText}>
                  {serviceIndex === selectedServices.length - 1 && questionIndex === serviceQuestions.length - 1 ? 'Complete' : 'Next'}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        );
      case 'text':
      default:
        return (
          <View style={styles.content}>
            <TextInput
              style={styles.textInput}
              placeholder={currentServiceQuestion.placeholder}
              value={serviceInputValue !== null && serviceInputValue !== undefined ? serviceInputValue : ''}
              onChangeText={(text) => {
                setServiceInputValue(text);
                handleServiceAnswer(text);
              }}
            />
            {showConfirmButton && (
              <TouchableOpacity
                style={[
                  styles.confirmButton,
                  (!isAnswered) && styles.disabledButton
                ]}
                onPress={handleServiceConfirm}
                disabled={!isAnswered}
              >
                <Text style={styles.confirmButtonText}>
                  {serviceIndex === selectedServices.length - 1 && questionIndex === serviceQuestions.length - 1 ? 'Complete' : 'Next'}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        );
    }
  };

  return (
    <GestureHandlerRootView style={styles.container}>
      <PanGestureHandler
        onGestureEvent={handleGesture}
        activeOffsetX={[-20, 20]}
      >
        <View style={styles.container}>
          {/* Show event or service name at the top */}
          <View style={styles.headerNameContainer}>
            <Text style={styles.headerNameText}>{progressLabel}</Text>
          </View>
          <View style={styles.progressContainer}>
            <Animated.View 
              style={[
                styles.progressBar,
                { 
                  width: progressAnimation.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%']
                  })
                }
              ]} 
            />
          </View>

          <Animated.View
            style={[
              styles.questionContainer,
              {
                opacity: slideAnimation.interpolate({
                  inputRange: [-1, 0, 1],
                  outputRange: [0.3, 1, 0.3]
                }),
                transform: [
                  {
                    translateX: slideAnimation.interpolate({
                      inputRange: [-1, 0, 1],
                      outputRange: [-SCREEN_WIDTH * 0.3, 0, SCREEN_WIDTH * 0.3],
                    }),
                  },
                  {
                    scale: slideAnimation.interpolate({
                      inputRange: [-1, 0, 1],
                      outputRange: [0.9, 1, 0.9]
                    })
                  }
                ],
              },
            ]}
          >
            {serviceQuestionFlow.active ? (
              serviceQuestionFlow.showServiceConfirmation ? (
                <View style={styles.content}>
                  <Text style={styles.questionText}>
                    {`Summary for ${serviceQuestionFlow.selectedServices[serviceQuestionFlow.serviceIndex]}`}
                  </Text>
                  <View style={{ marginBottom: 16 }}>
                    {(() => {
                      const service = serviceQuestionFlow.selectedServices[serviceQuestionFlow.serviceIndex];
                      const serviceQuestions = eventServiceQuestions[service] || [];
                      const answersObj = serviceQuestionFlow.serviceAnswers[service] || {};
                      return serviceQuestions.map((q: Question) => (
                        <View key={q.id} style={{ marginBottom: 8 }}>
                          <Text style={{ fontWeight: 'bold', color: '#333' }}>{q.text}</Text>
                          <Text style={{ color: '#555', marginLeft: 8 }}>
                            {Array.isArray(answersObj[q.id])
                              ? answersObj[q.id].join(', ')
                              : answersObj[q.id] !== undefined && answersObj[q.id] !== null
                                ? (q.type === 'date' && answersObj[q.id] instanceof Date)
                                  ? answersObj[q.id].toLocaleDateString()
                                  : (q.type === 'time' && answersObj[q.id] instanceof Date)
                                    ? answersObj[q.id].toLocaleTimeString()
                                    : String(answersObj[q.id])
                                : '—'}
                          </Text>
                        </View>
                      ));
                    })()}
                  </View>
                  <Text style={styles.questionText}>
                    {`Details saved for ${serviceQuestionFlow.selectedServices[serviceQuestionFlow.serviceIndex]}!`}
                  </Text>
                  <TouchableOpacity
                    style={styles.confirmButton}
                    onPress={handleServiceConfirm}
                  >
                    <Text style={styles.confirmButtonText}>
                      {serviceQuestionFlow.serviceIndex === serviceQuestionFlow.selectedServices.length - 1 ? 'Finish' : 'Continue'}
                    </Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <>
                  <Text style={styles.questionText}>
                    {eventServiceQuestions[serviceQuestionFlow.selectedServices[serviceQuestionFlow.serviceIndex]][serviceQuestionFlow.questionIndex].text}
                  </Text>
                  {renderServiceQuestion()}
                </>
              )
            ) : (
              <>
                <Text style={styles.questionText}>
                  {eventQuestions[currentQuestionIndex].text}
                </Text>
                {renderQuestion()}
              </>
            )}
          </Animated.View>

          {currentQuestionIndex > 0 && !serviceQuestionFlow.active && (
            <TouchableOpacity
              style={styles.backButton}
              onPress={handleBack}
            >
              <Text style={styles.backButtonText}>Back</Text>
            </TouchableOpacity>
          )}
        </View>
      </PanGestureHandler>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  headerNameContainer: {
    paddingTop: 24,
    paddingBottom: 8,
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  headerNameText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ff5a5f',
    letterSpacing: 1,
  },
  progressContainer: {
    paddingTop: 16,
    paddingHorizontal: 20,
  },
  progressBar: {
    height: 3,
    backgroundColor: '#ff5a5f',
    borderRadius: 1.5,
  },
  questionContainer: {
    flex: 1,
    padding: 20,
  },
  questionText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 24,
  },
  content: {
    flex: 1,
  },
  optionsContainer: {
    flex: 1,
  },
  optionButton: {
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#f8f8f8',
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  selectedOption: {
    backgroundColor: '#ff5a5f',
    borderColor: '#ff5a5f',
  },
  optionText: {
    fontSize: 16,
    color: '#333',
  },
  selectedOptionText: {
    color: '#fff',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    marginBottom: 16,
  },
  dateButton: {
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#f8f8f8',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  dateButtonText: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
  },
  confirmButton: {
    backgroundColor: '#ff5a5f',
    padding: 16,
    borderRadius: 24,
    alignItems: 'center',
    marginTop: 16,
    marginHorizontal: 20,
  },
  confirmButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.5,
  },
  backButton: {
    padding: 16,
    alignItems: 'center',
  },
  backButtonText: {
    color: '#666',
    fontSize: 16,
  },
  dateInputContainer: {
    flex: 1,
  },
  pickerContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginTop: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  picker: {
    height: 200,
    width: '100%',
  },
  pickerButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 8,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  pickerButton: {
    padding: 8,
    minWidth: 80,
    alignItems: 'center',
  },
  pickerButtonPrimary: {
    backgroundColor: '#ff5a5f',
    borderRadius: 8,
  },
  pickerButtonText: {
    fontSize: 16,
    color: '#666',
  },
  pickerButtonTextPrimary: {
    color: '#fff',
    fontWeight: '600',
  },
  errorText: {
    fontSize: 16,
    color: '#ff5a5f',
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: 20,
  },
});

export default EventQuestionnaire; 