eventapp

## Getting Started

### Prerequisites
- Node.js (latest LTS recommended)
- npm (comes with Node.js) or yarn
- Expo CLI (install globally if you don't have it):
  ```sh
  npm install -g expo-cli
  ```

### Install dependencies
```sh
npm install
```

### Run the app with Expo
Start the Expo development server:
```sh
npm run dev
```

- This will open Expo Dev Tools in your browser.
- You can run the app on:
  - iOS: Press `i` in the terminal (requires Xcode or Expo Go app)
  - Android: Press `a` in the terminal (requires Android Studio or Expo Go app)
  - Web: Press `w` in the terminal or use the web preview in Expo Dev Tools

### Run Expo with Tunnel (for remote device access)
To make your local Expo server accessible from any device/network, start Expo with the tunnel option:
```sh
npx expo start --clear --tunnel
```

- This is useful if your mobile device is on a different network than your computer.
- In Expo Dev Tools, you can also select "Connection" → "Tunnel".
