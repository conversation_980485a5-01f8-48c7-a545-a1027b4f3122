import React, { useState, useEffect, useCallback } from 'react';
import { Check, Setting<PERSON>, Sparkles } from 'lucide-react';

const GiftsForm = ({ onSubmit, previousData, validateRef, useContinueButton = false }) => {
  const [formData, setFormData] = useState({
    giftType: '',
    quantity: '',
    giftPreference: '',
    budgetPerPiece: '',
    packagingType: '',
    specialRequests: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [showSuccess, setShowSuccess] = useState(false);

  // Initialize form data with previous data if available
  useEffect(() => {
    if (previousData) {
      setFormData(prevData => ({
        ...prevData,
        ...previousData
      }));
    }
  }, [previousData]);

  // Validate form and submit silently (for continue button)
  // Using useCallback to memoize the function and avoid circular dependencies
  const validateAndSubmitSilent = useCallback(() => {
    const errors = {};

    // Check required fields
    if (!formData.giftType) errors.giftType = 'Please select a gift type';
    if (!formData.quantity) errors.quantity = 'Please enter the quantity';
    if (!formData.giftPreference) errors.giftPreference = 'Please select a gift preference';
    if (!formData.budgetPerPiece) errors.budgetPerPiece = 'Please select a budget per piece';

    if (Object.keys(errors).length === 0) {
      // Form is valid, submit
      onSubmit({
        serviceDetails: formData,
        category: 'Gifts',
        name: 'Gifts & Favors Service'
      });

      // Return true to indicate validation passed
      return true;
    } else {
      // Form has errors
      setFormErrors(errors);

      // Return false to indicate validation failed
      return false;
    }
  }, [formData, onSubmit, setFormErrors]);

  // Register the validation function with the parent component
  useEffect(() => {
    if (validateRef && useContinueButton) {
      validateRef(() => {
        // Return the result of validateAndSubmit
        return validateAndSubmitSilent();
      });
    }
  }, [validateRef, useContinueButton, validateAndSubmitSilent]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (type === 'checkbox') {
      if (checked) {
        setFormData({
          ...formData,
          [name]: [...(formData[name] || []), value]
        });
      } else {
        setFormData({
          ...formData,
          [name]: (formData[name] || []).filter(item => item !== value)
        });
      }
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };



  // Validate form and submit with visual feedback
  const validateAndSubmit = () => {
    const isValid = validateAndSubmitSilent();

    if (isValid) {
      // Show success message
      setShowSuccess(true);

      // Hide success message after 3 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }

    return isValid;
  };

  return (
    <div className="bg-white dark:bg-dark-secondary p-6 rounded-lg shadow-md">
      {showSuccess && (
        <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg text-green-700 dark:text-green-300 flex items-center">
          <Check className="mr-2" size={18} />
          Gifts & favors details saved successfully!
        </div>
      )}

      <p className="mb-6 text-light-secondary dark:text-gray-400 flex items-center">
        <Settings className="mr-2 text-[#ff5a5f]" size={16} />
        Please provide details for your gifts & favors requirements
      </p>

      <div className="space-y-6">
        {/* Gift Type */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Gift Type <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="relative">
            <select
              name="giftType"
              value={formData.giftType || ''}
              onChange={handleInputChange}
              className={`w-full p-3 rounded-lg border ${formErrors.giftType ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none`}
            >
              <option value="">Select Gift Type</option>
              <option value="Return Gifts for Guests">Return Gifts for Guests</option>
              <option value="VIP Gifts">VIP Gifts</option>
              <option value="Welcome Hampers">Welcome Hampers</option>
              <option value="Thank You Gifts">Thank You Gifts</option>
              <option value="Custom">Custom</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
            {formErrors.giftType && (
              <p className="mt-1 text-sm text-red-500">{formErrors.giftType}</p>
            )}
          </div>
        </div>

        {/* Quantity */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Quantity <span className="text-[#ff5a5f]">*</span>
          </label>
          <input
            type="number"
            name="quantity"
            value={formData.quantity || ''}
            onChange={handleInputChange}
            placeholder="Number of gifts needed"
            min="1"
            className={`w-full p-3 rounded-lg border ${formErrors.quantity ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                     bg-white dark:bg-dark-primary text-light-primary dark:text-white
                     focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent`}
          />
          {formErrors.quantity && (
            <p className="mt-1 text-sm text-red-500">{formErrors.quantity}</p>
          )}
        </div>

        {/* Gift Preference */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Gift Preference <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="relative">
            <select
              name="giftPreference"
              value={formData.giftPreference || ''}
              onChange={handleInputChange}
              className={`w-full p-3 rounded-lg border ${formErrors.giftPreference ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none`}
            >
              <option value="">Select Gift Preference</option>
              <option value="Traditional">Traditional</option>
              <option value="Practical/Useful">Practical/Useful</option>
              <option value="Decorative">Decorative</option>
              <option value="Edible/Sweets">Edible/Sweets</option>
              <option value="Personalized">Personalized</option>
              <option value="Eco-friendly">Eco-friendly</option>
              <option value="Luxury">Luxury</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
            {formErrors.giftPreference && (
              <p className="mt-1 text-sm text-red-500">{formErrors.giftPreference}</p>
            )}
          </div>
        </div>

        {/* Budget Per Piece */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Budget Per Piece <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="relative">
            <select
              name="budgetPerPiece"
              value={formData.budgetPerPiece || ''}
              onChange={handleInputChange}
              className={`w-full p-3 rounded-lg border ${formErrors.budgetPerPiece ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none`}
            >
              <option value="">Select Budget Per Piece</option>
              <option value="Under ₹200">Under ₹200</option>
              <option value="₹200-₹500">₹200-₹500</option>
              <option value="₹500-₹1,000">₹500-₹1,000</option>
              <option value="₹1,000-₹2,000">₹1,000-₹2,000</option>
              <option value="Above ₹2,000">Above ₹2,000</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
            {formErrors.budgetPerPiece && (
              <p className="mt-1 text-sm text-red-500">{formErrors.budgetPerPiece}</p>
            )}
          </div>
        </div>

        {/* Packaging Type */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Packaging Type
          </label>
          <div className="relative">
            <select
              name="packagingType"
              value={formData.packagingType || ''}
              onChange={handleInputChange}
              className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none"
            >
              <option value="">Select Packaging Type</option>
              <option value="Simple Wrapping">Simple Wrapping</option>
              <option value="Gift Boxes">Gift Boxes</option>
              <option value="Decorative Bags">Decorative Bags</option>
              <option value="Themed Packaging">Themed Packaging</option>
              <option value="Eco-friendly Packaging">Eco-friendly Packaging</option>
              <option value="Luxury Packaging">Luxury Packaging</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
          </div>
        </div>

        {/* Special Requests */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Special Requests
          </label>
          <textarea
            name="specialRequests"
            value={formData.specialRequests || ''}
            onChange={handleInputChange}
            placeholder="Any specific gift requirements or ideas..."
            rows="3"
            className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                     bg-white dark:bg-dark-primary text-light-primary dark:text-white
                     focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent"
          />
        </div>
      </div>

      {/* Only show the save button if not using the continue button */}
      {!useContinueButton && (
        <div className="mt-8 flex justify-end">
          <button
            type="button"
            onClick={validateAndSubmit}
            className="px-6 py-3 rounded-lg bg-gradient-to-r from-[#ff5a5f] to-[#ff8c8f] text-white font-medium hover:shadow-md transition-all duration-300 flex items-center"
          >
            {previousData ? (
              <>
                <Check className="mr-2" size={18} />
                Update Gifts & Favors Details
              </>
            ) : (
              <>
                <Sparkles className="mr-2" size={18} />
                Save Gifts & Favors Details
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default GiftsForm;
