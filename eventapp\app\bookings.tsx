import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Platform, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';

interface Booking {
  id: number;
  eventType: string;
  selections: {
    basicDetails?: {
      eventDate?: string;
      eventTime?: string;
      eventLocation?: string;
      numberOfPersons?: string;
    };
    [key: string]: any;
  };
  totalAmount: number;
  status: string;
  date: string;
}

export default function BookingsPage() {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadBookings = async () => {
      try {
        let storedBookings;
        if (Platform.OS === 'web') {
          const bookingsStr = localStorage.getItem('bookings');
          storedBookings = bookingsStr ? JSON.parse(bookingsStr) : [];
        } else {
          const bookingsStr = await AsyncStorage.getItem('bookings');
          storedBookings = bookingsStr ? JSON.parse(bookingsStr) : [];
        }
        const validBookings = storedBookings.filter((booking: any) => {
          return booking && 
                 typeof booking === 'object' && 
                 booking.id && 
                 booking.eventType && 
                 booking.selections;
        });
        setBookings(validBookings);
      } catch (error) {
        console.error('Error loading bookings:', error);
        setBookings([]);
      } finally {
        setLoading(false);
      }
    };

    loadBookings();
  }, []);

  const renderServiceDetails = (serviceName: string, serviceAnswers: any): React.ReactElement | null => {
    if (!serviceAnswers) return null;

    const details = Object.entries(serviceAnswers)
      .filter(([_, value]) => value !== undefined && value !== null)
      .map(([key, value]) => {
        // Format the key for display
        const displayKey = key
          .replace(/([A-Z])/g, ' $1') // Add space before capital letters
          .replace(/^./, str => str.toUpperCase()) // Capitalize first letter
          .trim();

        // Format the value for display
        let displayValue = value;
        if (Array.isArray(value)) {
          displayValue = value.join(', ');
        } else if (typeof value === 'boolean') {
          displayValue = value ? 'Yes' : 'No';
        }

        return (
          <View key={key} style={styles.serviceDetail}>
            <Text style={styles.serviceDetailLabel}>{displayKey}:</Text>
            <Text style={styles.serviceDetailValue}>{String(displayValue)}</Text>
          </View>
        );
      });

    if (details.length === 0) return null;

    return (
      <View style={styles.serviceSection}>
        <Text style={styles.serviceTitle}>{serviceName}</Text>
        <View style={styles.serviceDetailsContainer}>{details}</View>
      </View>
    );
  };

  const renderBookingDetails = (booking: Booking) => {
    const basicDetails = booking.selections?.basicDetails || {};
    const serviceDetails = Object.entries(booking.selections || {})
      .filter(([key]) => key !== 'basicDetails');

    return (
      <View style={styles.bookingDetails}>
        <View style={styles.basicDetailsSection}>
          <Text style={styles.sectionTitle}>Basic Details</Text>
          <View style={styles.basicDetailsContainer}>
            {basicDetails.eventDate && (
              <Text style={styles.detailText}>
                Date: {basicDetails.eventDate}
              </Text>
            )}
            {basicDetails.eventTime && (
              <Text style={styles.detailText}>
                Time: {basicDetails.eventTime}
              </Text>
            )}
            {basicDetails.eventLocation && (
              <Text style={styles.detailText}>
                Location: {basicDetails.eventLocation}
              </Text>
            )}
            {basicDetails.numberOfPersons && (
              <Text style={styles.detailText}>
                Guests: {basicDetails.numberOfPersons}
              </Text>
            )}
          </View>
        </View>

        {serviceDetails.length > 0 && (
          <View style={styles.servicesSection}>
            <Text style={styles.sectionTitle}>Service Details</Text>
            <View style={styles.servicesContainer}>
              {serviceDetails.map(([serviceName, serviceAnswers]) => (
                <View key={serviceName}>
                  {renderServiceDetails(serviceName, serviceAnswers)}
                </View>
              ))}
            </View>
          </View>
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading bookings...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.push('/(tabs)/profile')}
        >
          <Ionicons 
            name={Platform.OS === 'ios' ? 'chevron-back' : 'arrow-back'} 
            size={24} 
            color="#333"
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>My Bookings</Text>
        <View style={styles.backButton} /> {/* Spacer for alignment */}
      </View>

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollViewContent}>
        {bookings.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No bookings found</Text>
            <Text style={styles.emptySubText}>Your bookings will appear here</Text>
          </View>
        ) : (
          <View style={styles.bookingsContainer}>
            {bookings.map((booking) => (
              <View key={booking.id} style={styles.bookingCard}>
                <View style={styles.bookingHeader}>
                  <Text style={styles.eventType}>{booking.eventType}</Text>
                  <Text style={styles.bookingDate}>
                    {new Date(booking.date).toLocaleDateString()}
                  </Text>
                </View>
                
                {renderBookingDetails(booking)}

                <View style={styles.bookingFooter}>
                  <Text style={styles.statusText}>Status: {booking.status}</Text>
                  <Text style={styles.amountText}>
                    Amount: ₹{booking.totalAmount?.toLocaleString() || '0'}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: Platform.OS === 'web' ? 24 : 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    minHeight: 60,
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    textAlign: 'center',
    paddingHorizontal: 16,
    marginHorizontal: 8,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  emptySubText: {
    fontSize: 16,
    color: '#666',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 20,
  },
  bookingCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  bookingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  eventType: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  bookingDate: {
    fontSize: 14,
    color: '#666',
  },
  bookingDetails: {
    marginBottom: 12,
  },
  basicDetailsSection: {
    marginBottom: 16,
  },
  servicesSection: {
    marginTop: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  serviceSection: {
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  serviceTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#ff5a5f',
    marginBottom: 8,
  },
  serviceDetail: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  serviceDetailLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
    flex: 1,
  },
  serviceDetailValue: {
    fontSize: 14,
    color: '#333',
    flex: 2,
  },
  detailText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  bookingFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    paddingTop: 12,
  },
  statusText: {
    fontSize: 14,
    color: '#ff5a5f',
    fontWeight: '600',
  },
  amountText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '600',
  },
  serviceDetailsContainer: {
    flex: 1,
  },
  basicDetailsContainer: {
    flex: 1,
  },
  servicesContainer: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  bookingsContainer: {
    flex: 1,
  },
}); 