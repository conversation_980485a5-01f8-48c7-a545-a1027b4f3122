import React from 'react';
import { View, StyleSheet, Dimensions, Platform } from 'react-native';
import WebView from './WebView';

interface ResponsiveWebViewProps {
  uri: string;
  style?: any;
}

export default function ResponsiveWebView({ uri, style }: ResponsiveWebViewProps) {
  const screenWidth = Dimensions.get('window').width;
  const isWeb = Platform.OS === 'web';
  
  // Calculate dimensions based on Instagram's 4:5 aspect ratio
  const maxWidth = Math.min(screenWidth * 0.9, 600); // Max width of 600px or 90% of screen width
  const height = maxWidth * (5/4); // 4:5 aspect ratio

  if (isWeb) {
    // For web platform, use an iframe with the same dimensions
    return (
      <View style={[styles.container, { width: maxWidth, height }, style]}>
        <iframe
          src={uri}
          style={{
            width: '100%',
            height: '100%',
            border: 'none',
            borderRadius: '8px',
          }}
          title="Web Content"
        />
      </View>
    );
  }

  // For native platforms, use the WebView component
  return <WebView uri={uri} style={style} />;
}

const styles = StyleSheet.create({
  container: {
    alignSelf: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
    marginVertical: 16,
  },
}); 