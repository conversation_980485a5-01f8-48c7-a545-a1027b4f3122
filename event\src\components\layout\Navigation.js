import React from 'react';
import { Link } from 'react-router-dom';

const Navigation = ({ menuOpen, handleNavClick }) => {
  const navLinks = [
    { to: "/events", text: "Events" },
    { to: "/services", text: "Services" },
    { to: "/blogs", text: "Blogs" },
    { to: "/e-invites", text: "E-invites" },
    { to: "/planner", text: "Personal Planner" },
  ];

  return (
    <nav className={`absolute md:static top-16 left-0 w-full md:w-auto bg-light-primary dark:bg-dark-primary md:bg-transparent
      ${menuOpen ? "block" : "hidden"} md:flex items-center md:space-x-8 p-4 md:p-0 z-50`}>
      {navLinks.map((link, index) => (
        <Link
          key={index}
          to={link.to}
          className="text-light-primary dark:text-white font-medium hover:text-warm-orange block md:inline py-2 md:py-0 transition-colors"
          onClick={handleNavClick}
        >
          {link.text}
        </Link>
      ))}
    </nav>
  );
};

export default Navigation;