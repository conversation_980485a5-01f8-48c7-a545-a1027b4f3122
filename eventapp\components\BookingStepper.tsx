import React from 'react';
import { View, Text, StyleSheet, Animated, TouchableOpacity } from 'react-native';

interface Step {
  title: string;
  category: string;
  options: any[];
}

interface BookingStepperProps {
  steps: Step[];
  currentStep: number;
  onStepPress?: (index: number) => void;
}

const BookingStepper: React.FC<BookingStepperProps> = ({ steps, currentStep, onStepPress }) => {
  return (
    <View style={styles.container}>
      <View style={styles.stepperContainer}>
        {steps.map((step, index) => (
          <React.Fragment key={index}>
            <TouchableOpacity 
              style={styles.stepContainer}
              onPress={() => onStepPress && index < currentStep && onStepPress(index)}
              disabled={index > currentStep}
            >
              <View style={[
                styles.stepCircle,
                index <= currentStep ? styles.activeStep : styles.inactiveStep,
                { transform: [{ scale: index === currentStep ? 1.1 : 1 }] }
              ]}>
                <Text style={[
                  styles.stepNumber,
                  index <= currentStep ? styles.activeStepText : styles.inactiveStepText
                ]}>
                  {index < currentStep ? '✓' : (index + 1)}
                </Text>
              </View>
              <Text style={[
                styles.stepTitle,
                index <= currentStep ? styles.activeStepText : styles.inactiveStepText
              ]}>
                {step.title}
              </Text>
              {index < steps.length - 1 && (
                <View style={[
                  styles.connector,
                  index < currentStep ? styles.activeConnector : styles.inactiveConnector
                ]} />
              )}
            </TouchableOpacity>
          </React.Fragment>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
  },
  stepperContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  stepContainer: {
    alignItems: 'center',
    flex: 1,
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  activeStep: {
    backgroundColor: '#ff5a5f',
  },
  inactiveStep: {
    backgroundColor: '#e0e0e0',
  },
  stepNumber: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  activeStepText: {
    color: '#ff5a5f',
  },
  inactiveStepText: {
    color: '#9e9e9e',
  },
  stepTitle: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 4,
  },
  connector: {
    position: 'absolute',
    top: 16,
    right: -50,
    width: 100,
    height: 2,
  },
  activeConnector: {
    backgroundColor: '#ff5a5f',
  },
  inactiveConnector: {
    backgroundColor: '#e0e0e0',
  },
});

export default BookingStepper; 