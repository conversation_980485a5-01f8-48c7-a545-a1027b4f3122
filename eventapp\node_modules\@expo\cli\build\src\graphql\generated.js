/**
 * eslint-disable
 * This file was generated using GraphQL Codegen
 * Command: yarn generate-graphql-code
 * Run this during development for automatic type generation when editing GraphQL documents
 * For more info and docs, visit https://graphql-code-generator.com/
 */ "use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    AccountAppsSortByField: function() {
        return AccountAppsSortByField;
    },
    AccountUploadSessionType: function() {
        return AccountUploadSessionType;
    },
    ActivityTimelineProjectActivityType: function() {
        return ActivityTimelineProjectActivityType;
    },
    AndroidBuildType: function() {
        return AndroidBuildType;
    },
    AndroidFcmVersion: function() {
        return AndroidFcmVersion;
    },
    AndroidKeystoreType: function() {
        return AndroidKeystoreType;
    },
    AppInternalDistributionBuildPrivacy: function() {
        return AppInternalDistributionBuildPrivacy;
    },
    AppPlatform: function() {
        return AppPlatform;
    },
    AppPrivacy: function() {
        return AppPrivacy;
    },
    AppSort: function() {
        return AppSort;
    },
    AppStoreConnectUserRole: function() {
        return AppStoreConnectUserRole;
    },
    AppUploadSessionType: function() {
        return AppUploadSessionType;
    },
    AppleDeviceClass: function() {
        return AppleDeviceClass;
    },
    AppleTeamType: function() {
        return AppleTeamType;
    },
    AppsFilter: function() {
        return AppsFilter;
    },
    AssetMetadataStatus: function() {
        return AssetMetadataStatus;
    },
    AuditLogsExportFormat: function() {
        return AuditLogsExportFormat;
    },
    AuthProtocolType: function() {
        return AuthProtocolType;
    },
    AuthProviderIdentifier: function() {
        return AuthProviderIdentifier;
    },
    BackgroundJobResultType: function() {
        return BackgroundJobResultType;
    },
    BackgroundJobState: function() {
        return BackgroundJobState;
    },
    BuildCredentialsSource: function() {
        return BuildCredentialsSource;
    },
    BuildIosEnterpriseProvisioning: function() {
        return BuildIosEnterpriseProvisioning;
    },
    BuildLimitThresholdExceededMetadataType: function() {
        return BuildLimitThresholdExceededMetadataType;
    },
    BuildMode: function() {
        return BuildMode;
    },
    BuildPhase: function() {
        return BuildPhase;
    },
    BuildPriority: function() {
        return BuildPriority;
    },
    BuildResourceClass: function() {
        return BuildResourceClass;
    },
    BuildRetryDisabledReason: function() {
        return BuildRetryDisabledReason;
    },
    BuildStatus: function() {
        return BuildStatus;
    },
    BuildTrigger: function() {
        return BuildTrigger;
    },
    BuildWorkflow: function() {
        return BuildWorkflow;
    },
    ContinentCode: function() {
        return ContinentCode;
    },
    CrashSampleFor: function() {
        return CrashSampleFor;
    },
    CustomDomainDnsRecordType: function() {
        return CustomDomainDnsRecordType;
    },
    CustomDomainStatus: function() {
        return CustomDomainStatus;
    },
    DistributionType: function() {
        return DistributionType;
    },
    EasBuildBillingResourceClass: function() {
        return EasBuildBillingResourceClass;
    },
    EasBuildDeprecationInfoType: function() {
        return EasBuildDeprecationInfoType;
    },
    EasBuildWaiverType: function() {
        return EasBuildWaiverType;
    },
    EasService: function() {
        return EasService;
    },
    EasServiceMetric: function() {
        return EasServiceMetric;
    },
    EasTotalPlanEnablementUnit: function() {
        return EasTotalPlanEnablementUnit;
    },
    EntityTypeName: function() {
        return EntityTypeName;
    },
    EnvironmentSecretType: function() {
        return EnvironmentSecretType;
    },
    EnvironmentVariableEnvironment: function() {
        return EnvironmentVariableEnvironment;
    },
    EnvironmentVariableScope: function() {
        return EnvironmentVariableScope;
    },
    EnvironmentVariableVisibility: function() {
        return EnvironmentVariableVisibility;
    },
    Experiment: function() {
        return Experiment;
    },
    Feature: function() {
        return Feature;
    },
    FingerprintSourceType: function() {
        return FingerprintSourceType;
    },
    GitHubAppEnvironment: function() {
        return GitHubAppEnvironment;
    },
    GitHubAppInstallationStatus: function() {
        return GitHubAppInstallationStatus;
    },
    GitHubBuildTriggerExecutionBehavior: function() {
        return GitHubBuildTriggerExecutionBehavior;
    },
    GitHubBuildTriggerRunStatus: function() {
        return GitHubBuildTriggerRunStatus;
    },
    GitHubBuildTriggerType: function() {
        return GitHubBuildTriggerType;
    },
    GitHubJobRunJobType: function() {
        return GitHubJobRunJobType;
    },
    GitHubJobRunTriggerRunStatus: function() {
        return GitHubJobRunTriggerRunStatus;
    },
    GitHubJobRunTriggerType: function() {
        return GitHubJobRunTriggerType;
    },
    InsightsFilterType: function() {
        return InsightsFilterType;
    },
    InvoiceDiscountType: function() {
        return InvoiceDiscountType;
    },
    IosBuildType: function() {
        return IosBuildType;
    },
    IosDistributionType: function() {
        return IosDistributionType;
    },
    IosManagedBuildType: function() {
        return IosManagedBuildType;
    },
    IosSchemeBuildConfiguration: function() {
        return IosSchemeBuildConfiguration;
    },
    JobRunPriority: function() {
        return JobRunPriority;
    },
    JobRunStatus: function() {
        return JobRunStatus;
    },
    MailchimpAudience: function() {
        return MailchimpAudience;
    },
    MailchimpTag: function() {
        return MailchimpTag;
    },
    NotificationEvent: function() {
        return NotificationEvent;
    },
    NotificationType: function() {
        return NotificationType;
    },
    OfferType: function() {
        return OfferType;
    },
    OnboardingDeviceType: function() {
        return OnboardingDeviceType;
    },
    OnboardingEnvironment: function() {
        return OnboardingEnvironment;
    },
    Order: function() {
        return Order;
    },
    Permission: function() {
        return Permission;
    },
    ProjectArchiveSourceType: function() {
        return ProjectArchiveSourceType;
    },
    RequestMethod: function() {
        return RequestMethod;
    },
    RequestsOrderByDirection: function() {
        return RequestsOrderByDirection;
    },
    RequestsOrderByField: function() {
        return RequestsOrderByField;
    },
    ResourceClassExperiment: function() {
        return ResourceClassExperiment;
    },
    ResponseCacheStatus: function() {
        return ResponseCacheStatus;
    },
    ResponseStatusType: function() {
        return ResponseStatusType;
    },
    ResponseType: function() {
        return ResponseType;
    },
    Role: function() {
        return Role;
    },
    SecondFactorMethod: function() {
        return SecondFactorMethod;
    },
    StandardOffer: function() {
        return StandardOffer;
    },
    StatuspageIncidentImpact: function() {
        return StatuspageIncidentImpact;
    },
    StatuspageIncidentStatus: function() {
        return StatuspageIncidentStatus;
    },
    StatuspageServiceName: function() {
        return StatuspageServiceName;
    },
    StatuspageServiceStatus: function() {
        return StatuspageServiceStatus;
    },
    SubmissionAndroidArchiveType: function() {
        return SubmissionAndroidArchiveType;
    },
    SubmissionAndroidReleaseStatus: function() {
        return SubmissionAndroidReleaseStatus;
    },
    SubmissionAndroidTrack: function() {
        return SubmissionAndroidTrack;
    },
    SubmissionArchiveSourceType: function() {
        return SubmissionArchiveSourceType;
    },
    SubmissionPriority: function() {
        return SubmissionPriority;
    },
    SubmissionStatus: function() {
        return SubmissionStatus;
    },
    TargetEntityMutationType: function() {
        return TargetEntityMutationType;
    },
    UploadSessionType: function() {
        return UploadSessionType;
    },
    UsageMetricType: function() {
        return UsageMetricType;
    },
    UsageMetricsGranularity: function() {
        return UsageMetricsGranularity;
    },
    UserAgentBrowser: function() {
        return UserAgentBrowser;
    },
    UserAgentOs: function() {
        return UserAgentOs;
    },
    UserEntityTypeName: function() {
        return UserEntityTypeName;
    },
    WebhookType: function() {
        return WebhookType;
    },
    WorkerDeploymentCrashKind: function() {
        return WorkerDeploymentCrashKind;
    },
    WorkerDeploymentLogLevel: function() {
        return WorkerDeploymentLogLevel;
    },
    WorkerLoggerLevel: function() {
        return WorkerLoggerLevel;
    },
    WorkflowJobStatus: function() {
        return WorkflowJobStatus;
    },
    WorkflowJobType: function() {
        return WorkflowJobType;
    },
    WorkflowProjectSourceType: function() {
        return WorkflowProjectSourceType;
    },
    WorkflowRunStatus: function() {
        return WorkflowRunStatus;
    },
    WorkflowRunTriggerEventType: function() {
        return WorkflowRunTriggerEventType;
    }
});
var AccountAppsSortByField = /*#__PURE__*/ function(AccountAppsSortByField) {
    AccountAppsSortByField["LatestActivityTime"] = "LATEST_ACTIVITY_TIME";
    /**
   * Name prefers the display name but falls back to full_name with @account/
   * part stripped.
   */ AccountAppsSortByField["Name"] = "NAME";
    return AccountAppsSortByField;
}({});
var AccountUploadSessionType = /*#__PURE__*/ function(AccountUploadSessionType) {
    AccountUploadSessionType["ProfileImageUpload"] = "PROFILE_IMAGE_UPLOAD";
    AccountUploadSessionType["WorkflowsProjectSources"] = "WORKFLOWS_PROJECT_SOURCES";
    return AccountUploadSessionType;
}({});
var ActivityTimelineProjectActivityType = /*#__PURE__*/ function(ActivityTimelineProjectActivityType) {
    ActivityTimelineProjectActivityType["Build"] = "BUILD";
    ActivityTimelineProjectActivityType["Submission"] = "SUBMISSION";
    ActivityTimelineProjectActivityType["Update"] = "UPDATE";
    ActivityTimelineProjectActivityType["Worker"] = "WORKER";
    ActivityTimelineProjectActivityType["WorkflowRun"] = "WORKFLOW_RUN";
    return ActivityTimelineProjectActivityType;
}({});
var AndroidBuildType = /*#__PURE__*/ function(AndroidBuildType) {
    AndroidBuildType["Apk"] = "APK";
    AndroidBuildType["AppBundle"] = "APP_BUNDLE";
    /** @deprecated Use developmentClient option instead. */ AndroidBuildType["DevelopmentClient"] = "DEVELOPMENT_CLIENT";
    return AndroidBuildType;
}({});
var AndroidFcmVersion = /*#__PURE__*/ function(AndroidFcmVersion) {
    AndroidFcmVersion["Legacy"] = "LEGACY";
    AndroidFcmVersion["V1"] = "V1";
    return AndroidFcmVersion;
}({});
var AndroidKeystoreType = /*#__PURE__*/ function(AndroidKeystoreType) {
    AndroidKeystoreType["Jks"] = "JKS";
    AndroidKeystoreType["Pkcs12"] = "PKCS12";
    AndroidKeystoreType["Unknown"] = "UNKNOWN";
    return AndroidKeystoreType;
}({});
var AppInternalDistributionBuildPrivacy = /*#__PURE__*/ function(AppInternalDistributionBuildPrivacy) {
    AppInternalDistributionBuildPrivacy["Private"] = "PRIVATE";
    AppInternalDistributionBuildPrivacy["Public"] = "PUBLIC";
    return AppInternalDistributionBuildPrivacy;
}({});
var AppPlatform = /*#__PURE__*/ function(AppPlatform) {
    AppPlatform["Android"] = "ANDROID";
    AppPlatform["Ios"] = "IOS";
    return AppPlatform;
}({});
var AppPrivacy = /*#__PURE__*/ function(AppPrivacy) {
    AppPrivacy["Hidden"] = "HIDDEN";
    AppPrivacy["Public"] = "PUBLIC";
    AppPrivacy["Unlisted"] = "UNLISTED";
    return AppPrivacy;
}({});
var AppSort = /*#__PURE__*/ function(AppSort) {
    /** Sort by recently published */ AppSort["RecentlyPublished"] = "RECENTLY_PUBLISHED";
    /** Sort by highest trendScore */ AppSort["Viewed"] = "VIEWED";
    return AppSort;
}({});
var AppStoreConnectUserRole = /*#__PURE__*/ function(AppStoreConnectUserRole) {
    AppStoreConnectUserRole["AccessToReports"] = "ACCESS_TO_REPORTS";
    AppStoreConnectUserRole["AccountHolder"] = "ACCOUNT_HOLDER";
    AppStoreConnectUserRole["Admin"] = "ADMIN";
    AppStoreConnectUserRole["AppManager"] = "APP_MANAGER";
    AppStoreConnectUserRole["CloudManagedAppDistribution"] = "CLOUD_MANAGED_APP_DISTRIBUTION";
    AppStoreConnectUserRole["CloudManagedDeveloperId"] = "CLOUD_MANAGED_DEVELOPER_ID";
    AppStoreConnectUserRole["CreateApps"] = "CREATE_APPS";
    AppStoreConnectUserRole["CustomerSupport"] = "CUSTOMER_SUPPORT";
    AppStoreConnectUserRole["Developer"] = "DEVELOPER";
    AppStoreConnectUserRole["Finance"] = "FINANCE";
    AppStoreConnectUserRole["ImageManager"] = "IMAGE_MANAGER";
    AppStoreConnectUserRole["Marketing"] = "MARKETING";
    AppStoreConnectUserRole["ReadOnly"] = "READ_ONLY";
    AppStoreConnectUserRole["Sales"] = "SALES";
    AppStoreConnectUserRole["Technical"] = "TECHNICAL";
    AppStoreConnectUserRole["Unknown"] = "UNKNOWN";
    return AppStoreConnectUserRole;
}({});
var AppUploadSessionType = /*#__PURE__*/ function(AppUploadSessionType) {
    AppUploadSessionType["ProfileImageUpload"] = "PROFILE_IMAGE_UPLOAD";
    return AppUploadSessionType;
}({});
var AppleDeviceClass = /*#__PURE__*/ function(AppleDeviceClass) {
    AppleDeviceClass["Ipad"] = "IPAD";
    AppleDeviceClass["Iphone"] = "IPHONE";
    AppleDeviceClass["Mac"] = "MAC";
    AppleDeviceClass["Unknown"] = "UNKNOWN";
    return AppleDeviceClass;
}({});
var AppleTeamType = /*#__PURE__*/ function(AppleTeamType) {
    AppleTeamType["CompanyOrOrganization"] = "COMPANY_OR_ORGANIZATION";
    AppleTeamType["Individual"] = "INDIVIDUAL";
    AppleTeamType["InHouse"] = "IN_HOUSE";
    return AppleTeamType;
}({});
var AppsFilter = /*#__PURE__*/ function(AppsFilter) {
    /** Featured Projects */ AppsFilter["Featured"] = "FEATURED";
    /** New Projects */ AppsFilter["New"] = "NEW";
    return AppsFilter;
}({});
var AssetMetadataStatus = /*#__PURE__*/ function(AssetMetadataStatus) {
    AssetMetadataStatus["DoesNotExist"] = "DOES_NOT_EXIST";
    AssetMetadataStatus["Exists"] = "EXISTS";
    return AssetMetadataStatus;
}({});
var AuditLogsExportFormat = /*#__PURE__*/ function(AuditLogsExportFormat) {
    AuditLogsExportFormat["Csv"] = "CSV";
    AuditLogsExportFormat["Json"] = "JSON";
    AuditLogsExportFormat["Jsonl"] = "JSONL";
    return AuditLogsExportFormat;
}({});
var AuthProtocolType = /*#__PURE__*/ function(AuthProtocolType) {
    AuthProtocolType["Oidc"] = "OIDC";
    return AuthProtocolType;
}({});
var AuthProviderIdentifier = /*#__PURE__*/ function(AuthProviderIdentifier) {
    AuthProviderIdentifier["GoogleWs"] = "GOOGLE_WS";
    AuthProviderIdentifier["MsEntraId"] = "MS_ENTRA_ID";
    AuthProviderIdentifier["Okta"] = "OKTA";
    AuthProviderIdentifier["OneLogin"] = "ONE_LOGIN";
    AuthProviderIdentifier["StubIdp"] = "STUB_IDP";
    return AuthProviderIdentifier;
}({});
var BackgroundJobResultType = /*#__PURE__*/ function(BackgroundJobResultType) {
    BackgroundJobResultType["AuditLogsExport"] = "AUDIT_LOGS_EXPORT";
    BackgroundJobResultType["GithubBuild"] = "GITHUB_BUILD";
    BackgroundJobResultType["UserAuditLogsExport"] = "USER_AUDIT_LOGS_EXPORT";
    BackgroundJobResultType["Void"] = "VOID";
    return BackgroundJobResultType;
}({});
var BackgroundJobState = /*#__PURE__*/ function(BackgroundJobState) {
    BackgroundJobState["Failure"] = "FAILURE";
    BackgroundJobState["InProgress"] = "IN_PROGRESS";
    BackgroundJobState["Queued"] = "QUEUED";
    BackgroundJobState["Success"] = "SUCCESS";
    return BackgroundJobState;
}({});
var BuildCredentialsSource = /*#__PURE__*/ function(BuildCredentialsSource) {
    BuildCredentialsSource["Local"] = "LOCAL";
    BuildCredentialsSource["Remote"] = "REMOTE";
    return BuildCredentialsSource;
}({});
var BuildIosEnterpriseProvisioning = /*#__PURE__*/ function(BuildIosEnterpriseProvisioning) {
    BuildIosEnterpriseProvisioning["Adhoc"] = "ADHOC";
    BuildIosEnterpriseProvisioning["Universal"] = "UNIVERSAL";
    return BuildIosEnterpriseProvisioning;
}({});
var BuildLimitThresholdExceededMetadataType = /*#__PURE__*/ function(BuildLimitThresholdExceededMetadataType) {
    BuildLimitThresholdExceededMetadataType["Ios"] = "IOS";
    BuildLimitThresholdExceededMetadataType["Total"] = "TOTAL";
    return BuildLimitThresholdExceededMetadataType;
}({});
var BuildMode = /*#__PURE__*/ function(BuildMode) {
    BuildMode["Build"] = "BUILD";
    BuildMode["Custom"] = "CUSTOM";
    BuildMode["Repack"] = "REPACK";
    BuildMode["Resign"] = "RESIGN";
    return BuildMode;
}({});
var BuildPhase = /*#__PURE__*/ function(BuildPhase) {
    BuildPhase["BuilderInfo"] = "BUILDER_INFO";
    BuildPhase["CleanUpCredentials"] = "CLEAN_UP_CREDENTIALS";
    BuildPhase["CompleteBuild"] = "COMPLETE_BUILD";
    BuildPhase["ConfigureExpoUpdates"] = "CONFIGURE_EXPO_UPDATES";
    BuildPhase["ConfigureXcodeProject"] = "CONFIGURE_XCODE_PROJECT";
    BuildPhase["Custom"] = "CUSTOM";
    BuildPhase["DownloadApplicationArchive"] = "DOWNLOAD_APPLICATION_ARCHIVE";
    BuildPhase["EasBuildInternal"] = "EAS_BUILD_INTERNAL";
    BuildPhase["FailBuild"] = "FAIL_BUILD";
    BuildPhase["FixGradlew"] = "FIX_GRADLEW";
    BuildPhase["InstallCustomTools"] = "INSTALL_CUSTOM_TOOLS";
    BuildPhase["InstallDependencies"] = "INSTALL_DEPENDENCIES";
    BuildPhase["InstallPods"] = "INSTALL_PODS";
    BuildPhase["OnBuildCancelHook"] = "ON_BUILD_CANCEL_HOOK";
    BuildPhase["OnBuildCompleteHook"] = "ON_BUILD_COMPLETE_HOOK";
    BuildPhase["OnBuildErrorHook"] = "ON_BUILD_ERROR_HOOK";
    BuildPhase["OnBuildSuccessHook"] = "ON_BUILD_SUCCESS_HOOK";
    BuildPhase["ParseCustomWorkflowConfig"] = "PARSE_CUSTOM_WORKFLOW_CONFIG";
    BuildPhase["PostInstallHook"] = "POST_INSTALL_HOOK";
    BuildPhase["Prebuild"] = "PREBUILD";
    BuildPhase["PrepareArtifacts"] = "PREPARE_ARTIFACTS";
    BuildPhase["PrepareCredentials"] = "PREPARE_CREDENTIALS";
    BuildPhase["PrepareProject"] = "PREPARE_PROJECT";
    BuildPhase["PreInstallHook"] = "PRE_INSTALL_HOOK";
    BuildPhase["PreUploadArtifactsHook"] = "PRE_UPLOAD_ARTIFACTS_HOOK";
    BuildPhase["Queue"] = "QUEUE";
    BuildPhase["ReadAppConfig"] = "READ_APP_CONFIG";
    BuildPhase["ReadPackageJson"] = "READ_PACKAGE_JSON";
    BuildPhase["RestoreCache"] = "RESTORE_CACHE";
    BuildPhase["RunExpoDoctor"] = "RUN_EXPO_DOCTOR";
    BuildPhase["RunFastlane"] = "RUN_FASTLANE";
    BuildPhase["RunGradlew"] = "RUN_GRADLEW";
    BuildPhase["SaveCache"] = "SAVE_CACHE";
    BuildPhase["SetUpBuildEnvironment"] = "SET_UP_BUILD_ENVIRONMENT";
    BuildPhase["SpinUpBuilder"] = "SPIN_UP_BUILDER";
    BuildPhase["StartBuild"] = "START_BUILD";
    BuildPhase["Unknown"] = "UNKNOWN";
    BuildPhase["UploadApplicationArchive"] = "UPLOAD_APPLICATION_ARCHIVE";
    /** @deprecated No longer supported */ BuildPhase["UploadArtifacts"] = "UPLOAD_ARTIFACTS";
    BuildPhase["UploadBuildArtifacts"] = "UPLOAD_BUILD_ARTIFACTS";
    return BuildPhase;
}({});
var BuildPriority = /*#__PURE__*/ function(BuildPriority) {
    BuildPriority["High"] = "HIGH";
    BuildPriority["Normal"] = "NORMAL";
    BuildPriority["NormalPlus"] = "NORMAL_PLUS";
    return BuildPriority;
}({});
var BuildResourceClass = /*#__PURE__*/ function(BuildResourceClass) {
    BuildResourceClass["AndroidDefault"] = "ANDROID_DEFAULT";
    BuildResourceClass["AndroidLarge"] = "ANDROID_LARGE";
    BuildResourceClass["AndroidMedium"] = "ANDROID_MEDIUM";
    BuildResourceClass["IosDefault"] = "IOS_DEFAULT";
    /** @deprecated No longer available. Use IOS_M_LARGE instead. */ BuildResourceClass["IosIntelLarge"] = "IOS_INTEL_LARGE";
    /** @deprecated No longer available. Use IOS_M_MEDIUM instead. */ BuildResourceClass["IosIntelMedium"] = "IOS_INTEL_MEDIUM";
    BuildResourceClass["IosLarge"] = "IOS_LARGE";
    /** @deprecated Use IOS_M_MEDIUM instead */ BuildResourceClass["IosM1Large"] = "IOS_M1_LARGE";
    /** @deprecated Use IOS_M_MEDIUM instead */ BuildResourceClass["IosM1Medium"] = "IOS_M1_MEDIUM";
    BuildResourceClass["IosMedium"] = "IOS_MEDIUM";
    BuildResourceClass["IosMLarge"] = "IOS_M_LARGE";
    BuildResourceClass["IosMMedium"] = "IOS_M_MEDIUM";
    BuildResourceClass["Legacy"] = "LEGACY";
    BuildResourceClass["LinuxLarge"] = "LINUX_LARGE";
    BuildResourceClass["LinuxMedium"] = "LINUX_MEDIUM";
    return BuildResourceClass;
}({});
var BuildRetryDisabledReason = /*#__PURE__*/ function(BuildRetryDisabledReason) {
    BuildRetryDisabledReason["AlreadyRetried"] = "ALREADY_RETRIED";
    BuildRetryDisabledReason["InvalidStatus"] = "INVALID_STATUS";
    BuildRetryDisabledReason["IsGithubBuild"] = "IS_GITHUB_BUILD";
    BuildRetryDisabledReason["NotCompletedYet"] = "NOT_COMPLETED_YET";
    BuildRetryDisabledReason["TooMuchTimeElapsed"] = "TOO_MUCH_TIME_ELAPSED";
    return BuildRetryDisabledReason;
}({});
var BuildStatus = /*#__PURE__*/ function(BuildStatus) {
    BuildStatus["Canceled"] = "CANCELED";
    BuildStatus["Errored"] = "ERRORED";
    BuildStatus["Finished"] = "FINISHED";
    BuildStatus["InProgress"] = "IN_PROGRESS";
    BuildStatus["InQueue"] = "IN_QUEUE";
    BuildStatus["New"] = "NEW";
    BuildStatus["PendingCancel"] = "PENDING_CANCEL";
    return BuildStatus;
}({});
var BuildTrigger = /*#__PURE__*/ function(BuildTrigger) {
    BuildTrigger["EasCli"] = "EAS_CLI";
    BuildTrigger["GitBasedIntegration"] = "GIT_BASED_INTEGRATION";
    return BuildTrigger;
}({});
var BuildWorkflow = /*#__PURE__*/ function(BuildWorkflow) {
    BuildWorkflow["Generic"] = "GENERIC";
    BuildWorkflow["Managed"] = "MANAGED";
    BuildWorkflow["Unknown"] = "UNKNOWN";
    return BuildWorkflow;
}({});
var ContinentCode = /*#__PURE__*/ function(ContinentCode) {
    ContinentCode["Af"] = "AF";
    ContinentCode["An"] = "AN";
    ContinentCode["As"] = "AS";
    ContinentCode["Eu"] = "EU";
    ContinentCode["Na"] = "NA";
    ContinentCode["Oc"] = "OC";
    ContinentCode["Sa"] = "SA";
    ContinentCode["T1"] = "T1";
    return ContinentCode;
}({});
var CrashSampleFor = /*#__PURE__*/ function(CrashSampleFor) {
    CrashSampleFor["Newest"] = "NEWEST";
    CrashSampleFor["Oldest"] = "OLDEST";
    return CrashSampleFor;
}({});
var CustomDomainDnsRecordType = /*#__PURE__*/ function(CustomDomainDnsRecordType) {
    CustomDomainDnsRecordType["A"] = "A";
    CustomDomainDnsRecordType["Cname"] = "CNAME";
    CustomDomainDnsRecordType["Txt"] = "TXT";
    return CustomDomainDnsRecordType;
}({});
var CustomDomainStatus = /*#__PURE__*/ function(CustomDomainStatus) {
    CustomDomainStatus["Active"] = "ACTIVE";
    CustomDomainStatus["Error"] = "ERROR";
    CustomDomainStatus["Pending"] = "PENDING";
    CustomDomainStatus["TimedOut"] = "TIMED_OUT";
    return CustomDomainStatus;
}({});
var DistributionType = /*#__PURE__*/ function(DistributionType) {
    DistributionType["Internal"] = "INTERNAL";
    DistributionType["Simulator"] = "SIMULATOR";
    DistributionType["Store"] = "STORE";
    return DistributionType;
}({});
var EasBuildBillingResourceClass = /*#__PURE__*/ function(EasBuildBillingResourceClass) {
    EasBuildBillingResourceClass["Large"] = "LARGE";
    EasBuildBillingResourceClass["Medium"] = "MEDIUM";
    return EasBuildBillingResourceClass;
}({});
var EasBuildDeprecationInfoType = /*#__PURE__*/ function(EasBuildDeprecationInfoType) {
    EasBuildDeprecationInfoType["Internal"] = "INTERNAL";
    EasBuildDeprecationInfoType["UserFacing"] = "USER_FACING";
    return EasBuildDeprecationInfoType;
}({});
var EasBuildWaiverType = /*#__PURE__*/ function(EasBuildWaiverType) {
    EasBuildWaiverType["FastFailedBuild"] = "FAST_FAILED_BUILD";
    EasBuildWaiverType["SystemError"] = "SYSTEM_ERROR";
    return EasBuildWaiverType;
}({});
var EasService = /*#__PURE__*/ function(EasService) {
    EasService["Builds"] = "BUILDS";
    EasService["Jobs"] = "JOBS";
    EasService["Updates"] = "UPDATES";
    return EasService;
}({});
var EasServiceMetric = /*#__PURE__*/ function(EasServiceMetric) {
    EasServiceMetric["AssetsRequests"] = "ASSETS_REQUESTS";
    EasServiceMetric["BandwidthUsage"] = "BANDWIDTH_USAGE";
    EasServiceMetric["Builds"] = "BUILDS";
    EasServiceMetric["ManifestRequests"] = "MANIFEST_REQUESTS";
    EasServiceMetric["RunTime"] = "RUN_TIME";
    EasServiceMetric["UniqueUpdaters"] = "UNIQUE_UPDATERS";
    EasServiceMetric["UniqueUsers"] = "UNIQUE_USERS";
    return EasServiceMetric;
}({});
var EasTotalPlanEnablementUnit = /*#__PURE__*/ function(EasTotalPlanEnablementUnit) {
    EasTotalPlanEnablementUnit["Build"] = "BUILD";
    EasTotalPlanEnablementUnit["Byte"] = "BYTE";
    EasTotalPlanEnablementUnit["Concurrency"] = "CONCURRENCY";
    EasTotalPlanEnablementUnit["Request"] = "REQUEST";
    EasTotalPlanEnablementUnit["Updater"] = "UPDATER";
    EasTotalPlanEnablementUnit["User"] = "USER";
    return EasTotalPlanEnablementUnit;
}({});
var EntityTypeName = /*#__PURE__*/ function(EntityTypeName) {
    EntityTypeName["AccountEntity"] = "AccountEntity";
    EntityTypeName["AccountSsoConfigurationEntity"] = "AccountSSOConfigurationEntity";
    EntityTypeName["AndroidAppCredentialsEntity"] = "AndroidAppCredentialsEntity";
    EntityTypeName["AndroidKeystoreEntity"] = "AndroidKeystoreEntity";
    EntityTypeName["AppEntity"] = "AppEntity";
    EntityTypeName["AppStoreConnectApiKeyEntity"] = "AppStoreConnectApiKeyEntity";
    EntityTypeName["AppleDeviceEntity"] = "AppleDeviceEntity";
    EntityTypeName["AppleDistributionCertificateEntity"] = "AppleDistributionCertificateEntity";
    EntityTypeName["AppleProvisioningProfileEntity"] = "AppleProvisioningProfileEntity";
    EntityTypeName["AppleTeamEntity"] = "AppleTeamEntity";
    EntityTypeName["BranchEntity"] = "BranchEntity";
    EntityTypeName["ChannelEntity"] = "ChannelEntity";
    EntityTypeName["CustomerEntity"] = "CustomerEntity";
    EntityTypeName["GoogleServiceAccountKeyEntity"] = "GoogleServiceAccountKeyEntity";
    EntityTypeName["IosAppCredentialsEntity"] = "IosAppCredentialsEntity";
    EntityTypeName["UserInvitationEntity"] = "UserInvitationEntity";
    EntityTypeName["UserPermissionEntity"] = "UserPermissionEntity";
    EntityTypeName["WorkerCustomDomainEntity"] = "WorkerCustomDomainEntity";
    EntityTypeName["WorkerDeploymentAliasEntity"] = "WorkerDeploymentAliasEntity";
    EntityTypeName["WorkerEntity"] = "WorkerEntity";
    EntityTypeName["WorkflowEntity"] = "WorkflowEntity";
    EntityTypeName["WorkflowRevisionEntity"] = "WorkflowRevisionEntity";
    return EntityTypeName;
}({});
var EnvironmentSecretType = /*#__PURE__*/ function(EnvironmentSecretType) {
    EnvironmentSecretType["FileBase64"] = "FILE_BASE64";
    EnvironmentSecretType["String"] = "STRING";
    return EnvironmentSecretType;
}({});
var EnvironmentVariableEnvironment = /*#__PURE__*/ function(EnvironmentVariableEnvironment) {
    EnvironmentVariableEnvironment["Development"] = "DEVELOPMENT";
    EnvironmentVariableEnvironment["Preview"] = "PREVIEW";
    EnvironmentVariableEnvironment["Production"] = "PRODUCTION";
    return EnvironmentVariableEnvironment;
}({});
var EnvironmentVariableScope = /*#__PURE__*/ function(EnvironmentVariableScope) {
    EnvironmentVariableScope["Project"] = "PROJECT";
    EnvironmentVariableScope["Shared"] = "SHARED";
    return EnvironmentVariableScope;
}({});
var EnvironmentVariableVisibility = /*#__PURE__*/ function(EnvironmentVariableVisibility) {
    EnvironmentVariableVisibility["Public"] = "PUBLIC";
    EnvironmentVariableVisibility["Secret"] = "SECRET";
    EnvironmentVariableVisibility["Sensitive"] = "SENSITIVE";
    return EnvironmentVariableVisibility;
}({});
var Experiment = /*#__PURE__*/ function(Experiment) {
    Experiment["Orbit"] = "ORBIT";
    return Experiment;
}({});
var Feature = /*#__PURE__*/ function(Feature) {
    /** Priority Builds */ Feature["Builds"] = "BUILDS";
    /** Funds support for open source development */ Feature["OpenSource"] = "OPEN_SOURCE";
    /** Top Tier Support */ Feature["Support"] = "SUPPORT";
    /** Share access to projects */ Feature["Teams"] = "TEAMS";
    return Feature;
}({});
var FingerprintSourceType = /*#__PURE__*/ function(FingerprintSourceType) {
    FingerprintSourceType["Gcs"] = "GCS";
    return FingerprintSourceType;
}({});
var GitHubAppEnvironment = /*#__PURE__*/ function(GitHubAppEnvironment) {
    GitHubAppEnvironment["Development"] = "DEVELOPMENT";
    GitHubAppEnvironment["Production"] = "PRODUCTION";
    GitHubAppEnvironment["Staging"] = "STAGING";
    return GitHubAppEnvironment;
}({});
var GitHubAppInstallationStatus = /*#__PURE__*/ function(GitHubAppInstallationStatus) {
    GitHubAppInstallationStatus["Active"] = "ACTIVE";
    GitHubAppInstallationStatus["NotInstalled"] = "NOT_INSTALLED";
    GitHubAppInstallationStatus["Suspended"] = "SUSPENDED";
    return GitHubAppInstallationStatus;
}({});
var GitHubBuildTriggerExecutionBehavior = /*#__PURE__*/ function(GitHubBuildTriggerExecutionBehavior) {
    GitHubBuildTriggerExecutionBehavior["Always"] = "ALWAYS";
    GitHubBuildTriggerExecutionBehavior["BaseDirectoryChanged"] = "BASE_DIRECTORY_CHANGED";
    return GitHubBuildTriggerExecutionBehavior;
}({});
var GitHubBuildTriggerRunStatus = /*#__PURE__*/ function(GitHubBuildTriggerRunStatus) {
    GitHubBuildTriggerRunStatus["Errored"] = "ERRORED";
    GitHubBuildTriggerRunStatus["Success"] = "SUCCESS";
    return GitHubBuildTriggerRunStatus;
}({});
var GitHubBuildTriggerType = /*#__PURE__*/ function(GitHubBuildTriggerType) {
    GitHubBuildTriggerType["PullRequestUpdated"] = "PULL_REQUEST_UPDATED";
    GitHubBuildTriggerType["PushToBranch"] = "PUSH_TO_BRANCH";
    GitHubBuildTriggerType["TagUpdated"] = "TAG_UPDATED";
    return GitHubBuildTriggerType;
}({});
var GitHubJobRunJobType = /*#__PURE__*/ function(GitHubJobRunJobType) {
    GitHubJobRunJobType["PublishUpdate"] = "PUBLISH_UPDATE";
    return GitHubJobRunJobType;
}({});
var GitHubJobRunTriggerRunStatus = /*#__PURE__*/ function(GitHubJobRunTriggerRunStatus) {
    GitHubJobRunTriggerRunStatus["Errored"] = "ERRORED";
    GitHubJobRunTriggerRunStatus["Success"] = "SUCCESS";
    return GitHubJobRunTriggerRunStatus;
}({});
var GitHubJobRunTriggerType = /*#__PURE__*/ function(GitHubJobRunTriggerType) {
    GitHubJobRunTriggerType["PullRequestUpdated"] = "PULL_REQUEST_UPDATED";
    GitHubJobRunTriggerType["PushToBranch"] = "PUSH_TO_BRANCH";
    return GitHubJobRunTriggerType;
}({});
var InsightsFilterType = /*#__PURE__*/ function(InsightsFilterType) {
    InsightsFilterType["Platform"] = "PLATFORM";
    return InsightsFilterType;
}({});
var InvoiceDiscountType = /*#__PURE__*/ function(InvoiceDiscountType) {
    InvoiceDiscountType["Amount"] = "AMOUNT";
    InvoiceDiscountType["Percentage"] = "PERCENTAGE";
    return InvoiceDiscountType;
}({});
var IosBuildType = /*#__PURE__*/ function(IosBuildType) {
    IosBuildType["DevelopmentClient"] = "DEVELOPMENT_CLIENT";
    IosBuildType["Release"] = "RELEASE";
    return IosBuildType;
}({});
var IosDistributionType = /*#__PURE__*/ function(IosDistributionType) {
    IosDistributionType["AdHoc"] = "AD_HOC";
    IosDistributionType["AppStore"] = "APP_STORE";
    IosDistributionType["Development"] = "DEVELOPMENT";
    IosDistributionType["Enterprise"] = "ENTERPRISE";
    return IosDistributionType;
}({});
var IosManagedBuildType = /*#__PURE__*/ function(IosManagedBuildType) {
    IosManagedBuildType["DevelopmentClient"] = "DEVELOPMENT_CLIENT";
    IosManagedBuildType["Release"] = "RELEASE";
    return IosManagedBuildType;
}({});
var IosSchemeBuildConfiguration = /*#__PURE__*/ function(IosSchemeBuildConfiguration) {
    IosSchemeBuildConfiguration["Debug"] = "DEBUG";
    IosSchemeBuildConfiguration["Release"] = "RELEASE";
    return IosSchemeBuildConfiguration;
}({});
var JobRunPriority = /*#__PURE__*/ function(JobRunPriority) {
    JobRunPriority["High"] = "HIGH";
    JobRunPriority["Normal"] = "NORMAL";
    return JobRunPriority;
}({});
var JobRunStatus = /*#__PURE__*/ function(JobRunStatus) {
    JobRunStatus["Canceled"] = "CANCELED";
    JobRunStatus["Errored"] = "ERRORED";
    JobRunStatus["Finished"] = "FINISHED";
    JobRunStatus["InProgress"] = "IN_PROGRESS";
    JobRunStatus["InQueue"] = "IN_QUEUE";
    JobRunStatus["New"] = "NEW";
    JobRunStatus["PendingCancel"] = "PENDING_CANCEL";
    return JobRunStatus;
}({});
var MailchimpAudience = /*#__PURE__*/ function(MailchimpAudience) {
    MailchimpAudience["ExpoDevelopers"] = "EXPO_DEVELOPERS";
    MailchimpAudience["ExpoDeveloperOnboarding"] = "EXPO_DEVELOPER_ONBOARDING";
    MailchimpAudience["LaunchParty_2024"] = "LAUNCH_PARTY_2024";
    MailchimpAudience["NonprodExpoDevelopers"] = "NONPROD_EXPO_DEVELOPERS";
    return MailchimpAudience;
}({});
var MailchimpTag = /*#__PURE__*/ function(MailchimpTag) {
    MailchimpTag["DevClientUsers"] = "DEV_CLIENT_USERS";
    MailchimpTag["DidSubscribeToEasAtLeastOnce"] = "DID_SUBSCRIBE_TO_EAS_AT_LEAST_ONCE";
    MailchimpTag["EasMasterList"] = "EAS_MASTER_LIST";
    MailchimpTag["NewsletterSignupList"] = "NEWSLETTER_SIGNUP_LIST";
    return MailchimpTag;
}({});
var NotificationEvent = /*#__PURE__*/ function(NotificationEvent) {
    NotificationEvent["BuildComplete"] = "BUILD_COMPLETE";
    NotificationEvent["BuildErrored"] = "BUILD_ERRORED";
    NotificationEvent["BuildLimitThresholdExceeded"] = "BUILD_LIMIT_THRESHOLD_EXCEEDED";
    NotificationEvent["BuildPlanCreditThresholdExceeded"] = "BUILD_PLAN_CREDIT_THRESHOLD_EXCEEDED";
    NotificationEvent["SubmissionComplete"] = "SUBMISSION_COMPLETE";
    NotificationEvent["SubmissionErrored"] = "SUBMISSION_ERRORED";
    NotificationEvent["Test"] = "TEST";
    return NotificationEvent;
}({});
var NotificationType = /*#__PURE__*/ function(NotificationType) {
    NotificationType["Email"] = "EMAIL";
    NotificationType["Web"] = "WEB";
    return NotificationType;
}({});
var OfferType = /*#__PURE__*/ function(OfferType) {
    /** Addon, or supplementary subscription */ OfferType["Addon"] = "ADDON";
    /** Advanced Purchase of Paid Resource */ OfferType["Prepaid"] = "PREPAID";
    /** Term subscription */ OfferType["Subscription"] = "SUBSCRIPTION";
    return OfferType;
}({});
var OnboardingDeviceType = /*#__PURE__*/ function(OnboardingDeviceType) {
    OnboardingDeviceType["Device"] = "DEVICE";
    OnboardingDeviceType["Simulator"] = "SIMULATOR";
    return OnboardingDeviceType;
}({});
var OnboardingEnvironment = /*#__PURE__*/ function(OnboardingEnvironment) {
    OnboardingEnvironment["DevBuild"] = "DEV_BUILD";
    OnboardingEnvironment["ExpoGo"] = "EXPO_GO";
    return OnboardingEnvironment;
}({});
var Order = /*#__PURE__*/ function(Order) {
    Order["Asc"] = "ASC";
    Order["Desc"] = "DESC";
    return Order;
}({});
var Permission = /*#__PURE__*/ function(Permission) {
    Permission["Admin"] = "ADMIN";
    Permission["Own"] = "OWN";
    Permission["Publish"] = "PUBLISH";
    Permission["View"] = "VIEW";
    return Permission;
}({});
var ProjectArchiveSourceType = /*#__PURE__*/ function(ProjectArchiveSourceType) {
    ProjectArchiveSourceType["Gcs"] = "GCS";
    ProjectArchiveSourceType["Git"] = "GIT";
    ProjectArchiveSourceType["None"] = "NONE";
    ProjectArchiveSourceType["S3"] = "S3";
    ProjectArchiveSourceType["Url"] = "URL";
    return ProjectArchiveSourceType;
}({});
var RequestMethod = /*#__PURE__*/ function(RequestMethod) {
    RequestMethod["Delete"] = "DELETE";
    RequestMethod["Get"] = "GET";
    RequestMethod["Head"] = "HEAD";
    RequestMethod["Options"] = "OPTIONS";
    RequestMethod["Patch"] = "PATCH";
    RequestMethod["Post"] = "POST";
    RequestMethod["Put"] = "PUT";
    return RequestMethod;
}({});
var RequestsOrderByDirection = /*#__PURE__*/ function(RequestsOrderByDirection) {
    RequestsOrderByDirection["Asc"] = "ASC";
    RequestsOrderByDirection["Desc"] = "DESC";
    return RequestsOrderByDirection;
}({});
var RequestsOrderByField = /*#__PURE__*/ function(RequestsOrderByField) {
    RequestsOrderByField["AssetsSum"] = "ASSETS_SUM";
    RequestsOrderByField["CacheHitRatio"] = "CACHE_HIT_RATIO";
    RequestsOrderByField["CachePassRatio"] = "CACHE_PASS_RATIO";
    RequestsOrderByField["CrashesSum"] = "CRASHES_SUM";
    RequestsOrderByField["Duration"] = "DURATION";
    RequestsOrderByField["RequestsSum"] = "REQUESTS_SUM";
    return RequestsOrderByField;
}({});
var ResourceClassExperiment = /*#__PURE__*/ function(ResourceClassExperiment) {
    ResourceClassExperiment["C3D"] = "C3D";
    ResourceClassExperiment["N2"] = "N2";
    return ResourceClassExperiment;
}({});
var ResponseCacheStatus = /*#__PURE__*/ function(ResponseCacheStatus) {
    ResponseCacheStatus["Hit"] = "HIT";
    ResponseCacheStatus["Miss"] = "MISS";
    ResponseCacheStatus["Pass"] = "PASS";
    return ResponseCacheStatus;
}({});
var ResponseStatusType = /*#__PURE__*/ function(ResponseStatusType) {
    ResponseStatusType["ClientError"] = "CLIENT_ERROR";
    ResponseStatusType["None"] = "NONE";
    ResponseStatusType["Redirect"] = "REDIRECT";
    ResponseStatusType["ServerError"] = "SERVER_ERROR";
    ResponseStatusType["Successful"] = "SUCCESSFUL";
    return ResponseStatusType;
}({});
var ResponseType = /*#__PURE__*/ function(ResponseType) {
    ResponseType["Asset"] = "ASSET";
    ResponseType["Crash"] = "CRASH";
    ResponseType["Rejected"] = "REJECTED";
    ResponseType["Route"] = "ROUTE";
    return ResponseType;
}({});
var Role = /*#__PURE__*/ function(Role) {
    Role["Admin"] = "ADMIN";
    Role["Custom"] = "CUSTOM";
    Role["Developer"] = "DEVELOPER";
    Role["HasAdmin"] = "HAS_ADMIN";
    Role["NotAdmin"] = "NOT_ADMIN";
    Role["Owner"] = "OWNER";
    Role["ViewOnly"] = "VIEW_ONLY";
    return Role;
}({});
var SecondFactorMethod = /*#__PURE__*/ function(SecondFactorMethod) {
    /** Google Authenticator (TOTP) */ SecondFactorMethod["Authenticator"] = "AUTHENTICATOR";
    /** SMS */ SecondFactorMethod["Sms"] = "SMS";
    return SecondFactorMethod;
}({});
var StandardOffer = /*#__PURE__*/ function(StandardOffer) {
    /** $29 USD per month, 30 day trial */ StandardOffer["Default"] = "DEFAULT";
    /** $800 USD per month */ StandardOffer["Support"] = "SUPPORT";
    /** $29 USD per month, 1 year trial */ StandardOffer["YcDeals"] = "YC_DEALS";
    /** $348 USD per year, 30 day trial */ StandardOffer["YearlySub"] = "YEARLY_SUB";
    return StandardOffer;
}({});
var StatuspageIncidentImpact = /*#__PURE__*/ function(StatuspageIncidentImpact) {
    StatuspageIncidentImpact["Critical"] = "CRITICAL";
    StatuspageIncidentImpact["Maintenance"] = "MAINTENANCE";
    StatuspageIncidentImpact["Major"] = "MAJOR";
    StatuspageIncidentImpact["Minor"] = "MINOR";
    StatuspageIncidentImpact["None"] = "NONE";
    return StatuspageIncidentImpact;
}({});
var StatuspageIncidentStatus = /*#__PURE__*/ function(StatuspageIncidentStatus) {
    StatuspageIncidentStatus["Completed"] = "COMPLETED";
    StatuspageIncidentStatus["Identified"] = "IDENTIFIED";
    StatuspageIncidentStatus["Investigating"] = "INVESTIGATING";
    StatuspageIncidentStatus["InProgress"] = "IN_PROGRESS";
    StatuspageIncidentStatus["Monitoring"] = "MONITORING";
    StatuspageIncidentStatus["Resolved"] = "RESOLVED";
    StatuspageIncidentStatus["Scheduled"] = "SCHEDULED";
    StatuspageIncidentStatus["Verifying"] = "VERIFYING";
    return StatuspageIncidentStatus;
}({});
var StatuspageServiceName = /*#__PURE__*/ function(StatuspageServiceName) {
    StatuspageServiceName["EasBuild"] = "EAS_BUILD";
    StatuspageServiceName["EasSubmit"] = "EAS_SUBMIT";
    StatuspageServiceName["EasUpdate"] = "EAS_UPDATE";
    StatuspageServiceName["GithubApiRequests"] = "GITHUB_API_REQUESTS";
    StatuspageServiceName["GithubWebhooks"] = "GITHUB_WEBHOOKS";
    return StatuspageServiceName;
}({});
var StatuspageServiceStatus = /*#__PURE__*/ function(StatuspageServiceStatus) {
    StatuspageServiceStatus["DegradedPerformance"] = "DEGRADED_PERFORMANCE";
    StatuspageServiceStatus["MajorOutage"] = "MAJOR_OUTAGE";
    StatuspageServiceStatus["Operational"] = "OPERATIONAL";
    StatuspageServiceStatus["PartialOutage"] = "PARTIAL_OUTAGE";
    StatuspageServiceStatus["UnderMaintenance"] = "UNDER_MAINTENANCE";
    return StatuspageServiceStatus;
}({});
var SubmissionAndroidArchiveType = /*#__PURE__*/ function(SubmissionAndroidArchiveType) {
    SubmissionAndroidArchiveType["Aab"] = "AAB";
    SubmissionAndroidArchiveType["Apk"] = "APK";
    return SubmissionAndroidArchiveType;
}({});
var SubmissionAndroidReleaseStatus = /*#__PURE__*/ function(SubmissionAndroidReleaseStatus) {
    SubmissionAndroidReleaseStatus["Completed"] = "COMPLETED";
    SubmissionAndroidReleaseStatus["Draft"] = "DRAFT";
    SubmissionAndroidReleaseStatus["Halted"] = "HALTED";
    SubmissionAndroidReleaseStatus["InProgress"] = "IN_PROGRESS";
    return SubmissionAndroidReleaseStatus;
}({});
var SubmissionAndroidTrack = /*#__PURE__*/ function(SubmissionAndroidTrack) {
    SubmissionAndroidTrack["Alpha"] = "ALPHA";
    SubmissionAndroidTrack["Beta"] = "BETA";
    SubmissionAndroidTrack["Internal"] = "INTERNAL";
    SubmissionAndroidTrack["Production"] = "PRODUCTION";
    return SubmissionAndroidTrack;
}({});
var SubmissionArchiveSourceType = /*#__PURE__*/ function(SubmissionArchiveSourceType) {
    SubmissionArchiveSourceType["GcsBuildApplicationArchive"] = "GCS_BUILD_APPLICATION_ARCHIVE";
    SubmissionArchiveSourceType["GcsBuildApplicationArchiveOrchestrator"] = "GCS_BUILD_APPLICATION_ARCHIVE_ORCHESTRATOR";
    SubmissionArchiveSourceType["GcsSubmitArchive"] = "GCS_SUBMIT_ARCHIVE";
    SubmissionArchiveSourceType["Url"] = "URL";
    return SubmissionArchiveSourceType;
}({});
var SubmissionPriority = /*#__PURE__*/ function(SubmissionPriority) {
    SubmissionPriority["High"] = "HIGH";
    SubmissionPriority["Normal"] = "NORMAL";
    return SubmissionPriority;
}({});
var SubmissionStatus = /*#__PURE__*/ function(SubmissionStatus) {
    SubmissionStatus["AwaitingBuild"] = "AWAITING_BUILD";
    SubmissionStatus["Canceled"] = "CANCELED";
    SubmissionStatus["Errored"] = "ERRORED";
    SubmissionStatus["Finished"] = "FINISHED";
    SubmissionStatus["InProgress"] = "IN_PROGRESS";
    SubmissionStatus["InQueue"] = "IN_QUEUE";
    return SubmissionStatus;
}({});
var TargetEntityMutationType = /*#__PURE__*/ function(TargetEntityMutationType) {
    TargetEntityMutationType["Create"] = "CREATE";
    TargetEntityMutationType["Delete"] = "DELETE";
    TargetEntityMutationType["Update"] = "UPDATE";
    return TargetEntityMutationType;
}({});
var UploadSessionType = /*#__PURE__*/ function(UploadSessionType) {
    UploadSessionType["EasBuildGcsProjectMetadata"] = "EAS_BUILD_GCS_PROJECT_METADATA";
    UploadSessionType["EasBuildGcsProjectSources"] = "EAS_BUILD_GCS_PROJECT_SOURCES";
    /** @deprecated Use EAS_BUILD_GCS_PROJECT_SOURCES instead. */ UploadSessionType["EasBuildProjectSources"] = "EAS_BUILD_PROJECT_SOURCES";
    /** @deprecated Use EAS_SUBMIT_GCS_APP_ARCHIVE instead. */ UploadSessionType["EasSubmitAppArchive"] = "EAS_SUBMIT_APP_ARCHIVE";
    UploadSessionType["EasSubmitGcsAppArchive"] = "EAS_SUBMIT_GCS_APP_ARCHIVE";
    UploadSessionType["EasUpdateFingerprint"] = "EAS_UPDATE_FINGERPRINT";
    return UploadSessionType;
}({});
var UsageMetricType = /*#__PURE__*/ function(UsageMetricType) {
    UsageMetricType["Bandwidth"] = "BANDWIDTH";
    UsageMetricType["Build"] = "BUILD";
    UsageMetricType["Minute"] = "MINUTE";
    UsageMetricType["Request"] = "REQUEST";
    UsageMetricType["Update"] = "UPDATE";
    UsageMetricType["User"] = "USER";
    return UsageMetricType;
}({});
var UsageMetricsGranularity = /*#__PURE__*/ function(UsageMetricsGranularity) {
    UsageMetricsGranularity["Day"] = "DAY";
    UsageMetricsGranularity["Hour"] = "HOUR";
    UsageMetricsGranularity["Minute"] = "MINUTE";
    UsageMetricsGranularity["Total"] = "TOTAL";
    return UsageMetricsGranularity;
}({});
var UserAgentBrowser = /*#__PURE__*/ function(UserAgentBrowser) {
    UserAgentBrowser["AndroidMobile"] = "ANDROID_MOBILE";
    UserAgentBrowser["Chrome"] = "CHROME";
    UserAgentBrowser["ChromeIos"] = "CHROME_IOS";
    UserAgentBrowser["Edge"] = "EDGE";
    UserAgentBrowser["FacebookMobile"] = "FACEBOOK_MOBILE";
    UserAgentBrowser["Firefox"] = "FIREFOX";
    UserAgentBrowser["FirefoxIos"] = "FIREFOX_IOS";
    UserAgentBrowser["InternetExplorer"] = "INTERNET_EXPLORER";
    UserAgentBrowser["Konqueror"] = "KONQUEROR";
    UserAgentBrowser["Mozilla"] = "MOZILLA";
    UserAgentBrowser["Opera"] = "OPERA";
    UserAgentBrowser["Safari"] = "SAFARI";
    UserAgentBrowser["SafariMobile"] = "SAFARI_MOBILE";
    UserAgentBrowser["SamsungInternet"] = "SAMSUNG_INTERNET";
    UserAgentBrowser["UcBrowser"] = "UC_BROWSER";
    return UserAgentBrowser;
}({});
var UserAgentOs = /*#__PURE__*/ function(UserAgentOs) {
    UserAgentOs["Android"] = "ANDROID";
    UserAgentOs["ChromeOs"] = "CHROME_OS";
    UserAgentOs["Ios"] = "IOS";
    UserAgentOs["IpadOs"] = "IPAD_OS";
    UserAgentOs["Linux"] = "LINUX";
    UserAgentOs["MacOs"] = "MAC_OS";
    UserAgentOs["Windows"] = "WINDOWS";
    return UserAgentOs;
}({});
var UserEntityTypeName = /*#__PURE__*/ function(UserEntityTypeName) {
    UserEntityTypeName["AccessTokenEntity"] = "AccessTokenEntity";
    UserEntityTypeName["DiscordUserEntity"] = "DiscordUserEntity";
    UserEntityTypeName["GitHubUserEntity"] = "GitHubUserEntity";
    UserEntityTypeName["PasswordEntity"] = "PasswordEntity";
    UserEntityTypeName["SsoUserEntity"] = "SSOUserEntity";
    UserEntityTypeName["UserEntity"] = "UserEntity";
    UserEntityTypeName["UserPermissionEntity"] = "UserPermissionEntity";
    UserEntityTypeName["UserSecondFactorBackupCodesEntity"] = "UserSecondFactorBackupCodesEntity";
    UserEntityTypeName["UserSecondFactorDeviceEntity"] = "UserSecondFactorDeviceEntity";
    return UserEntityTypeName;
}({});
var WebhookType = /*#__PURE__*/ function(WebhookType) {
    WebhookType["Build"] = "BUILD";
    WebhookType["Submit"] = "SUBMIT";
    return WebhookType;
}({});
var WorkerDeploymentCrashKind = /*#__PURE__*/ function(WorkerDeploymentCrashKind) {
    WorkerDeploymentCrashKind["ExceededCpu"] = "EXCEEDED_CPU";
    WorkerDeploymentCrashKind["ExceededMemory"] = "EXCEEDED_MEMORY";
    WorkerDeploymentCrashKind["ExceededSubrequests"] = "EXCEEDED_SUBREQUESTS";
    WorkerDeploymentCrashKind["Generic"] = "GENERIC";
    WorkerDeploymentCrashKind["Internal"] = "INTERNAL";
    WorkerDeploymentCrashKind["ResponseStreamDisconnected"] = "RESPONSE_STREAM_DISCONNECTED";
    return WorkerDeploymentCrashKind;
}({});
var WorkerDeploymentLogLevel = /*#__PURE__*/ function(WorkerDeploymentLogLevel) {
    WorkerDeploymentLogLevel["Debug"] = "DEBUG";
    WorkerDeploymentLogLevel["Error"] = "ERROR";
    WorkerDeploymentLogLevel["Fatal"] = "FATAL";
    WorkerDeploymentLogLevel["Info"] = "INFO";
    WorkerDeploymentLogLevel["Log"] = "LOG";
    WorkerDeploymentLogLevel["Warn"] = "WARN";
    return WorkerDeploymentLogLevel;
}({});
var WorkerLoggerLevel = /*#__PURE__*/ function(WorkerLoggerLevel) {
    WorkerLoggerLevel["Debug"] = "DEBUG";
    WorkerLoggerLevel["Error"] = "ERROR";
    WorkerLoggerLevel["Fatal"] = "FATAL";
    WorkerLoggerLevel["Info"] = "INFO";
    WorkerLoggerLevel["Trace"] = "TRACE";
    WorkerLoggerLevel["Warn"] = "WARN";
    return WorkerLoggerLevel;
}({});
var WorkflowJobStatus = /*#__PURE__*/ function(WorkflowJobStatus) {
    WorkflowJobStatus["ActionRequired"] = "ACTION_REQUIRED";
    WorkflowJobStatus["Canceled"] = "CANCELED";
    WorkflowJobStatus["Failure"] = "FAILURE";
    WorkflowJobStatus["InProgress"] = "IN_PROGRESS";
    WorkflowJobStatus["New"] = "NEW";
    WorkflowJobStatus["PendingCancel"] = "PENDING_CANCEL";
    WorkflowJobStatus["Skipped"] = "SKIPPED";
    WorkflowJobStatus["Success"] = "SUCCESS";
    return WorkflowJobStatus;
}({});
var WorkflowJobType = /*#__PURE__*/ function(WorkflowJobType) {
    WorkflowJobType["AppleDeviceRegistrationRequest"] = "APPLE_DEVICE_REGISTRATION_REQUEST";
    WorkflowJobType["Build"] = "BUILD";
    WorkflowJobType["Custom"] = "CUSTOM";
    WorkflowJobType["Deploy"] = "DEPLOY";
    WorkflowJobType["GetBuild"] = "GET_BUILD";
    WorkflowJobType["MaestroTest"] = "MAESTRO_TEST";
    WorkflowJobType["RequireApproval"] = "REQUIRE_APPROVAL";
    WorkflowJobType["Submission"] = "SUBMISSION";
    WorkflowJobType["Update"] = "UPDATE";
    return WorkflowJobType;
}({});
var WorkflowProjectSourceType = /*#__PURE__*/ function(WorkflowProjectSourceType) {
    WorkflowProjectSourceType["Gcs"] = "GCS";
    return WorkflowProjectSourceType;
}({});
var WorkflowRunStatus = /*#__PURE__*/ function(WorkflowRunStatus) {
    WorkflowRunStatus["ActionRequired"] = "ACTION_REQUIRED";
    WorkflowRunStatus["Canceled"] = "CANCELED";
    WorkflowRunStatus["Failure"] = "FAILURE";
    WorkflowRunStatus["InProgress"] = "IN_PROGRESS";
    WorkflowRunStatus["New"] = "NEW";
    WorkflowRunStatus["PendingCancel"] = "PENDING_CANCEL";
    WorkflowRunStatus["Success"] = "SUCCESS";
    return WorkflowRunStatus;
}({});
var WorkflowRunTriggerEventType = /*#__PURE__*/ function(WorkflowRunTriggerEventType) {
    WorkflowRunTriggerEventType["Github"] = "GITHUB";
    WorkflowRunTriggerEventType["Manual"] = "MANUAL";
    return WorkflowRunTriggerEventType;
}({});

//# sourceMappingURL=generated.js.map