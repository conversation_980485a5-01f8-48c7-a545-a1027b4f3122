import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import PageWrapper from '../layout/PageWrapper';
import BookingStep from '../booking/BookingStep';
import BookingSummary from '../booking/BookingSummary';
import BasicDetailsForm from '../booking/BasicDetailsForm';

// Service-specific input forms
const serviceInputForms = {
  Decoration: {
    title: "Decoration Details",
    category: "Decoration",
    type: "service-input",
    fields: [
      {
        name: "theme",
        label: "Decoration Theme",
        type: "select",
        options: [
          "Traditional",
          "Modern",
          "Fusion",
          "Minimalist",
          "Elegant",
          "Rustic",
          "Bohemian",
          "Royal",
          "Floral",
          "Custom"
        ],
        required: true
      },
      {
        name: "colorScheme",
        label: "Color Scheme",
        type: "select",
        options: [
          "Red & Gold",
          "Pastel Colors",
          "White & Green",
          "Blue & Silver",
          "Purple & Gold",
          "Pink & White",
          "Yellow & Orange",
          "Custom"
        ],
        required: true
      },
      {
        name: "decorationAreas",
        label: "Areas to Decorate",
        type: "multiselect",
        options: [
          "Entrance",
          "Stage",
          "Dining Area",
          "Seating Area",
          "Photo Booth",
          "Ceiling",
          "Walls",
          "Outdoor Space"
        ],
        required: true
      },
      {
        name: "specialRequests",
        label: "Special Requests",
        type: "textarea",
        placeholder: "Any specific decoration elements or requirements...",
        required: false
      }
    ]
  },
  Catering: {
    title: "Catering Details",
    category: "Catering",
    type: "service-input",
    fields: [
      {
        name: "cuisineType",
        label: "Cuisine Type",
        type: "select",
        options: [
          "North Indian",
          "South Indian",
          "Pan Asian",
          "Continental",
          "Multi-Cuisine",
          "Regional Specialties",
          "Fusion",
          "Custom"
        ],
        required: true
      },
      {
        name: "mealType",
        label: "Meal Type",
        type: "select",
        options: [
          "Breakfast",
          "Lunch",
          "Dinner",
          "High Tea",
          "Cocktail & Snacks",
          "Buffet",
          "Plated Service"
        ],
        required: true
      },
      {
        name: "dietaryRestrictions",
        label: "Dietary Restrictions",
        type: "multiselect",
        options: [
          "Vegetarian",
          "Vegan",
          "Jain",
          "Gluten-Free",
          "Nut-Free",
          "Dairy-Free",
          "No Onion No Garlic"
        ],
        required: false
      },
      {
        name: "beverages",
        label: "Beverages",
        type: "multiselect",
        options: [
          "Soft Drinks",
          "Juices",
          "Tea & Coffee",
          "Mocktails",
          "Alcoholic Beverages",
          "Specialty Drinks"
        ],
        required: false
      },
      {
        name: "specialRequests",
        label: "Special Requests",
        type: "textarea",
        placeholder: "Any specific menu items or catering requirements...",
        required: false
      }
    ]
  },
  Priest: {
    title: "Priest Service Details",
    category: "Priest",
    type: "service-input",
    fields: [
      {
        name: "ceremonyType",
        label: "Ceremony Type",
        type: "select",
        options: [
          "Griha Pravesh (House Warming)",
          "Satyanarayan Puja",
          "Ganesh Puja",
          "Lakshmi Puja",
          "Navgraha Shanti",
          "Vastu Shanti",
          "Wedding Ceremony",
          "Baby Naming Ceremony",
          "Other"
        ],
        required: true
      },
      {
        name: "language",
        label: "Preferred Language",
        type: "select",
        options: [
          "Sanskrit with Hindi explanation",
          "Sanskrit with English explanation",
          "Hindi",
          "Tamil",
          "Telugu",
          "Kannada",
          "Malayalam",
          "Bengali",
          "Gujarati",
          "Marathi"
        ],
        required: true
      },
      {
        name: "duration",
        label: "Expected Duration",
        type: "select",
        options: [
          "1-2 hours",
          "2-3 hours",
          "3-4 hours",
          "4+ hours"
        ],
        required: true
      },
      {
        name: "poojaItems",
        label: "Pooja Items",
        type: "select",
        options: [
          "We will arrange",
          "Need complete package",
          "Need partial assistance"
        ],
        required: true
      },
      {
        name: "specialRequests",
        label: "Special Requests",
        type: "textarea",
        placeholder: "Any specific rituals or requirements...",
        required: false
      }
    ]
  },
  Photography: {
    title: "Photography Details",
    category: "Photography",
    type: "service-input",
    fields: [
      {
        name: "coverageType",
        label: "Coverage Type",
        type: "select",
        options: [
          "Event Photography",
          "Candid Photography",
          "Traditional + Candid",
          "Family Portraits",
          "Complete Coverage"
        ],
        required: true
      },
      {
        name: "duration",
        label: "Duration",
        type: "select",
        options: [
          "2-4 hours",
          "4-6 hours",
          "6-8 hours",
          "Full day"
        ],
        required: true
      },
      {
        name: "deliverables",
        label: "Deliverables",
        type: "multiselect",
        options: [
          "Digital Photos",
          "Printed Album",
          "Canvas Prints",
          "Photo Frames",
          "Same-day Edits"
        ],
        required: true
      },
      {
        name: "stylePreference",
        label: "Style Preference",
        type: "select",
        options: [
          "Traditional",
          "Contemporary",
          "Artistic",
          "Documentary",
          "Cinematic",
          "Minimalist"
        ],
        required: false
      },
      {
        name: "specialRequests",
        label: "Special Requests",
        type: "textarea",
        placeholder: "Any specific photography requirements or shots...",
        required: false
      }
    ]
  },
  Videography: {
    title: "Videography Details",
    category: "Videography",
    type: "service-input",
    fields: [
      {
        name: "coverageType",
        label: "Coverage Type",
        type: "select",
        options: [
          "Event Highlights",
          "Full Ceremony Recording",
          "Cinematic Film",
          "Documentary Style",
          "Complete Coverage"
        ],
        required: true
      },
      {
        name: "duration",
        label: "Duration",
        type: "select",
        options: [
          "2-4 hours",
          "4-6 hours",
          "6-8 hours",
          "Full day"
        ],
        required: true
      },
      {
        name: "deliverables",
        label: "Deliverables",
        type: "multiselect",
        options: [
          "Highlight Reel (3-5 min)",
          "Short Film (5-10 min)",
          "Full Ceremony Video",
          "Raw Footage",
          "Social Media Clips"
        ],
        required: true
      },
      {
        name: "additionalServices",
        label: "Additional Services",
        type: "multiselect",
        options: [
          "Drone Footage",
          "Same-day Edit",
          "Multiple Cameras",
          "Special Effects",
          "Interviews"
        ],
        required: false
      },
      {
        name: "specialRequests",
        label: "Special Requests",
        type: "textarea",
        placeholder: "Any specific videography requirements...",
        required: false
      }
    ]
  },
  Music: {
    title: "Music/DJ Details",
    category: "Music",
    type: "service-input",
    fields: [
      {
        name: "musicType",
        label: "Music Type",
        type: "select",
        options: [
          "DJ",
          "Live Band",
          "Classical Musicians",
          "Instrumental",
          "Vocalist",
          "DJ + Live Music",
          "Custom"
        ],
        required: true
      },
      {
        name: "duration",
        label: "Duration",
        type: "select",
        options: [
          "2-3 hours",
          "3-4 hours",
          "4-6 hours",
          "Full event"
        ],
        required: true
      },
      {
        name: "musicGenres",
        label: "Music Genres",
        type: "multiselect",
        options: [
          "Bollywood",
          "Classical Indian",
          "Western Pop",
          "EDM/Dance",
          "Rock",
          "Sufi",
          "Folk",
          "Fusion"
        ],
        required: true
      },
      {
        name: "equipmentNeeded",
        label: "Equipment Needed",
        type: "multiselect",
        options: [
          "Sound System",
          "Lighting",
          "Microphones",
          "Instruments",
          "Dance Floor",
          "Karaoke Setup"
        ],
        required: false
      },
      {
        name: "specialRequests",
        label: "Special Requests",
        type: "textarea",
        placeholder: "Any specific songs or music requirements...",
        required: false
      }
    ]
  },
  Invitation: {
    title: "Invitation Details",
    category: "Invitation",
    type: "service-input",
    fields: [
      {
        name: "invitationType",
        label: "Invitation Type",
        type: "select",
        options: [
          "Digital Only",
          "Printed Only",
          "Digital + Printed",
          "Video Invitation",
          "Custom"
        ],
        required: true
      },
      {
        name: "designStyle",
        label: "Design Style",
        type: "select",
        options: [
          "Traditional",
          "Modern",
          "Minimalist",
          "Elegant",
          "Artistic",
          "Themed",
          "Custom"
        ],
        required: true
      },
      {
        name: "quantity",
        label: "Quantity (for printed)",
        type: "number",
        placeholder: "Number of invitations needed",
        required: false
      },
      {
        name: "additionalItems",
        label: "Additional Items",
        type: "multiselect",
        options: [
          "RSVP Cards",
          "Thank You Cards",
          "Direction Cards",
          "Program Details",
          "Custom Envelopes",
          "Digital RSVP Management"
        ],
        required: false
      },
      {
        name: "specialRequests",
        label: "Special Requests",
        type: "textarea",
        placeholder: "Any specific invitation requirements or content...",
        required: false
      }
    ]
  },
  Transportation: {
    title: "Transportation Details",
    category: "Transportation",
    type: "service-input",
    fields: [
      {
        name: "vehicleTypes",
        label: "Vehicle Types",
        type: "multiselect",
        options: [
          "Sedan",
          "SUV",
          "Luxury Car",
          "Vintage Car",
          "Mini Bus",
          "Coach Bus",
          "Decorated Vehicle"
        ],
        required: true
      },
      {
        name: "numberOfVehicles",
        label: "Number of Vehicles",
        type: "number",
        placeholder: "How many vehicles needed",
        required: true
      },
      {
        name: "duration",
        label: "Duration",
        type: "select",
        options: [
          "One-way transfer",
          "Return transfer",
          "4-6 hours",
          "Full day (8-10 hours)"
        ],
        required: true
      },
      {
        name: "specialRequests",
        label: "Special Requests",
        type: "textarea",
        placeholder: "Any specific transportation requirements...",
        required: false
      }
    ]
  },
  Gifts: {
    title: "Gifts & Favors Details",
    category: "Gifts",
    type: "service-input",
    fields: [
      {
        name: "giftType",
        label: "Gift Type",
        type: "select",
        options: [
          "Return Gifts for Guests",
          "VIP Gifts",
          "Welcome Hampers",
          "Thank You Gifts",
          "Custom"
        ],
        required: true
      },
      {
        name: "quantity",
        label: "Quantity",
        type: "number",
        placeholder: "Number of gifts needed",
        required: true
      },
      {
        name: "giftPreference",
        label: "Gift Preference",
        type: "select",
        options: [
          "Traditional",
          "Practical/Useful",
          "Decorative",
          "Edible/Sweets",
          "Personalized",
          "Eco-friendly",
          "Luxury"
        ],
        required: true
      },
      {
        name: "budgetPerPiece",
        label: "Budget Per Piece",
        type: "select",
        options: [
          "Under ₹200",
          "₹200-₹500",
          "₹500-₹1,000",
          "₹1,000-₹2,000",
          "Above ₹2,000"
        ],
        required: true
      },
      {
        name: "specialRequests",
        label: "Special Requests",
        type: "textarea",
        placeholder: "Any specific gift requirements or ideas...",
        required: false
      }
    ]
  },
  Lighting: {
    title: "Lighting Details",
    category: "Lighting",
    type: "service-input",
    fields: [
      {
        name: "lightingType",
        label: "Lighting Type",
        type: "multiselect",
        options: [
          "Ambient Lighting",
          "Stage Lighting",
          "Decorative Lighting",
          "LED Walls/Screens",
          "Special Effects Lighting",
          "Outdoor Lighting",
          "Custom"
        ],
        required: true
      },
      {
        name: "colorTheme",
        label: "Color Theme",
        type: "select",
        options: [
          "Warm White",
          "Cool White",
          "RGB/Multicolor",
          "Event Theme Colors",
          "Dynamic/Changing",
          "Custom"
        ],
        required: true
      },
      {
        name: "specialEffects",
        label: "Special Effects",
        type: "multiselect",
        options: [
          "Fog/Haze",
          "Laser Lights",
          "Moving Heads",
          "Projection Mapping",
          "Spotlights",
          "Strobes",
          "None"
        ],
        required: false
      },
      {
        name: "specialRequests",
        label: "Special Requests",
        type: "textarea",
        placeholder: "Any specific lighting requirements or effects...",
        required: false
      }
    ]
  }
};

// Define event flows for different event types
const eventFlows = {
  "house-warming": [
    serviceInputForms.Decoration,
    serviceInputForms.Catering,
    serviceInputForms.Priest,
    serviceInputForms.Photography,
    serviceInputForms.Videography,
    serviceInputForms.Invitation
  ],

  "birthday-party": [
    serviceInputForms.Decoration,
    serviceInputForms.Catering,
    serviceInputForms.Photography,
    serviceInputForms.Videography,
    serviceInputForms.Music,
    serviceInputForms.Gifts,
    serviceInputForms.Lighting
  ],

  "birthday": [
    serviceInputForms.Decoration,
    serviceInputForms.Catering,
    serviceInputForms.Photography,
    serviceInputForms.Videography,
    serviceInputForms.Music,
    serviceInputForms.Gifts,
    serviceInputForms.Lighting
  ],

  "wedding": [
    serviceInputForms.Decoration,
    serviceInputForms.Catering,
    serviceInputForms.Priest,
    serviceInputForms.Photography,
    serviceInputForms.Videography,
    serviceInputForms.Music,
    serviceInputForms.Transportation,
    serviceInputForms.Invitation,
    serviceInputForms.Gifts,
    serviceInputForms.Lighting
  ],

  "anniversary": [
    serviceInputForms.Decoration,
    serviceInputForms.Catering,
    serviceInputForms.Photography,
    serviceInputForms.Videography,
    serviceInputForms.Music,
    serviceInputForms.Gifts
  ],

  "baby-shower": [
    serviceInputForms.Decoration,
    serviceInputForms.Catering,
    serviceInputForms.Photography,
    serviceInputForms.Gifts
  ],

  "corporate-event": [
    serviceInputForms.Decoration,
    serviceInputForms.Catering,
    serviceInputForms.Photography,
    serviceInputForms.Videography,
    serviceInputForms.Music,
    serviceInputForms.Transportation,
    serviceInputForms.Lighting
  ],

  "corporate": [
    serviceInputForms.Decoration,
    serviceInputForms.Catering,
    serviceInputForms.Photography,
    serviceInputForms.Videography,
    serviceInputForms.Music,
    serviceInputForms.Transportation,
    serviceInputForms.Lighting
  ],

  "conference": [
    serviceInputForms.Decoration,
    serviceInputForms.Catering,
    serviceInputForms.Photography,
    serviceInputForms.Videography,
    serviceInputForms.Music,
    serviceInputForms.Transportation,
    serviceInputForms.Lighting
  ],

  "hindu-pooja": [
    serviceInputForms.Decoration,
    serviceInputForms.Catering,
    serviceInputForms.Priest,
    serviceInputForms.Photography
  ]
};

const BookingFlow = () => {
  const navigate = useNavigate();
  const { eventType } = useParams();
  // Always start with the basic details form
  const [flowStage, setFlowStage] = useState('basicDetails'); // 'basicDetails', 'serviceSelection', 'confirmation'
  const [stepIndex, setStepIndex] = useState(0);
  const [selectedOption, setSelectedOption] = useState(null);
  const [selections, setSelections] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [basicFormData, setBasicFormData] = useState({
    eventDate: '',
    eventTime: '',
    eventLocation: '',
    numberOfPersons: '',
    services: {
      Decoration: false,
      Catering: false,
      Priest: false,
      Photography: false,
      Videography: false,
      Music: false,
      Transportation: false,
      Invitation: false,
      Gifts: false,
      Lighting: false
    },
    additionalNotes: ''
  });

  // Get all available steps for this event type
  const allSteps = eventFlows[eventType] || [];

  // Log available services for debugging
  console.log("Event type:", eventType);
  console.log("Available steps for this event type:", allSteps);

  // Extract categories from the steps
  const availableCategories = allSteps.map(step => step.category);
  console.log("Available service categories:", availableCategories);

  // Filter steps based on selected services
  const getFilteredSteps = () => {
    if (flowStage === 'basicDetails') {
      return [];
    }

    // Filter steps based on selected services
    return allSteps.filter(step => {
      // Include steps for selected services
      return basicFormData.services[step.category] === true;
    });
  };

  const steps = getFilteredSteps();

  useEffect(() => {
    // Reset selected option when moving to a new step
    setSelectedOption(null);
  }, [stepIndex]);

  const handleBasicFormSubmit = () => {
    // Save basic details to selections
    setSelections({
      ...selections,
      BasicDetails: {
        eventDate: basicFormData.eventDate,
        eventTime: basicFormData.eventTime,
        eventLocation: basicFormData.eventLocation,
        numberOfPersons: basicFormData.numberOfPersons,
        additionalNotes: basicFormData.additionalNotes,
        price: '0' // No price for basic details
      }
    });

    // Move to service selection stage
    setFlowStage('serviceSelection');
    setStepIndex(0);
  };

  const handleOptionSelect = (option) => {
    setSelectedOption(option);
    setSelections({
      ...selections,
      [steps[stepIndex].category]: option
    });
  };

  // Create a ref to store the validate function from the current service form
  const [validateFormRef, setValidateFormRef] = React.useState(null);

  // Updated handleNextStep to validate the form before proceeding
  const handleNextStep = () => {
    // If we have a validation function, call it first
    if (validateFormRef && typeof validateFormRef === 'function') {
      // Call the validation function and check if it returns true
      const isValid = validateFormRef();
      if (!isValid) {
        // If validation fails, don't proceed
        return;
      }
    }

    if (stepIndex === steps.length - 1) {
      // If we're at the last step, move to confirmation
      setFlowStage('confirmation');
    } else {
      // Otherwise, move to the next step
      setStepIndex(stepIndex + 1);
    }
  };

  const handleConfirmBooking = async () => {
    try {
      setLoading(true);
      setError(null);

      // Create a new booking object
      const newBooking = {
        id: Date.now(), // Simple unique ID generation
        eventType: eventType.replace("-", " ").replace(/\b\w/g, l => l.toUpperCase()),
        date: basicFormData.eventDate,
        time: basicFormData.eventTime || 'Not specified',
        numberOfPersons: basicFormData.numberOfPersons,
        status: "Confirmed",
        details: {
          basicDetails: {
            eventDate: basicFormData.eventDate,
            eventTime: basicFormData.eventTime,
            eventLocation: basicFormData.eventLocation,
            numberOfPersons: basicFormData.numberOfPersons,
            additionalNotes: basicFormData.additionalNotes
          },
          // Only include selected services
          ...(basicFormData.services.Decoration && { decoration: selections.Decoration }),
          ...(basicFormData.services.Catering && { catering: selections.Catering }),
          ...(basicFormData.services.Priest && { priest: selections.Priest }),
          ...(basicFormData.services.Photography && { photography: selections.Photography }),
          ...(basicFormData.services.Videography && { videography: selections.Videography }),
          ...(basicFormData.services.Music && { music: selections.Music }),
          ...(basicFormData.services.Transportation && { transportation: selections.Transportation }),
          ...(basicFormData.services.Invitation && { invitation: selections.Invitation }),
          ...(basicFormData.services.Gifts && { gifts: selections.Gifts }),
          ...(basicFormData.services.Lighting && { lighting: selections.Lighting })
        },
        totalAmount: Object.values(selections)
          .filter(item => item !== selections.BasicDetails) // Exclude BasicDetails from calculation
          .reduce((sum, item) => {
            const price = item.price ? parseInt(item.price.replace(/,/g, '')) : 0;
            return sum + (isNaN(price) ? 0 : price);
          }, 0)
      };

      try {
        // Submit the booking to Netlify forms
        const formData = new FormData();

        // Add form-name field which Netlify requires
        formData.append('form-name', 'event-booking-confirmation');

        // Add basic details
        formData.append('eventType', newBooking.eventType);
        formData.append('eventDate', newBooking.date);
        formData.append('eventTime', newBooking.time);
        formData.append('numberOfPersons', newBooking.numberOfPersons);
        formData.append('totalAmount', newBooking.totalAmount);

        // Add selected services as JSON
        formData.append('bookingDetails', JSON.stringify(newBooking.details));

        // Submit the form data to Netlify
        await fetch('/', {
          method: 'POST',
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
          body: new URLSearchParams(formData).toString()
        });

        console.log('Booking submitted to Netlify successfully');
      } catch (netErr) {
        console.error('Error submitting to Netlify:', netErr);
        // Continue with local storage even if Netlify submission fails
      }

      // Get existing bookings from localStorage or initialize empty array
      const existingBookings = JSON.parse(localStorage.getItem('bookings') || '[]');

      // Add new booking to array
      const updatedBookings = [...existingBookings, newBooking];

      // Save to localStorage
      localStorage.setItem('bookings', JSON.stringify(updatedBookings));

      // Navigate to my-bookings with success state
      navigate('/my-bookings', {
        state: {
          bookingSuccess: true,
          bookingDetails: newBooking
        }
      });
    } catch (err) {
      setError('Failed to confirm booking. Please try again.');
      console.error('Booking error:', err);
    } finally {
      setLoading(false);
    }
  };

  // Add debugging to identify the issue
  console.log("Current event type:", eventType);
  console.log("Available event types:", Object.keys(eventFlows));
  console.log("Is event type in available types?", Object.keys(eventFlows).includes(eventType));

  // Only show error if the event type is completely unknown and not in our list
  if (!eventType || (eventType && !Object.keys(eventFlows).includes(eventType))) {
    return (
      <PageWrapper title="Error" subtitle="Event type not found">
        <div className="text-center">
          <p className="text-red-500 mb-4">
            {!eventType ? "No event type selected" : `Event type "${eventType}" is not supported`}
          </p>
          <p className="mb-4">Available event types: {Object.keys(eventFlows).join(', ')}</p>
          <Link
            to="/events"
            className="bg-[#ff5a5f] text-white px-6 py-2 rounded-full"
          >
            Back to Events
          </Link>
        </div>
      </PageWrapper>
    );
  }

  // Always allow all event types to proceed
  // This ensures we don't block any valid event types

  // If we're in service selection but there are no steps (no services selected), go back to basic details
  if (flowStage === 'serviceSelection' && steps.length === 0) {
    setFlowStage('basicDetails');
  }

  return (
    <PageWrapper
      title={`Book ${eventType.replace("-", " ")}`}
      subtitle={
        flowStage === 'basicDetails'
          ? "Enter basic event details"
          : flowStage === 'serviceSelection'
            ? "Select your preferred vendors and services"
            : "Review and confirm your booking"
      }
    >
      <div className="space-y-8">
        {/* Progress Indicator */}
        <div className="mb-10">
          <div className="relative">
            {/* Progress Bar */}
            <div className="absolute top-1/2 left-0 w-full h-1 bg-gray-200 dark:bg-gray-700 -translate-y-1/2 rounded-full"></div>
            <div
              className="absolute top-1/2 left-0 h-1 bg-gradient-to-r from-[#ff5a5f] to-[#ff8c8f] -translate-y-1/2 rounded-full transition-all duration-500"
              style={{
                width: flowStage === 'basicDetails'
                  ? '0%'
                  : flowStage === 'serviceSelection'
                    ? '50%'
                    : '100%'
              }}
            ></div>

            {/* Step Indicators */}
            <div className="relative flex justify-between">
              <div className="flex flex-col items-center">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center z-10
                  ${flowStage !== 'basicDetails'
                    ? 'bg-gradient-to-r from-[#ff5a5f] to-[#ff8c8f] text-white'
                    : 'bg-white dark:bg-dark-secondary border-2 border-[#ff5a5f] text-[#ff5a5f]'}
                  shadow-md transition-all duration-300`}
                >
                  {flowStage !== 'basicDetails' ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  ) : '1'}
                </div>
                <span className={`mt-2 text-sm font-medium
                  ${flowStage === 'basicDetails'
                    ? 'text-[#ff5a5f]'
                    : 'text-light-secondary dark:text-gray-400'}`}
                >
                  Basic Details
                </span>
              </div>

              <div className="flex flex-col items-center">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center z-10
                  ${flowStage === 'confirmation'
                    ? 'bg-gradient-to-r from-[#ff5a5f] to-[#ff8c8f] text-white'
                    : flowStage === 'serviceSelection'
                      ? 'bg-white dark:bg-dark-secondary border-2 border-[#ff5a5f] text-[#ff5a5f]'
                      : 'bg-white dark:bg-dark-secondary border-2 border-gray-300 dark:border-gray-600 text-gray-400'}
                  shadow-md transition-all duration-300`}
                >
                  {flowStage === 'confirmation'
                    ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    )
                    : '2'}
                </div>
                <span className={`mt-2 text-sm font-medium
                  ${flowStage === 'serviceSelection'
                    ? 'text-[#ff5a5f]'
                    : flowStage === 'confirmation'
                      ? 'text-light-secondary dark:text-gray-400'
                      : 'text-gray-400 dark:text-gray-500'}`}
                >
                  Service Selection
                </span>
              </div>

              <div className="flex flex-col items-center">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center z-10
                  ${flowStage === 'confirmation'
                    ? 'bg-white dark:bg-dark-secondary border-2 border-[#ff5a5f] text-[#ff5a5f]'
                    : 'bg-white dark:bg-dark-secondary border-2 border-gray-300 dark:border-gray-600 text-gray-400'}
                  shadow-md transition-all duration-300`}
                >
                  3
                </div>
                <span className={`mt-2 text-sm font-medium
                  ${flowStage === 'confirmation'
                    ? 'text-[#ff5a5f]'
                    : 'text-gray-400 dark:text-gray-500'}`}
                >
                  Confirmation
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Basic Details Form */}
        {flowStage === 'basicDetails' && (
          <BasicDetailsForm
            formData={basicFormData}
            setFormData={setBasicFormData}
            onSubmit={handleBasicFormSubmit}
            eventType={eventType}
            availableServices={availableCategories}
          />
        )}

        {/* Service Selection Steps */}
        {flowStage === 'serviceSelection' && steps.length > 0 && (
          <>
            {/* Service Selection Progress */}
            {steps.length > 1 && (
              <div className="mb-8 bg-white dark:bg-dark-secondary p-4 rounded-lg shadow-sm border border-gray-100 dark:border-gray-800">
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <span className="font-medium">Service:</span>
                    <span className="ml-2 font-bold text-[#ff5a5f]">{steps[stepIndex].title}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <span className="text-[#ff5a5f] font-bold">{stepIndex + 1}</span>
                    <span className="mx-1 text-gray-400">/</span>
                    <span className="text-gray-500 dark:text-gray-400">{steps.length}</span>
                  </div>
                </div>

                <div className="mt-3 relative">
                  {/* Progress Bar Background */}
                  <div className="h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                    {/* Progress Bar Fill */}
                    <div
                      className="h-full bg-gradient-to-r from-[#ff5a5f] to-[#ff8c8f] rounded-full transition-all duration-500"
                      style={{ width: `${(stepIndex / (steps.length - 1)) * 100}%` }}
                    ></div>
                  </div>

                  {/* Step Markers */}
                  <div className="absolute top-0 left-0 w-full flex justify-between -mt-2">
                    {steps.map((step, idx) => (
                      <div
                        key={idx}
                        className={`w-5 h-5 rounded-full flex items-center justify-center text-xs
                          ${idx < stepIndex
                            ? 'bg-gradient-to-r from-[#ff5a5f] to-[#ff8c8f] text-white'
                            : idx === stepIndex
                              ? 'bg-white border-2 border-[#ff5a5f] text-[#ff5a5f]'
                              : 'bg-white border border-gray-300 dark:border-gray-600 text-gray-400'}
                          transition-all duration-300 cursor-pointer`}
                        onClick={() => {
                          // Only allow going back to previous steps
                          if (idx < stepIndex) {
                            setStepIndex(idx);
                          }
                        }}
                        title={step.title}
                      >
                        {idx < stepIndex ? (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        ) : (
                          <span className="text-xs">{idx + 1}</span>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            <BookingStep
              step={steps[stepIndex]}
              selectedOption={selectedOption}
              onOptionSelect={handleOptionSelect}
              previousSelections={selections}
              validateRef={setValidateFormRef}
            />

            {error && (
              <div className="text-red-500 text-center py-2">
                {error}
              </div>
            )}

            <div className="flex justify-between items-center mt-8">
              {stepIndex > 0 ? (
                <button
                  className="px-6 py-3 rounded-lg bg-white dark:bg-dark-secondary border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 flex items-center shadow-sm"
                  onClick={() => setStepIndex(stepIndex - 1)}
                  disabled={loading}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  Previous Service
                </button>
              ) : (
                <button
                  className="px-6 py-3 rounded-lg bg-white dark:bg-dark-secondary border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 flex items-center shadow-sm"
                  onClick={() => setFlowStage('basicDetails')}
                  disabled={loading}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  Back to Details
                </button>
              )}
              <button
                className={`px-6 py-3 rounded-lg bg-gradient-to-r from-[#ff5a5f] to-[#ff8c8f] text-white font-medium hover:shadow-md transition-all duration-300 flex items-center
                  ${(!selectedOption || loading) && 'opacity-50 cursor-not-allowed'}`}
                onClick={handleNextStep}
                disabled={!selectedOption || loading}
              >
                {loading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </>
                ) : (
                  <>
                    {stepIndex === steps.length - 1 ? 'Review Booking' : (
                      <>
                        Save & Continue
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </>
                    )}
                  </>
                )}
              </button>
            </div>
          </>
        )}

        {/* Confirmation Page */}
        {flowStage === 'confirmation' && (
          <div className="space-y-8">
            <div className="bg-light-secondary dark:bg-dark-secondary p-8 rounded-lg shadow-md">
              <h3 className="text-2xl font-bold text-light-primary dark:text-white mb-6">
                Basic Details
              </h3>
              <div className="space-y-4 mb-6">
                <p className="text-light-secondary dark:text-gray-400">
                  Event Type: <span className="font-semibold">{eventType.replace("-", " ")}</span>
                </p>
                <p className="text-light-secondary dark:text-gray-400">
                  Event Date: <span className="font-semibold">{basicFormData.eventDate}</span>
                </p>
                <p className="text-light-secondary dark:text-gray-400">
                  Number of Persons: <span className="font-semibold">{basicFormData.numberOfPersons}</span>
                </p>
                {basicFormData.additionalNotes && (
                  <p className="text-light-secondary dark:text-gray-400">
                    Additional Notes: <span className="font-semibold">{basicFormData.additionalNotes}</span>
                  </p>
                )}
              </div>

              <BookingSummary selections={selections} eventType={eventType} />
            </div>

            {error && (
              <div className="text-red-500 text-center py-2">
                {error}
              </div>
            )}

            <div className="flex justify-between items-center">
              <button
                className="px-6 py-3 rounded-lg bg-white dark:bg-dark-secondary border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 flex items-center shadow-sm"
                onClick={() => setFlowStage('serviceSelection')}
                disabled={loading}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Back to Services
              </button>
              <button
                className={`px-6 py-3 rounded-lg bg-gradient-to-r from-[#ff5a5f] to-[#ff8c8f] text-white font-medium hover:shadow-md transition-all duration-300 flex items-center
                  ${loading && 'opacity-50 cursor-not-allowed'}`}
                onClick={handleConfirmBooking}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </>
                ) : (
                  <>
                    Confirm Booking
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </>
                )}
              </button>
            </div>
          </div>
        )}
      </div>
    </PageWrapper>
  );
};

export default BookingFlow;
