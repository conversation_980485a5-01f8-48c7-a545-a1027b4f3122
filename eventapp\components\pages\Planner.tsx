import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';

export default function PlannerPage() {
  return (
    <ScrollView style={styles.container}>
      <Text style={styles.header}>Personal Event Planner</Text>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Timeline</Text>
        <Text style={styles.sectionDesc}>Create and manage your event timeline</Text>
      </View>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Guest List</Text>
        <Text style={styles.sectionDesc}>Manage your guest list and RSVPs</Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  section: {
    backgroundColor: '#f7f7f7',
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 6,
  },
  sectionDesc: {
    fontSize: 15,
    color: '#555',
  },
}); 