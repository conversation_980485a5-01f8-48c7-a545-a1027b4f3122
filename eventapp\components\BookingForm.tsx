import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TextInput, TouchableOpacity, Modal } from 'react-native';

interface Option {
  id: string;
  title: string;
  description: string;
  price: number;
  image?: string;
}

interface Step {
  title: string;
  category: string;
  type: string;
  fields?: Field[];
  options: Option[];
}

interface Field {
  name: string;
  label: string;
  type: string;
  options?: string[];
  required: boolean;
  placeholder?: string;
}

interface BookingFormProps {
  step: Step;
  selectedOption: Option | null;
  onSelect: (category: string, option: Option) => void;
  onNext: () => void;
  onPrevious: () => void;
  isLastStep: boolean;
  isFirstStep: boolean;
}

const BookingForm: React.FC<BookingFormProps> = ({
  step,
  selectedOption,
  onSelect,
  onNext,
  onPrevious,
  isLastStep,
  isFirstStep,
}) => {
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [showDropdown, setShowDropdown] = useState<string | null>(null);

  const handleInputChange = (field: Field, value: string | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field.name]: value
    }));
    setShowDropdown(null);
  };

  const renderField = (field: Field) => {
    switch (field.type) {
      case 'select':
        return (
          <View style={styles.fieldContainer} key={field.name}>
            <Text style={styles.fieldLabel}>{field.label}</Text>
            <TouchableOpacity
              style={styles.selectButton}
              onPress={() => setShowDropdown(field.name)}
            >
              <Text style={styles.selectButtonText}>
                {formData[field.name] || 'Select an option'}
              </Text>
            </TouchableOpacity>
            <Modal
              visible={showDropdown === field.name}
              transparent
              animationType="slide"
              onRequestClose={() => setShowDropdown(null)}
            >
              <TouchableOpacity
                style={styles.modalOverlay}
                onPress={() => setShowDropdown(null)}
              >
                <View style={styles.modalContent}>
                  <ScrollView>
                    {field.options?.map((option) => (
                      <TouchableOpacity
                        key={option}
                        style={styles.modalOption}
                        onPress={() => handleInputChange(field, option)}
                      >
                        <Text style={styles.modalOptionText}>{option}</Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </View>
              </TouchableOpacity>
            </Modal>
          </View>
        );

      case 'multiselect':
        return (
          <View style={styles.fieldContainer} key={field.name}>
            <Text style={styles.fieldLabel}>{field.label}</Text>
            <View style={styles.multiSelectContainer}>
              {field.options?.map((option) => (
                <TouchableOpacity
                  key={option}
                  style={[
                    styles.multiSelectOption,
                    (formData[field.name] || []).includes(option) && styles.multiSelectOptionSelected
                  ]}
                  onPress={() => {
                    const currentValues = formData[field.name] || [];
                    const newValues = currentValues.includes(option)
                      ? currentValues.filter((v: string) => v !== option)
                      : [...currentValues, option];
                    handleInputChange(field, newValues);
                  }}
                >
                  <Text style={[
                    styles.multiSelectOptionText,
                    (formData[field.name] || []).includes(option) && styles.multiSelectOptionTextSelected
                  ]}>
                    {option}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        );

      case 'textarea':
        return (
          <View style={styles.fieldContainer} key={field.name}>
            <Text style={styles.fieldLabel}>{field.label}</Text>
            <TextInput
              style={styles.textArea}
              multiline
              numberOfLines={4}
              value={formData[field.name]}
              onChangeText={(value) => handleInputChange(field, value)}
              placeholder={field.placeholder}
            />
          </View>
        );

      default:
        return null;
    }
  };

  const renderServiceOptions = () => (
    <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.optionsContainer}>
      {step.options.map((option) => (
        <TouchableOpacity
          key={option.id}
          style={[
            styles.optionCard,
            selectedOption?.id === option.id && styles.selectedOptionCard
          ]}
          onPress={() => onSelect(step.category, option)}
        >
          {option.image && (
            <View style={styles.optionImageContainer}>
              {/* Add Image component here if needed */}
            </View>
          )}
          <View style={styles.optionContent}>
            <Text style={styles.optionTitle}>{option.title}</Text>
            <Text style={styles.optionDescription}>{option.description}</Text>
            <Text style={styles.optionPrice}>₹{option.price}</Text>
          </View>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{step.title}</Text>
      </View>

      <ScrollView style={styles.content}>
        {step.type === 'service-input' && step.fields ? (
          <View style={styles.formContainer}>
            {step.fields.map(renderField)}
          </View>
        ) : (
          renderServiceOptions()
        )}
      </ScrollView>

      <View style={styles.footer}>
        {!isFirstStep && (
          <TouchableOpacity
            style={[styles.button, styles.secondaryButton]}
            onPress={onPrevious}
          >
            <Text style={styles.secondaryButtonText}>Previous</Text>
          </TouchableOpacity>
        )}
        <TouchableOpacity
          style={[styles.button, styles.primaryButton]}
          onPress={onNext}
          disabled={!selectedOption && step.type !== 'service-input'}
        >
          <Text style={styles.primaryButtonText}>
            {isLastStep ? 'Review Booking' : 'Next'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  content: {
    flex: 1,
  },
  formContainer: {
    padding: 16,
  },
  fieldContainer: {
    marginBottom: 16,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  selectButton: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#fff',
  },
  selectButtonText: {
    fontSize: 16,
    color: '#333',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: '50%',
  },
  modalOption: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalOptionText: {
    fontSize: 16,
    color: '#333',
  },
  multiSelectContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  multiSelectOption: {
    margin: 4,
    padding: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    backgroundColor: '#fff',
  },
  multiSelectOptionSelected: {
    backgroundColor: '#ff5a5f',
    borderColor: '#ff5a5f',
  },
  multiSelectOptionText: {
    color: '#666',
  },
  multiSelectOptionTextSelected: {
    color: '#fff',
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    height: 100,
    textAlignVertical: 'top',
  },
  optionsContainer: {
    padding: 16,
  },
  optionCard: {
    width: 280,
    marginRight: 16,
    borderRadius: 12,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  selectedOptionCard: {
    borderWidth: 2,
    borderColor: '#ff5a5f',
  },
  optionImageContainer: {
    height: 160,
    backgroundColor: '#f5f5f5',
  },
  optionContent: {
    padding: 16,
  },
  optionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  optionDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  optionPrice: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ff5a5f',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  button: {
    flex: 1,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 8,
  },
  primaryButton: {
    backgroundColor: '#ff5a5f',
  },
  secondaryButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ff5a5f',
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonText: {
    color: '#ff5a5f',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default BookingForm; 