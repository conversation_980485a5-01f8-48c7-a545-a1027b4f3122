import React from 'react';

const ProfileDropdown = ({ isOpen, setIsOpen }) => {
  const avatarSeed = "john-doe";
  const avatarUrl = `https://api.dicebear.com/7.x/avataaars/svg?seed=${avatarSeed}&backgroundColor=b6e3f4`;

  return (
    <div className="relative profile-dropdown">
      <img 
        src={avatarUrl}
        alt="Profile" 
        className="w-10 h-10 rounded-full cursor-pointer border-2 border-gray-200 dark:border-gray-700 hover:border-[#ff5a5f] transition-colors"
        onClick={() => setIsOpen(!isOpen)} 
      />
      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-light-primary dark:bg-dark-secondary shadow-lg rounded-lg p-2 z-50">
          <div className="p-3 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <img 
                src={avatarUrl}
                alt="Profile" 
                className="w-12 h-12 rounded-full border-2 border-gray-200 dark:border-gray-700" 
              />
              <div>
                <p className="text-light-primary dark:text-white font-medium">John Doe</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileDropdown;