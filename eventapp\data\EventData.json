{"Birthday": {"metadata": {"title": "Birthday Parties", "description": "Memorable birthday celebrations", "images": ["https://images.unsplash.com/photo-1530103862676-de8c9debad1d", "https://images.unsplash.com/photo-1464349095431-e9a21285b5f3", "https://images.unsplash.com/photo-1513151233558-d860c5398176"], "icon": "cake", "path": "/event/booking?type=birthday"}, "questions": [{"id": "guestCount", "text": "How many guests are you expecting?", "type": "select", "options": ["1-20 guests", "21-50 guests", "51-100 guests", "101+ guests"], "required": true}, {"id": "eventDate", "text": "When is the birthday party?", "type": "date", "required": true}, {"id": "eventTime", "text": "What time will the party start?", "type": "time", "required": true}, {"id": "location", "text": "Where will the party take place?", "type": "text", "placeholder": "Enter venue or location", "required": true}, {"id": "theme", "text": "Do you have a party theme?", "type": "text", "placeholder": "e.g., <PERSON><PERSON><PERSON>, Princess, Sports", "required": false}, {"id": "services", "text": "Which services do you need for the party?", "type": "multiselect", "options": ["Decoration", "Catering", "Photography", "Videography", "Music/DJ", "Games & Activities", "Cake"], "required": true}], "serviceQuestions": {"Decoration": [{"id": "theme", "text": "What is the decoration theme?", "type": "text", "placeholder": "e.g., Balloons, Cartoon Characters", "required": false}, {"id": "areas", "text": "What areas need decorating?", "type": "text", "placeholder": "e.g., Stage, Tables, Entrance", "required": false}, {"id": "balloons", "text": "Do you want balloon decorations?", "type": "select", "options": ["Yes", "No"], "required": false}, {"id": "backdrop", "text": "Do you need a photo backdrop?", "type": "select", "options": ["Yes", "No"], "required": false}], "Catering": [{"id": "cuisine", "text": "What type of food do you prefer?", "type": "select", "options": ["Snacks & Finger Food", "Full Meal", "Desserts Only", "Custom"], "required": true}, {"id": "diet", "text": "Do you require any dietary restrictions?", "type": "multiselect", "options": ["Vegetarian", "Vegan", "Gluten-Free", "Nut-Free", "None"], "required": false}, {"id": "beverages", "text": "What beverages would you like to include?", "type": "multiselect", "options": ["Soft Drinks", "Juices", "<PERSON>ck<PERSON>", "Specialty Drinks"], "required": false}], "Photography": [{"id": "coverageType", "text": "What type of photography do you need?", "type": "select", "options": ["Candid", "Posed", "Both"], "required": true}, {"id": "duration", "text": "How many hours of coverage do you need?", "type": "select", "options": ["2-3 hours", "3-4 hours", "4-6 hours"], "required": true}], "Videography": [{"id": "coverageType", "text": "What type of video coverage do you need?", "type": "select", "options": ["Highlights Only", "Full Coverage"], "required": true}, {"id": "duration", "text": "How many hours of coverage do you need?", "type": "select", "options": ["2-3 hours", "3-4 hours", "4-6 hours"], "required": true}], "Music/DJ": [{"id": "musicType", "text": "What type of music do you prefer?", "type": "select", "options": ["Kids Songs", "Pop", "Mixed", "Custom"], "required": true}, {"id": "duration", "text": "How many hours of music do you need?", "type": "select", "options": ["2-3 hours", "3-4 hours", "4-6 hours"], "required": true}, {"id": "equipmentNeeded", "text": "What equipment do you need?", "type": "multiselect", "options": ["Sound System", "Microphones", "Karaoke Setup"], "required": false}], "Games & Activities": [{"id": "activityType", "text": "What type of activities do you want?", "type": "multiselect", "options": ["Magic Show", "Clown", "Face Painting", "Balloon Artist", "Games", "Dance Activities", "Other"], "required": true}, {"id": "duration", "text": "How long should the activities last?", "type": "select", "options": ["1-2 hours", "2-3 hours", "3-4 hours"], "required": true}], "Cake": [{"id": "cakeType", "text": "What type of cake do you want?", "type": "select", "options": ["Chocolate", "Vanilla", "Fruit", "Custom Design"], "required": true}, {"id": "cakeSize", "text": "What size of cake do you need?", "type": "select", "options": ["Small (1-2 kg)", "Medium (2-3 kg)", "Large (3-5 kg)", "Custom"], "required": true}, {"id": "design", "text": "Do you have a specific design in mind?", "type": "text", "placeholder": "Describe the cake design", "required": false}]}}, "Corporate": {"metadata": {"title": "Corporate Events", "description": "Professional business event planning", "images": ["https://images.unsplash.com/photo-1540575467063-178a50c2df87", "https://images.unsplash.com/photo-1517457373958-b7bdd4587205", "https://images.unsplash.com/photo-1511578314322-379afb476865"], "icon": "briefcase", "path": "/event/booking?type=corporate"}, "questions": [{"id": "eventType", "text": "What type of corporate event is this?", "type": "select", "options": ["Conference", "Seminar", "Team Building", "Product Launch", "Other"], "required": true}, {"id": "guestCount", "text": "How many attendees are you expecting?", "type": "select", "options": ["1-20 attendees", "21-50 attendees", "51-100 attendees", "101+ attendees"], "required": true}, {"id": "eventDate", "text": "When is the event?", "type": "date", "required": true}, {"id": "eventTime", "text": "What time will the event start?", "type": "time", "required": true}, {"id": "location", "text": "Where will the event take place?", "type": "text", "placeholder": "Enter venue or location", "required": true}, {"id": "services", "text": "Which services do you need for your event?", "type": "multiselect", "options": ["Decoration", "Catering", "Photography", "Videography", "Music/DJ", "Transportation", "Gifts & Favors", "Lighting"], "required": true}], "serviceQuestions": {"Decoration": [{"id": "theme", "text": "What is the decoration theme?", "type": "text", "placeholder": "e.g., Corporate Colors, Branding", "required": false}, {"id": "areas", "text": "What areas need decorating?", "type": "text", "placeholder": "e.g., Stage, Tables, Entrance", "required": false}, {"id": "branding", "text": "Do you need branding elements?", "type": "select", "options": ["Yes", "No"], "required": false}], "Catering": [{"id": "cuisine", "text": "What type of cuisine do you prefer?", "type": "select", "options": ["Continental", "Indian", "Multi-Cuisine", "Finger Food", "Custom"], "required": true}, {"id": "mealType", "text": "What type of meal service do you prefer?", "type": "select", "options": ["Breakfast", "Lunch", "Dinner", "High Tea", "Cocktail & Snacks"], "required": true}, {"id": "diet", "text": "Do you require any dietary restrictions?", "type": "multiselect", "options": ["Vegetarian", "Vegan", "Gluten-Free", "None"], "required": false}, {"id": "beverages", "text": "What beverages would you like to include?", "type": "multiselect", "options": ["Coffee & Tea", "Soft Drinks", "Juices", "<PERSON>ck<PERSON>", "Alcoholic Beverages"], "required": false}], "Photography": [{"id": "coverageType", "text": "What type of photography do you need?", "type": "select", "options": ["Event Coverage", "Group Photos", "Product Photos", "Both"], "required": true}, {"id": "duration", "text": "How many hours of coverage do you need?", "type": "select", "options": ["2-4 hours", "4-6 hours", "6-8 hours", "Full day"], "required": true}], "Videography": [{"id": "coverageType", "text": "What type of video coverage do you need?", "type": "select", "options": ["Event Highlights", "Full Coverage", "Product Showcase", "Both"], "required": true}, {"id": "duration", "text": "How many hours of coverage do you need?", "type": "select", "options": ["2-4 hours", "4-6 hours", "6-8 hours", "Full day"], "required": true}], "Music/DJ": [{"id": "musicType", "text": "What type of music do you prefer?", "type": "select", "options": ["Background Music", "Live Music", "DJ", "None"], "required": true}, {"id": "duration", "text": "How many hours of music do you need?", "type": "select", "options": ["2-3 hours", "3-4 hours", "4-6 hours", "Full event"], "required": true}], "Transportation": [{"id": "vehicleTypes", "text": "What type of vehicles do you need?", "type": "multiselect", "options": ["Sedan", "SUV", "Mini Bus", "Coach Bus"], "required": true}, {"id": "numberOfVehicles", "text": "How many vehicles do you need?", "type": "number", "required": true}, {"id": "duration", "text": "What duration of service do you need?", "type": "select", "options": ["One-way transfer", "Return transfer", "Full day"], "required": true}], "Gifts & Favors": [{"id": "giftType", "text": "What type of gifts do you want?", "type": "select", "options": ["Corporate Gifts", "Welcome Kits", "Thank You Gifts", "Custom"], "required": true}, {"id": "quantity", "text": "How many gifts do you need?", "type": "number", "required": true}, {"id": "branding", "text": "Do you want branded gifts?", "type": "select", "options": ["Yes", "No"], "required": true}], "Lighting": [{"id": "lightingType", "text": "What type of lighting do you need?", "type": "multiselect", "options": ["Stage Lighting", "Ambient Lighting", "Spotlights", "LED Walls/Screens", "Special Effects"], "required": true}, {"id": "colorTheme", "text": "What color theme do you prefer?", "type": "select", "options": ["Corporate Colors", "Neutral", "Dynamic", "Custom"], "required": true}]}}, "HouseWarming": {"metadata": {"title": "House Warming", "description": "Traditional house warming ceremony planning", "images": ["https://images.unsplash.com/photo-1600585154340-be6161a56a0c", "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c", "https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea"], "icon": "home", "path": "/event/booking?type=house-warming"}, "questions": [{"id": "guestCount", "text": "How many guests are you expecting?", "type": "select", "options": ["1-20 guests", "21-50 guests", "51-100 guests", "101+ guests"], "required": true}, {"id": "eventDate", "text": "When is the ceremony?", "type": "date", "required": true}, {"id": "eventTime", "text": "What time will the ceremony start?", "type": "time", "required": true}, {"id": "location", "text": "Where will the ceremony take place?", "type": "text", "placeholder": "Enter venue or location", "required": true}, {"id": "services", "text": "Which services do you need for the ceremony?", "type": "multiselect", "options": ["Decoration", "Catering", "Photography", "Videography", "Priest Services", "Lighting"], "required": true}], "serviceQuestions": {"Decoration": [{"id": "theme", "text": "What is the decoration theme?", "type": "text", "placeholder": "e.g., Traditional, Modern", "required": false}, {"id": "areas", "text": "What areas need decorating?", "type": "text", "placeholder": "e.g., Pooja Room, Living Room", "required": false}, {"id": "floral", "text": "Do you need floral arrangements?", "type": "select", "options": ["Yes", "No"], "required": false}], "Catering": [{"id": "cuisine", "text": "What type of cuisine do you prefer?", "type": "select", "options": ["Traditional", "Modern", "Mix"], "required": true}, {"id": "mealType", "text": "What type of meal service do you prefer?", "type": "select", "options": ["Breakfast", "Lunch", "Dinner", "High Tea"], "required": true}, {"id": "diet", "text": "Do you require any dietary restrictions?", "type": "multiselect", "options": ["Vegetarian", "Vegan", "<PERSON>", "None"], "required": false}], "Photography": [{"id": "coverageType", "text": "What type of photography do you need?", "type": "select", "options": ["Ceremony Only", "Full Event", "Family Portraits"], "required": true}, {"id": "duration", "text": "How many hours of coverage do you need?", "type": "select", "options": ["2-3 hours", "3-4 hours", "4-6 hours"], "required": true}], "Videography": [{"id": "coverageType", "text": "What type of video coverage do you need?", "type": "select", "options": ["Highlights", "Full Ceremony"], "required": true}, {"id": "duration", "text": "How many hours of coverage do you need?", "type": "select", "options": ["2-3 hours", "3-4 hours", "4-6 hours"], "required": true}], "Priest Services": [{"id": "ceremonyType", "text": "What type of ceremony is being conducted?", "type": "select", "options": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "required": true}, {"id": "language", "text": "What is the preferred language for the ceremony?", "type": "select", "options": ["Sanskrit with Hindi", "Sanskrit with English", "Hindi", "Regional Language"], "required": true}], "Lighting": [{"id": "lightingType", "text": "What type of lighting do you need?", "type": "multiselect", "options": ["Ambient", "Decorative", "Special Effects"], "required": true}, {"id": "colorTheme", "text": "What color theme do you prefer?", "type": "select", "options": ["Warm", "Cool", "Traditional", "Modern"], "required": true}]}}, "Conference": {"metadata": {"title": "Conferences", "description": "Large-scale conference organization", "images": ["https://images.unsplash.com/photo-1540575467063-178a50c2df87", "https://images.unsplash.com/photo-1505373877841-8d25f7d46678", "https://images.unsplash.com/photo-1515187029135-18ee286d815b"], "icon": "users", "path": "/event/booking?type=conference"}, "questions": [{"id": "eventType", "text": "What type of conference is this?", "type": "select", "options": ["Academic", "Business", "Technology", "Medical", "Other"], "required": true}, {"id": "guestCount", "text": "How many attendees are you expecting?", "type": "select", "options": ["1-50 attendees", "51-100 attendees", "101-200 attendees", "201-500 attendees", "500+ attendees"], "required": true}, {"id": "eventDate", "text": "When is the conference?", "type": "date", "required": true}, {"id": "eventTime", "text": "What time will the conference start?", "type": "time", "required": true}, {"id": "location", "text": "Where will the conference take place?", "type": "text", "placeholder": "Enter venue or location", "required": true}, {"id": "services", "text": "Which services do you need for your conference?", "type": "multiselect", "options": ["Decoration", "Catering", "Photography", "Videography", "AV Equipment", "Transportation", "Accommodation", "Lighting"], "required": true}], "serviceQuestions": {"Decoration": [{"id": "theme", "text": "What is the decoration theme?", "type": "text", "placeholder": "e.g., Corporate, Branding", "required": false}, {"id": "areas", "text": "What areas need decorating?", "type": "text", "placeholder": "e.g., Stage, Registration, Breakout Rooms", "required": false}, {"id": "branding", "text": "Do you need branding elements?", "type": "select", "options": ["Yes", "No"], "required": false}], "Catering": [{"id": "cuisine", "text": "What type of cuisine do you prefer?", "type": "select", "options": ["Continental", "Indian", "Multi-Cuisine", "Finger Food"], "required": true}, {"id": "mealType", "text": "What type of meal service do you prefer?", "type": "select", "options": ["Breakfast", "Lunch", "Dinner", "High Tea", "Cocktail & Snacks"], "required": true}, {"id": "diet", "text": "Do you require any dietary restrictions?", "type": "multiselect", "options": ["Vegetarian", "Vegan", "Gluten-Free", "None"], "required": false}], "Photography": [{"id": "coverageType", "text": "What type of photography do you need?", "type": "select", "options": ["Event Coverage", "Group Photos", "Speaker Photos", "All"], "required": true}, {"id": "duration", "text": "How many hours of coverage do you need?", "type": "select", "options": ["2-4 hours", "4-6 hours", "6-8 hours", "Full day"], "required": true}], "Videography": [{"id": "coverageType", "text": "What type of video coverage do you need?", "type": "select", "options": ["Highlights", "Full Coverage", "Speaker Recordings", "All"], "required": true}, {"id": "duration", "text": "How many hours of coverage do you need?", "type": "select", "options": ["2-4 hours", "4-6 hours", "6-8 hours", "Full day"], "required": true}], "AV Equipment": [{"id": "equipment", "text": "What AV equipment do you need?", "type": "multiselect", "options": ["Projector", "Screens", "Sound System", "Microphones", "Recording Equipment", "Video Conferencing"], "required": true}, {"id": "setup", "text": "Do you need setup and technical support?", "type": "select", "options": ["Yes", "No"], "required": true}], "Transportation": [{"id": "vehicleTypes", "text": "What type of vehicles do you need?", "type": "multiselect", "options": ["Sedan", "SUV", "Mini Bus", "Coach Bus"], "required": true}, {"id": "numberOfVehicles", "text": "How many vehicles do you need?", "type": "number", "required": true}], "Accommodation": [{"id": "roomTypes", "text": "What type of rooms do you need?", "type": "multiselect", "options": ["Standard", "Deluxe", "Suite", "All"], "required": true}, {"id": "numberOfRooms", "text": "How many rooms do you need?", "type": "number", "required": true}, {"id": "duration", "text": "How many nights?", "type": "number", "required": true}], "Lighting": [{"id": "lightingType", "text": "What type of lighting do you need?", "type": "multiselect", "options": ["Stage Lighting", "Ambient", "Spotlights", "LED Walls"], "required": true}, {"id": "colorTheme", "text": "What color theme do you prefer?", "type": "select", "options": ["Corporate", "Neutral", "Dynamic", "Custom"], "required": true}]}}}