import React, { useState, useEffect } from 'react';
import { Check, Settings, Sparkles } from 'lucide-react';

const InvitationForm = ({ onSubmit, previousData }) => {
  const [formData, setFormData] = useState({
    invitationType: '',
    designStyle: '',
    quantity: '',
    additionalItems: [],
    deliveryMethod: '',
    specialRequests: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [showSuccess, setShowSuccess] = useState(false);

  // Initialize form data with previous data if available
  useEffect(() => {
    if (previousData) {
      setFormData(prevData => ({
        ...prevData,
        ...previousData
      }));
    }
  }, [previousData]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (type === 'checkbox') {
      if (checked) {
        setFormData({
          ...formData,
          [name]: [...(formData[name] || []), value]
        });
      } else {
        setFormData({
          ...formData,
          [name]: (formData[name] || []).filter(item => item !== value)
        });
      }
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // Validate form and submit
  const validateAndSubmit = () => {
    const errors = {};

    // Check required fields
    if (!formData.invitationType) errors.invitationType = 'Please select an invitation type';
    if (!formData.designStyle) errors.designStyle = 'Please select a design style';
    if (formData.invitationType && formData.invitationType.includes('Printed') && !formData.quantity) {
      errors.quantity = 'Please enter the quantity for printed invitations';
    }

    if (Object.keys(errors).length === 0) {
      // Form is valid, submit
      onSubmit({
        serviceDetails: formData,
        category: 'Invitation',
        name: 'Invitation Service'
      });

      // Show success message
      setShowSuccess(true);

      // Hide success message after 3 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    } else {
      // Form has errors
      setFormErrors(errors);
    }
  };

  return (
    <div className="bg-white dark:bg-dark-secondary p-6 rounded-lg shadow-md">
      {showSuccess && (
        <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg text-green-700 dark:text-green-300 flex items-center">
          <Check className="mr-2" size={18} />
          Invitation details saved successfully!
        </div>
      )}

      <p className="mb-6 text-light-secondary dark:text-gray-400 flex items-center">
        <Settings className="mr-2 text-[#ff5a5f]" size={16} />
        Please provide details for your invitation requirements
      </p>

      <div className="space-y-6">
        {/* Invitation Type */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Invitation Type <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="relative">
            <select
              name="invitationType"
              value={formData.invitationType || ''}
              onChange={handleInputChange}
              className={`w-full p-3 rounded-lg border ${formErrors.invitationType ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none`}
            >
              <option value="">Select Invitation Type</option>
              <option value="Digital Only">Digital Only</option>
              <option value="Printed Only">Printed Only</option>
              <option value="Digital + Printed">Digital + Printed</option>
              <option value="Video Invitation">Video Invitation</option>
              <option value="Custom">Custom</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
            {formErrors.invitationType && (
              <p className="mt-1 text-sm text-red-500">{formErrors.invitationType}</p>
            )}
          </div>
        </div>

        {/* Design Style */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Design Style <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="relative">
            <select
              name="designStyle"
              value={formData.designStyle || ''}
              onChange={handleInputChange}
              className={`w-full p-3 rounded-lg border ${formErrors.designStyle ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none`}
            >
              <option value="">Select Design Style</option>
              <option value="Traditional">Traditional</option>
              <option value="Modern">Modern</option>
              <option value="Minimalist">Minimalist</option>
              <option value="Elegant">Elegant</option>
              <option value="Artistic">Artistic</option>
              <option value="Themed">Themed</option>
              <option value="Custom">Custom</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
            {formErrors.designStyle && (
              <p className="mt-1 text-sm text-red-500">{formErrors.designStyle}</p>
            )}
          </div>
        </div>

        {/* Quantity (for printed) */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Quantity (for printed)
            {formData.invitationType && formData.invitationType.includes('Printed') && (
              <span className="text-[#ff5a5f]">*</span>
            )}
          </label>
          <input
            type="number"
            name="quantity"
            value={formData.quantity || ''}
            onChange={handleInputChange}
            placeholder="Number of invitations needed"
            min="1"
            className={`w-full p-3 rounded-lg border ${formErrors.quantity ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                     bg-white dark:bg-dark-primary text-light-primary dark:text-white
                     focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent`}
          />
          {formErrors.quantity && (
            <p className="mt-1 text-sm text-red-500">{formErrors.quantity}</p>
          )}
        </div>

        {/* Delivery Method */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Delivery Method
          </label>
          <div className="relative">
            <select
              name="deliveryMethod"
              value={formData.deliveryMethod || ''}
              onChange={handleInputChange}
              className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none"
            >
              <option value="">Select Delivery Method</option>
              <option value="Email">Email</option>
              <option value="WhatsApp">WhatsApp</option>
              <option value="Courier">Courier</option>
              <option value="Hand Delivery">Hand Delivery</option>
              <option value="Self Pickup">Self Pickup</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
          </div>
        </div>

        {/* Additional Items */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Additional Items
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {["RSVP Cards", "Thank You Cards", "Direction Cards", "Program Details", "Custom Envelopes", "Digital RSVP Management", "QR Codes", "Gift Registry Cards"].map((option, i) => (
              <div key={i} className="flex items-start">
                <input
                  type="checkbox"
                  id={`additionalItems-${i}`}
                  name="additionalItems"
                  value={option}
                  checked={(formData.additionalItems || []).includes(option)}
                  onChange={handleInputChange}
                  className="h-4 w-4 mt-1 text-[#ff5a5f] focus:ring-[#ff5a5f] border-gray-300 rounded"
                />
                <label htmlFor={`additionalItems-${i}`} className="ml-2 block text-sm text-light-primary dark:text-white">
                  {option}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Special Requests */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Special Requests
          </label>
          <textarea
            name="specialRequests"
            value={formData.specialRequests || ''}
            onChange={handleInputChange}
            placeholder="Any specific invitation requirements or content..."
            rows="3"
            className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                     bg-white dark:bg-dark-primary text-light-primary dark:text-white
                     focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent"
          />
        </div>
      </div>

      <div className="mt-8 flex justify-end">
        <button
          type="button"
          onClick={validateAndSubmit}
          className="px-6 py-3 rounded-lg bg-gradient-to-r from-[#ff5a5f] to-[#ff8c8f] text-white font-medium hover:shadow-md transition-all duration-300 flex items-center"
        >
          {previousData ? (
            <>
              <Check className="mr-2" size={18} />
              Update Invitation Details
            </>
          ) : (
            <>
              <Sparkles className="mr-2" size={18} />
              Save Invitation Details
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default InvitationForm;
