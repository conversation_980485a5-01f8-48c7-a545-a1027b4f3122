import React, { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import {
  Calendar,
  MapPin,
  Users,
  Clock,
  Cake,
  Home,
  Heart,
  Baby,
  Music,
  Utensils,
  Camera,
  Gift,
  DollarSign,
  Phone,
  Mail,
  User,
  FileText,
  Sparkles
} from 'lucide-react';

const EventBookingForm = ({ onSubmitSuccess }) => {
  const [formState, setFormState] = useState({
    fullName: '',
    email: '',
    phone: '',
    eventType: '',
    eventDate: '',
    eventTime: '',
    guestCount: '',
    location: '',
    venueType: '',
    budget: '',
    preferredTheme: '',
    dietaryRequirements: '',
    selectedServices: {
      decoration: false,
      catering: false,
      photography: false,
      videography: false,
      music: false,
      transportation: false,
      invitation: false,
      priestServices: false,
      giftFavors: false,
      lighting: false,
      flowerArrangements: false,
      bartending: false,
      security: false,
      cleanup: false
    },
    additionalInfo: '',
    submitting: false,
    success: false,
    error: null
  });

  // Track if event type has been selected to show conditional fields
  const [showServiceOptions, setShowServiceOptions] = useState(false);

  // Update showServiceOptions when eventType changes
  useEffect(() => {
    if (formState.eventType) {
      setShowServiceOptions(true);
    } else {
      setShowServiceOptions(false);
    }
  }, [formState.eventType]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (type === 'checkbox') {
      // For service checkboxes
      setFormState(prev => ({
        ...prev,
        selectedServices: {
          ...prev.selectedServices,
          [name]: checked
        }
      }));
    } else {
      // For other inputs
      setFormState(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    setFormState(prev => ({ ...prev, submitting: true, error: null }));

    try {
      // Get form data
      const form = e.target;
      const formData = new FormData(form);

      // Add form-name field which Netlify requires
      formData.append('form-name', 'event-booking');

      // Submit the form data to Netlify
      const response = await fetch('/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams(formData).toString()
      });

      if (!response.ok) {
        throw new Error(`Form submission failed: ${response.status}`);
      }

      // Reset form and show success message
      setFormState({
        fullName: '',
        email: '',
        phone: '',
        eventType: '',
        eventDate: '',
        eventTime: '',
        guestCount: '',
        location: '',
        venueType: '',
        budget: '',
        preferredTheme: '',
        dietaryRequirements: '',
        selectedServices: {
          decoration: false,
          catering: false,
          photography: false,
          videography: false,
          music: false,
          transportation: false,
          invitation: false,
          priestServices: false,
          giftFavors: false,
          lighting: false,
          flowerArrangements: false,
          bartending: false,
          security: false,
          cleanup: false
        },
        additionalInfo: '',
        submitting: false,
        success: true,
        error: null
      });

      if (onSubmitSuccess) {
        // Wait a moment before closing the modal to show success message
        setTimeout(() => {
          onSubmitSuccess();
        }, 3000);
      }
    } catch (error) {
      console.error('Form submission error:', error);
      setFormState(prev => ({
        ...prev,
        submitting: false,
        error: 'There was an error submitting your booking request. Please try again.'
      }));
    }
  };

  // Expanded event types with icons
  const eventTypes = [
    { value: 'Birthday Party', icon: <Cake size={18} /> },
    { value: 'House Warming', icon: <Home size={18} /> },
    { value: 'Hindu Pooja', icon: <Sparkles size={18} /> },
    { value: 'Anniversary', icon: <Heart size={18} /> },
    { value: 'Baby Shower', icon: <Baby size={18} /> },
    { value: 'Wedding', icon: <Heart size={18} /> },
    { value: 'Engagement', icon: <Gift size={18} /> },
    { value: 'Corporate Event', icon: <FileText size={18} /> },
    { value: 'Graduation', icon: <User size={18} /> },
    { value: 'Retirement Party', icon: <Gift size={18} /> },
    { value: 'Festival Celebration', icon: <Music size={18} /> },
    { value: 'Other', icon: <Sparkles size={18} /> }
  ];

  // Venue types
  const venueTypes = [
    'Home/Residence',
    'Banquet Hall',
    'Hotel',
    'Restaurant',
    'Outdoor Garden',
    'Beach',
    'Temple/Religious Venue',
    'Community Center',
    'Corporate Space',
    'Farm House',
    'Other'
  ];

  // Budget ranges
  const budgetRanges = [
    'Under ₹50,000',
    '₹50,000 - ₹1,00,000',
    '₹1,00,000 - ₹2,00,000',
    '₹2,00,000 - ₹5,00,000',
    'Above ₹5,00,000'
  ];

  // Theme options based on event type
  const getThemeOptions = () => {
    switch(formState.eventType) {
      case 'Birthday Party':
        return ['Classic', 'Superhero', 'Princess', 'Sports', 'Vintage', 'Bollywood', 'Hollywood', 'Cartoon Characters', 'Custom'];
      case 'House Warming':
        return ['Traditional', 'Modern', 'Fusion', 'Minimalist', 'Eco-friendly', 'Custom'];
      case 'Hindu Pooja':
        return ['Traditional', 'Regional Style', 'Festive', 'Simple', 'Elaborate', 'Custom'];
      case 'Wedding':
        return ['Traditional', 'Royal', 'Beach', 'Garden', 'Destination', 'Bollywood', 'Fusion', 'Minimalist', 'Custom'];
      case 'Baby Shower':
        return ['Gender Reveal', 'Storybook', 'Animal Theme', 'Traditional', 'Modern', 'Custom'];
      default:
        return ['Traditional', 'Modern', 'Fusion', 'Minimalist', 'Elaborate', 'Themed', 'Custom'];
    }
  };

  if (formState.success) {
    return (
      <div className="text-center py-6 sm:py-8 px-4 sm:px-0">
        <div className="mb-4 text-celebrate-orange">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 sm:h-16 sm:w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 className="text-xl sm:text-2xl font-bold mb-2">Booking Request Submitted!</h3>
        <p className="text-gray-600 mb-6 text-sm sm:text-base">Thank you for your interest in booking an event with us. Our team will contact you shortly to discuss the details.</p>
        <Button
          onClick={onSubmitSuccess}
          className="bg-celebrate-orange hover:bg-celebrate-red text-white w-full sm:w-auto px-6 py-2 sm:py-3"
        >
          Close
        </Button>
      </div>
    );
  }

  return (
    <form
      onSubmit={handleSubmit}
      className="space-y-6 sm:space-y-8"
      name="event-booking"
      method="POST"
      data-netlify="true"
      netlify-honeypot="bot-field"
    >
      {/* Hidden field for Netlify forms */}
      <input type="hidden" name="form-name" value="event-booking" />
      <p className="hidden">
        <label>Don't fill this out if you're human: <input name="bot-field" /></label>
      </p>

      {/* Form Header with Decorative Elements */}
      <div className="text-center mb-6">
        <h2 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-2">Event Booking Request</h2>
        <p className="text-gray-600">Fill out the form below to start planning your perfect event</p>
        <div className="flex justify-center mt-4">
          <div className="w-20 h-1 bg-gradient-to-r from-celebrate-orange to-celebrate-red rounded-full"></div>
        </div>
      </div>

      {/* Personal Information Section */}
      <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
        <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-4 flex items-center">
          <User className="mr-2 text-celebrate-orange" size={22} />
          Personal Information
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
          <div>
            <label htmlFor="fullName" className="block text-sm sm:text-base font-medium text-gray-700 mb-1">
              Full Name *
            </label>
            <div className="flex items-center">
              <div className="text-gray-500 mr-2">
                <User size={18} />
              </div>
              <input
                type="text"
                id="fullName"
                name="fullName"
                value={formState.fullName}
                onChange={handleChange}
                required
                className="w-full px-3 sm:px-4 py-2 sm:py-3 text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-celebrate-orange focus:border-celebrate-orange transition-all"
              />
            </div>
          </div>

          <div>
            <label htmlFor="email" className="block text-sm sm:text-base font-medium text-gray-700 mb-1">
              Email Address *
            </label>
            <div className="flex items-center">
              <div className="text-gray-500 mr-2">
                <Mail size={18} />
              </div>
              <input
                type="email"
                id="email"
                name="email"
                value={formState.email}
                onChange={handleChange}
                required
                className="w-full px-3 sm:px-4 py-2 sm:py-3 text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-celebrate-orange focus:border-celebrate-orange transition-all"
              />
            </div>
          </div>

          <div>
            <label htmlFor="phone" className="block text-sm sm:text-base font-medium text-gray-700 mb-1">
              Phone Number *
            </label>
            <div className="flex items-center">
              <div className="text-gray-500 mr-2">
                <Phone size={18} />
              </div>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formState.phone}
                onChange={handleChange}
                required
                placeholder="+91 XXXXX XXXXX"
                className="w-full px-3 sm:px-4 py-2 sm:py-3 text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-celebrate-orange focus:border-celebrate-orange transition-all"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Event Details Section */}
      <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
        <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-4 flex items-center">
          <Calendar className="mr-2 text-celebrate-orange" size={22} />
          Event Details
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
          <div>
            <label htmlFor="eventType" className="block text-sm sm:text-base font-medium text-gray-700 mb-1">
              Event Type *
            </label>
            <div className="relative">
              <select
                id="eventType"
                name="eventType"
                value={formState.eventType}
                onChange={handleChange}
                required
                className="w-full pl-10 pr-3 sm:pr-4 py-2 sm:py-3 text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-celebrate-orange focus:border-celebrate-orange transition-all appearance-none"
              >
                <option value="">Select event type</option>
                {eventTypes.map(type => (
                  <option key={type.value} value={type.value}>{type.value}</option>
                ))}
              </select>
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                {formState.eventType ?
                  eventTypes.find(t => t.value === formState.eventType)?.icon || <Sparkles size={18} />
                  : <Sparkles size={18} />}
              </div>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                  <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                </svg>
              </div>
            </div>
          </div>

          <div>
            <label htmlFor="eventDate" className="block text-sm sm:text-base font-medium text-gray-700 mb-1">
              Event Date *
            </label>
            <div className="flex items-center">
              <div className="text-gray-500 mr-2">
                <Calendar size={18} />
              </div>
              <input
                type="date"
                id="eventDate"
                name="eventDate"
                value={formState.eventDate}
                onChange={handleChange}
                required
                min={new Date().toISOString().split('T')[0]}
                className="w-full px-3 sm:px-4 py-2 sm:py-3 text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-celebrate-orange focus:border-celebrate-orange transition-all"
              />
            </div>
          </div>

          <div>
            <label htmlFor="eventTime" className="block text-sm sm:text-base font-medium text-gray-700 mb-1">
              Event Time *
            </label>
            <div className="flex items-center">
              <div className="text-gray-500 mr-2">
                <Clock size={18} />
              </div>
              <input
                type="time"
                id="eventTime"
                name="eventTime"
                value={formState.eventTime}
                onChange={handleChange}
                required
                className="w-full px-3 sm:px-4 py-2 sm:py-3 text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-celebrate-orange focus:border-celebrate-orange transition-all"
              />
            </div>
          </div>

          <div>
            <label htmlFor="guestCount" className="block text-sm sm:text-base font-medium text-gray-700 mb-1">
              Number of Guests *
            </label>
            <div className="flex items-center">
              <div className="text-gray-500 mr-2">
                <Users size={18} />
              </div>
              <input
                type="number"
                id="guestCount"
                name="guestCount"
                value={formState.guestCount}
                onChange={handleChange}
                required
                min="1"
                className="w-full px-3 sm:px-4 py-2 sm:py-3 text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-celebrate-orange focus:border-celebrate-orange transition-all"
              />
            </div>
          </div>

          <div>
            <label htmlFor="location" className="block text-sm sm:text-base font-medium text-gray-700 mb-1">
              Event Location *
            </label>
            <div className="flex items-center">
              <div className="text-gray-500 mr-2">
                <MapPin size={18} />
              </div>
              <input
                type="text"
                id="location"
                name="location"
                value={formState.location}
                onChange={handleChange}
                required
                placeholder="City, State"
                className="w-full px-3 sm:px-4 py-2 sm:py-3 text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-celebrate-orange focus:border-celebrate-orange transition-all"
              />
            </div>
          </div>

          <div>
            <label htmlFor="venueType" className="block text-sm sm:text-base font-medium text-gray-700 mb-1">
              Venue Type *
            </label>
            <div className="relative">
              <select
                id="venueType"
                name="venueType"
                value={formState.venueType}
                onChange={handleChange}
                required
                className="w-full pl-10 pr-3 sm:pr-4 py-2 sm:py-3 text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-celebrate-orange focus:border-celebrate-orange transition-all appearance-none"
              >
                <option value="">Select venue type</option>
                {venueTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                <Home size={18} />
              </div>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                  <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Budget and Theme Section */}
      <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
        <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-4 flex items-center">
          <DollarSign className="mr-2 text-celebrate-orange" size={22} />
          Budget & Theme
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
          <div>
            <label htmlFor="budget" className="block text-sm sm:text-base font-medium text-gray-700 mb-1">
              Budget Range *
            </label>
            <div className="relative">
              <select
                id="budget"
                name="budget"
                value={formState.budget}
                onChange={handleChange}
                required
                className="w-full pl-10 pr-3 sm:pr-4 py-2 sm:py-3 text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-celebrate-orange focus:border-celebrate-orange transition-all appearance-none"
              >
                <option value="">Select budget range</option>
                {budgetRanges.map(range => (
                  <option key={range} value={range}>{range}</option>
                ))}
              </select>
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                <DollarSign size={18} />
              </div>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                  <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                </svg>
              </div>
            </div>
          </div>

          {formState.eventType && (
            <div>
              <label htmlFor="preferredTheme" className="block text-sm sm:text-base font-medium text-gray-700 mb-1">
                Preferred Theme
              </label>
              <div className="relative">
                <select
                  id="preferredTheme"
                  name="preferredTheme"
                  value={formState.preferredTheme}
                  onChange={handleChange}
                  className="w-full pl-10 pr-3 sm:pr-4 py-2 sm:py-3 text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-celebrate-orange focus:border-celebrate-orange transition-all appearance-none"
                >
                  <option value="">Select theme (optional)</option>
                  {getThemeOptions().map(theme => (
                    <option key={theme} value={theme}>{theme}</option>
                  ))}
                </select>
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                  <Sparkles size={18} />
                </div>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                  <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                    <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                  </svg>
                </div>
              </div>
            </div>
          )}

          <div className="sm:col-span-2">
            <label htmlFor="dietaryRequirements" className="block text-sm sm:text-base font-medium text-gray-700 mb-1">
              Dietary Requirements/Preferences
            </label>
            <div className="flex items-center">
              <div className="text-gray-500 mr-2">
                <Utensils size={18} />
              </div>
              <input
                type="text"
                id="dietaryRequirements"
                name="dietaryRequirements"
                value={formState.dietaryRequirements}
                onChange={handleChange}
                placeholder="Vegetarian, Vegan, Gluten-free, etc."
                className="w-full px-3 sm:px-4 py-2 sm:py-3 text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-celebrate-orange focus:border-celebrate-orange transition-all"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Services Selection */}
      <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
        <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-4 flex items-center">
          <Sparkles className="mr-2 text-celebrate-orange" size={22} />
          Services Required
        </h3>
        <p className="text-sm text-gray-600 mb-4">
          Select the services you need for your event. Our team will provide customized options based on your selections.
        </p>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
          {/* Decoration Services */}
          <div className={`p-4 rounded-lg border ${formState.selectedServices.decoration
            ? 'border-celebrate-orange bg-orange-50'
            : 'border-gray-200 hover:border-gray-300'} transition-all cursor-pointer`}
            onClick={() => setFormState(prev => ({
              ...prev,
              selectedServices: {
                ...prev.selectedServices,
                decoration: !prev.selectedServices.decoration
              }
            }))}
          >
            <div className="flex items-start">
              <input
                type="checkbox"
                id="decoration"
                name="decoration"
                checked={formState.selectedServices.decoration}
                onChange={handleChange}
                className="h-5 w-5 mt-0.5 text-celebrate-orange focus:ring-celebrate-orange border-gray-300 rounded"
              />
              <div className="ml-3">
                <label htmlFor="decoration" className="font-medium text-gray-800 cursor-pointer">Decoration</label>
                <p className="text-sm text-gray-600 mt-1">Theme setup, floral arrangements, venue styling</p>
              </div>
            </div>
          </div>

          {/* Catering Services */}
          <div className={`p-4 rounded-lg border ${formState.selectedServices.catering
            ? 'border-celebrate-orange bg-orange-50'
            : 'border-gray-200 hover:border-gray-300'} transition-all cursor-pointer`}
            onClick={() => setFormState(prev => ({
              ...prev,
              selectedServices: {
                ...prev.selectedServices,
                catering: !prev.selectedServices.catering
              }
            }))}
          >
            <div className="flex items-start">
              <input
                type="checkbox"
                id="catering"
                name="catering"
                checked={formState.selectedServices.catering}
                onChange={handleChange}
                className="h-5 w-5 mt-0.5 text-celebrate-orange focus:ring-celebrate-orange border-gray-300 rounded"
              />
              <div className="ml-3">
                <label htmlFor="catering" className="font-medium text-gray-800 cursor-pointer">Catering</label>
                <p className="text-sm text-gray-600 mt-1">Food, beverages, service staff, menu planning</p>
              </div>
            </div>
          </div>

          {/* Photography Services */}
          <div className={`p-4 rounded-lg border ${formState.selectedServices.photography
            ? 'border-celebrate-orange bg-orange-50'
            : 'border-gray-200 hover:border-gray-300'} transition-all cursor-pointer`}
            onClick={() => setFormState(prev => ({
              ...prev,
              selectedServices: {
                ...prev.selectedServices,
                photography: !prev.selectedServices.photography
              }
            }))}
          >
            <div className="flex items-start">
              <input
                type="checkbox"
                id="photography"
                name="photography"
                checked={formState.selectedServices.photography}
                onChange={handleChange}
                className="h-5 w-5 mt-0.5 text-celebrate-orange focus:ring-celebrate-orange border-gray-300 rounded"
              />
              <div className="ml-3">
                <label htmlFor="photography" className="font-medium text-gray-800 cursor-pointer">Photography</label>
                <p className="text-sm text-gray-600 mt-1">Professional event photography, photo editing</p>
              </div>
            </div>
          </div>

          {/* Videography Services */}
          <div className={`p-4 rounded-lg border ${formState.selectedServices.videography
            ? 'border-celebrate-orange bg-orange-50'
            : 'border-gray-200 hover:border-gray-300'} transition-all cursor-pointer`}
            onClick={() => setFormState(prev => ({
              ...prev,
              selectedServices: {
                ...prev.selectedServices,
                videography: !prev.selectedServices.videography
              }
            }))}
          >
            <div className="flex items-start">
              <input
                type="checkbox"
                id="videography"
                name="videography"
                checked={formState.selectedServices.videography}
                onChange={handleChange}
                className="h-5 w-5 mt-0.5 text-celebrate-orange focus:ring-celebrate-orange border-gray-300 rounded"
              />
              <div className="ml-3">
                <label htmlFor="videography" className="font-medium text-gray-800 cursor-pointer">Videography</label>
                <p className="text-sm text-gray-600 mt-1">Event filming, highlight reels, full video coverage</p>
              </div>
            </div>
          </div>

          {/* Music/DJ Services */}
          <div className={`p-4 rounded-lg border ${formState.selectedServices.music
            ? 'border-celebrate-orange bg-orange-50'
            : 'border-gray-200 hover:border-gray-300'} transition-all cursor-pointer`}
            onClick={() => setFormState(prev => ({
              ...prev,
              selectedServices: {
                ...prev.selectedServices,
                music: !prev.selectedServices.music
              }
            }))}
          >
            <div className="flex items-start">
              <input
                type="checkbox"
                id="music"
                name="music"
                checked={formState.selectedServices.music}
                onChange={handleChange}
                className="h-5 w-5 mt-0.5 text-celebrate-orange focus:ring-celebrate-orange border-gray-300 rounded"
              />
              <div className="ml-3">
                <label htmlFor="music" className="font-medium text-gray-800 cursor-pointer">Music/DJ</label>
                <p className="text-sm text-gray-600 mt-1">DJ services, live music, sound equipment</p>
              </div>
            </div>
          </div>

          {/* Transportation Services */}
          <div className={`p-4 rounded-lg border ${formState.selectedServices.transportation
            ? 'border-celebrate-orange bg-orange-50'
            : 'border-gray-200 hover:border-gray-300'} transition-all cursor-pointer`}
            onClick={() => setFormState(prev => ({
              ...prev,
              selectedServices: {
                ...prev.selectedServices,
                transportation: !prev.selectedServices.transportation
              }
            }))}
          >
            <div className="flex items-start">
              <input
                type="checkbox"
                id="transportation"
                name="transportation"
                checked={formState.selectedServices.transportation}
                onChange={handleChange}
                className="h-5 w-5 mt-0.5 text-celebrate-orange focus:ring-celebrate-orange border-gray-300 rounded"
              />
              <div className="ml-3">
                <label htmlFor="transportation" className="font-medium text-gray-800 cursor-pointer">Transportation</label>
                <p className="text-sm text-gray-600 mt-1">Guest shuttles, luxury vehicles, coordination</p>
              </div>
            </div>
          </div>

          {/* Invitation Services */}
          <div className={`p-4 rounded-lg border ${formState.selectedServices.invitation
            ? 'border-celebrate-orange bg-orange-50'
            : 'border-gray-200 hover:border-gray-300'} transition-all cursor-pointer`}
            onClick={() => setFormState(prev => ({
              ...prev,
              selectedServices: {
                ...prev.selectedServices,
                invitation: !prev.selectedServices.invitation
              }
            }))}
          >
            <div className="flex items-start">
              <input
                type="checkbox"
                id="invitation"
                name="invitation"
                checked={formState.selectedServices.invitation}
                onChange={handleChange}
                className="h-5 w-5 mt-0.5 text-celebrate-orange focus:ring-celebrate-orange border-gray-300 rounded"
              />
              <div className="ml-3">
                <label htmlFor="invitation" className="font-medium text-gray-800 cursor-pointer">Invitations</label>
                <p className="text-sm text-gray-600 mt-1">Digital/printed invitations, RSVP management</p>
              </div>
            </div>
          </div>

          {/* Priest Services */}
          <div className={`p-4 rounded-lg border ${formState.selectedServices.priestServices
            ? 'border-celebrate-orange bg-orange-50'
            : 'border-gray-200 hover:border-gray-300'} transition-all cursor-pointer`}
            onClick={() => setFormState(prev => ({
              ...prev,
              selectedServices: {
                ...prev.selectedServices,
                priestServices: !prev.selectedServices.priestServices
              }
            }))}
          >
            <div className="flex items-start">
              <input
                type="checkbox"
                id="priestServices"
                name="priestServices"
                checked={formState.selectedServices.priestServices}
                onChange={handleChange}
                className="h-5 w-5 mt-0.5 text-celebrate-orange focus:ring-celebrate-orange border-gray-300 rounded"
              />
              <div className="ml-3">
                <label htmlFor="priestServices" className="font-medium text-gray-800 cursor-pointer">Priest Services</label>
                <p className="text-sm text-gray-600 mt-1">Religious ceremonies, rituals, consultation</p>
              </div>
            </div>
          </div>

          {/* Gift/Favors Services */}
          <div className={`p-4 rounded-lg border ${formState.selectedServices.giftFavors
            ? 'border-celebrate-orange bg-orange-50'
            : 'border-gray-200 hover:border-gray-300'} transition-all cursor-pointer`}
            onClick={() => setFormState(prev => ({
              ...prev,
              selectedServices: {
                ...prev.selectedServices,
                giftFavors: !prev.selectedServices.giftFavors
              }
            }))}
          >
            <div className="flex items-start">
              <input
                type="checkbox"
                id="giftFavors"
                name="giftFavors"
                checked={formState.selectedServices.giftFavors}
                onChange={handleChange}
                className="h-5 w-5 mt-0.5 text-celebrate-orange focus:ring-celebrate-orange border-gray-300 rounded"
              />
              <div className="ml-3">
                <label htmlFor="giftFavors" className="font-medium text-gray-800 cursor-pointer">Gifts & Favors</label>
                <p className="text-sm text-gray-600 mt-1">Return gifts, guest favors, packaging</p>
              </div>
            </div>
          </div>

          {/* Lighting Services */}
          <div className={`p-4 rounded-lg border ${formState.selectedServices.lighting
            ? 'border-celebrate-orange bg-orange-50'
            : 'border-gray-200 hover:border-gray-300'} transition-all cursor-pointer`}
            onClick={() => setFormState(prev => ({
              ...prev,
              selectedServices: {
                ...prev.selectedServices,
                lighting: !prev.selectedServices.lighting
              }
            }))}
          >
            <div className="flex items-start">
              <input
                type="checkbox"
                id="lighting"
                name="lighting"
                checked={formState.selectedServices.lighting}
                onChange={handleChange}
                className="h-5 w-5 mt-0.5 text-celebrate-orange focus:ring-celebrate-orange border-gray-300 rounded"
              />
              <div className="ml-3">
                <label htmlFor="lighting" className="font-medium text-gray-800 cursor-pointer">Lighting</label>
                <p className="text-sm text-gray-600 mt-1">Ambient lighting, stage lighting, special effects</p>
              </div>
            </div>
          </div>

          {/* Flower Arrangements */}
          <div className={`p-4 rounded-lg border ${formState.selectedServices.flowerArrangements
            ? 'border-celebrate-orange bg-orange-50'
            : 'border-gray-200 hover:border-gray-300'} transition-all cursor-pointer`}
            onClick={() => setFormState(prev => ({
              ...prev,
              selectedServices: {
                ...prev.selectedServices,
                flowerArrangements: !prev.selectedServices.flowerArrangements
              }
            }))}
          >
            <div className="flex items-start">
              <input
                type="checkbox"
                id="flowerArrangements"
                name="flowerArrangements"
                checked={formState.selectedServices.flowerArrangements}
                onChange={handleChange}
                className="h-5 w-5 mt-0.5 text-celebrate-orange focus:ring-celebrate-orange border-gray-300 rounded"
              />
              <div className="ml-3">
                <label htmlFor="flowerArrangements" className="font-medium text-gray-800 cursor-pointer">Flower Arrangements</label>
                <p className="text-sm text-gray-600 mt-1">Bouquets, centerpieces, venue decoration</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Information */}
      <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
        <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-4 flex items-center">
          <FileText className="mr-2 text-celebrate-orange" size={22} />
          Additional Information
        </h3>
        <div>
          <textarea
            id="additionalInfo"
            name="additionalInfo"
            value={formState.additionalInfo}
            onChange={handleChange}
            rows={4}
            className="w-full px-3 sm:px-4 py-2 sm:py-3 text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-celebrate-orange focus:border-celebrate-orange transition-all"
            placeholder="Tell us more about your event, special requirements, or any questions you have..."
          ></textarea>
          <p className="text-sm text-gray-500 mt-2">
            Please include any specific details that will help us understand your event better.
          </p>
        </div>
      </div>

      {formState.error && (
        <div className="bg-red-50 p-4 rounded-md border border-red-200 text-red-600">
          <p className="font-medium">Error</p>
          <p className="text-sm">{formState.error}</p>
        </div>
      )}

      <div className="flex justify-center sm:justify-end mt-6">
        <Button
          type="submit"
          disabled={formState.submitting}
          className="w-full sm:w-auto bg-gradient-to-r from-celebrate-orange to-celebrate-red text-white px-8 py-3 text-base sm:text-lg font-medium rounded-md shadow-md hover:shadow-lg transition-all duration-300"
        >
          {formState.submitting ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Submitting...
            </span>
          ) : (
            'Submit Booking Request'
          )}
        </Button>
      </div>
    </form>
  );
};

export default EventBookingForm;
