import * as React from 'react';
import Svg, { Path, G } from 'react-native-svg';

interface Props {
  width?: number;
  height?: number;
  color?: string;
}

const GoogleLogo: React.FC<Props> = ({ width = 24, height = 24, color = '#000000' }) => (
  <Svg width={width} height={height} viewBox="0 0 24 24">
    <G stroke="none" strokeWidth={1} fill="none" fillRule="evenodd">
      <Path
        d="M20.64,12.2045 C20.64,11.5664 20.5827,10.9527 20.4764,10.3636 L12,10.3636 L12,13.845 L16.8436,13.845 C16.635,14.97 16.0009,15.9232 15.0477,16.5614 L15.0477,18.8195 L17.9564,18.8195 C19.6582,17.2527 20.64,14.9455 20.64,12.2045 L20.64,12.2045 Z"
        fill="#4285F4"
      />
      <Path
        d="M12,21 C14.43,21 16.4673,20.1941 17.9564,18.8195 L15.0477,16.5614 C14.2418,17.1014 13.2109,17.4205 12,17.4205 C9.65591,17.4205 7.67182,15.8373 6.96409,13.71 L3.95727,13.71 L3.95727,16.0418 C5.43818,18.9832 8.48182,21 12,21 L12,21 Z"
        fill="#34A853"
      />
      <Path
        d="M6.96409,13.71 C6.78409,13.17 6.68182,12.5932 6.68182,12 C6.68182,11.4068 6.78409,10.83 6.96409,10.29 L6.96409,7.95818 L3.95727,7.95818 C3.34773,9.17318 3,10.5477 3,12 C3,13.4523 3.34773,14.8268 3.95727,16.0418 L6.96409,13.71 L6.96409,13.71 Z"
        fill="#FBBC05"
      />
      <Path
        d="M12,6.57955 C13.3214,6.57955 14.5077,7.03364 15.4405,7.92545 L18.0218,5.34409 C16.4632,3.89182 14.4268,3 12,3 C8.48182,3 5.43818,5.01682 3.95727,7.95818 L6.96409,10.29 C7.67182,8.16273 9.65591,6.57955 12,6.57955 L12,6.57955 Z"
        fill="#EA4335"
      />
    </G>
  </Svg>
);

export default GoogleLogo; 