import React, { useState, useEffect } from 'react';
import { Check, Setting<PERSON>, Sparkles } from 'lucide-react';

const CateringForm = ({ onSubmit, previousData }) => {
  const [formData, setFormData] = useState({
    cuisineType: '',
    mealType: '',
    dietaryRestrictions: [],
    beverages: [],
    numberOfCourses: '',
    specialRequests: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [showSuccess, setShowSuccess] = useState(false);

  // Initialize form data with previous data if available
  useEffect(() => {
    if (previousData) {
      setFormData(prevData => ({
        ...prevData,
        ...previousData
      }));
    }
  }, [previousData]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (type === 'checkbox') {
      if (checked) {
        setFormData({
          ...formData,
          [name]: [...(formData[name] || []), value]
        });
      } else {
        setFormData({
          ...formData,
          [name]: (formData[name] || []).filter(item => item !== value)
        });
      }
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // Validate form and submit
  const validateAndSubmit = () => {
    const errors = {};

    // Check required fields
    if (!formData.cuisineType) errors.cuisineType = 'Please select a cuisine type';
    if (!formData.mealType) errors.mealType = 'Please select a meal type';

    if (Object.keys(errors).length === 0) {
      // Form is valid, submit
      onSubmit({
        serviceDetails: formData,
        category: 'Catering',
        name: 'Catering Service'
      });
      
      // Show success message
      setShowSuccess(true);
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    } else {
      // Form has errors
      setFormErrors(errors);
    }
  };

  return (
    <div className="bg-white dark:bg-dark-secondary p-6 rounded-lg shadow-md">
      {showSuccess && (
        <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg text-green-700 dark:text-green-300 flex items-center">
          <Check className="mr-2" size={18} />
          Catering details saved successfully!
        </div>
      )}
      
      <p className="mb-6 text-light-secondary dark:text-gray-400 flex items-center">
        <Settings className="mr-2 text-[#ff5a5f]" size={16} />
        Please provide details for your catering requirements
      </p>

      <div className="space-y-6">
        {/* Cuisine Type */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Cuisine Type <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="relative">
            <select
              name="cuisineType"
              value={formData.cuisineType || ''}
              onChange={handleInputChange}
              className={`w-full p-3 rounded-lg border ${formErrors.cuisineType ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none`}
            >
              <option value="">Select Cuisine Type</option>
              <option value="North Indian">North Indian</option>
              <option value="South Indian">South Indian</option>
              <option value="Pan Asian">Pan Asian</option>
              <option value="Continental">Continental</option>
              <option value="Multi-Cuisine">Multi-Cuisine</option>
              <option value="Regional Specialties">Regional Specialties</option>
              <option value="Fusion">Fusion</option>
              <option value="Custom">Custom</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
            {formErrors.cuisineType && (
              <p className="mt-1 text-sm text-red-500">{formErrors.cuisineType}</p>
            )}
          </div>
        </div>

        {/* Meal Type */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Meal Type <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="relative">
            <select
              name="mealType"
              value={formData.mealType || ''}
              onChange={handleInputChange}
              className={`w-full p-3 rounded-lg border ${formErrors.mealType ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none`}
            >
              <option value="">Select Meal Type</option>
              <option value="Breakfast">Breakfast</option>
              <option value="Lunch">Lunch</option>
              <option value="Dinner">Dinner</option>
              <option value="High Tea">High Tea</option>
              <option value="Cocktail & Snacks">Cocktail & Snacks</option>
              <option value="Buffet">Buffet</option>
              <option value="Plated Service">Plated Service</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
            {formErrors.mealType && (
              <p className="mt-1 text-sm text-red-500">{formErrors.mealType}</p>
            )}
          </div>
        </div>

        {/* Number of Courses */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Number of Courses
          </label>
          <div className="relative">
            <select
              name="numberOfCourses"
              value={formData.numberOfCourses || ''}
              onChange={handleInputChange}
              className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none"
            >
              <option value="">Select Number of Courses</option>
              <option value="2 Course">2 Course (Starters + Main)</option>
              <option value="3 Course">3 Course (Starters + Main + Dessert)</option>
              <option value="4 Course">4 Course (Soup + Starters + Main + Dessert)</option>
              <option value="5 Course">5 Course (Soup + Appetizers + Salad + Main + Dessert)</option>
              <option value="Multi-course">Multi-course Elaborate Menu</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
          </div>
        </div>

        {/* Dietary Restrictions */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Dietary Restrictions
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {["Vegetarian", "Vegan", "Jain", "Gluten-Free", "Nut-Free", "Dairy-Free", "No Onion No Garlic"].map((option, i) => (
              <div key={i} className="flex items-start">
                <input
                  type="checkbox"
                  id={`dietaryRestrictions-${i}`}
                  name="dietaryRestrictions"
                  value={option}
                  checked={(formData.dietaryRestrictions || []).includes(option)}
                  onChange={handleInputChange}
                  className="h-4 w-4 mt-1 text-[#ff5a5f] focus:ring-[#ff5a5f] border-gray-300 rounded"
                />
                <label htmlFor={`dietaryRestrictions-${i}`} className="ml-2 block text-sm text-light-primary dark:text-white">
                  {option}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Beverages */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Beverages
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {["Soft Drinks", "Juices", "Tea & Coffee", "Mocktails", "Alcoholic Beverages", "Specialty Drinks"].map((option, i) => (
              <div key={i} className="flex items-start">
                <input
                  type="checkbox"
                  id={`beverages-${i}`}
                  name="beverages"
                  value={option}
                  checked={(formData.beverages || []).includes(option)}
                  onChange={handleInputChange}
                  className="h-4 w-4 mt-1 text-[#ff5a5f] focus:ring-[#ff5a5f] border-gray-300 rounded"
                />
                <label htmlFor={`beverages-${i}`} className="ml-2 block text-sm text-light-primary dark:text-white">
                  {option}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Special Requests */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Special Requests
          </label>
          <textarea
            name="specialRequests"
            value={formData.specialRequests || ''}
            onChange={handleInputChange}
            placeholder="Any specific menu items or catering requirements..."
            rows="3"
            className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                     bg-white dark:bg-dark-primary text-light-primary dark:text-white
                     focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent"
          />
        </div>
      </div>

      <div className="mt-8 flex justify-end">
        <button
          type="button"
          onClick={validateAndSubmit}
          className="px-6 py-3 rounded-lg bg-gradient-to-r from-[#ff5a5f] to-[#ff8c8f] text-white font-medium hover:shadow-md transition-all duration-300 flex items-center"
        >
          {previousData ? (
            <>
              <Check className="mr-2" size={18} />
              Update Catering Details
            </>
          ) : (
            <>
              <Sparkles className="mr-2" size={18} />
              Save Catering Details
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default CateringForm;
