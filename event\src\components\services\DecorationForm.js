import React, { useState, useEffect } from 'react';
import { Check, Setting<PERSON>, Sparkles } from 'lucide-react';

const DecorationForm = ({ onSubmit, previousData }) => {
  const [formData, setFormData] = useState({
    theme: '',
    colorScheme: '',
    decorationAreas: [],
    flowerTypes: [],
    decorElements: [],
    specialRequests: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [showSuccess, setShowSuccess] = useState(false);

  // Initialize form data with previous data if available
  useEffect(() => {
    if (previousData) {
      setFormData(prevData => ({
        ...prevData,
        ...previousData
      }));
    }
  }, [previousData]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (type === 'checkbox') {
      if (checked) {
        setFormData({
          ...formData,
          [name]: [...(formData[name] || []), value]
        });
      } else {
        setFormData({
          ...formData,
          [name]: (formData[name] || []).filter(item => item !== value)
        });
      }
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // Validate form and submit
  const validateAndSubmit = () => {
    const errors = {};

    // Check required fields
    if (!formData.theme) errors.theme = 'Please select a decoration theme';
    if (!formData.colorScheme) errors.colorScheme = 'Please select a color scheme';
    if (!formData.decorationAreas || formData.decorationAreas.length === 0) {
      errors.decorationAreas = 'Please select at least one area to decorate';
    }

    if (Object.keys(errors).length === 0) {
      // Form is valid, submit
      onSubmit({
        serviceDetails: formData,
        category: 'Decoration',
        name: 'Decoration Service'
      });
      
      // Show success message
      setShowSuccess(true);
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    } else {
      // Form has errors
      setFormErrors(errors);
    }
  };

  return (
    <div className="bg-white dark:bg-dark-secondary p-6 rounded-lg shadow-md">
      {showSuccess && (
        <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg text-green-700 dark:text-green-300 flex items-center">
          <Check className="mr-2" size={18} />
          Decoration details saved successfully!
        </div>
      )}
      
      <p className="mb-6 text-light-secondary dark:text-gray-400 flex items-center">
        <Settings className="mr-2 text-[#ff5a5f]" size={16} />
        Please provide details for your decoration requirements
      </p>

      <div className="space-y-6">
        {/* Theme Selection */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Decoration Theme <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="relative">
            <select
              name="theme"
              value={formData.theme || ''}
              onChange={handleInputChange}
              className={`w-full p-3 rounded-lg border ${formErrors.theme ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none`}
            >
              <option value="">Select Decoration Theme</option>
              <option value="Traditional">Traditional</option>
              <option value="Modern">Modern</option>
              <option value="Fusion">Fusion</option>
              <option value="Minimalist">Minimalist</option>
              <option value="Elegant">Elegant</option>
              <option value="Rustic">Rustic</option>
              <option value="Bohemian">Bohemian</option>
              <option value="Royal">Royal</option>
              <option value="Floral">Floral</option>
              <option value="Custom">Custom</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
            {formErrors.theme && (
              <p className="mt-1 text-sm text-red-500">{formErrors.theme}</p>
            )}
          </div>
        </div>

        {/* Color Scheme */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Color Scheme <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="relative">
            <select
              name="colorScheme"
              value={formData.colorScheme || ''}
              onChange={handleInputChange}
              className={`w-full p-3 rounded-lg border ${formErrors.colorScheme ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
                       bg-white dark:bg-dark-primary text-light-primary dark:text-white
                       focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent appearance-none`}
            >
              <option value="">Select Color Scheme</option>
              <option value="Red & Gold">Red & Gold</option>
              <option value="Pastel Colors">Pastel Colors</option>
              <option value="White & Green">White & Green</option>
              <option value="Blue & Silver">Blue & Silver</option>
              <option value="Purple & Gold">Purple & Gold</option>
              <option value="Pink & White">Pink & White</option>
              <option value="Yellow & Orange">Yellow & Orange</option>
              <option value="Custom">Custom</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
            {formErrors.colorScheme && (
              <p className="mt-1 text-sm text-red-500">{formErrors.colorScheme}</p>
            )}
          </div>
        </div>

        {/* Areas to Decorate */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Areas to Decorate <span className="text-[#ff5a5f]">*</span>
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {["Entrance", "Stage", "Dining Area", "Seating Area", "Photo Booth", "Ceiling", "Walls", "Outdoor Space"].map((option, i) => (
              <div key={i} className="flex items-start">
                <input
                  type="checkbox"
                  id={`decorationAreas-${i}`}
                  name="decorationAreas"
                  value={option}
                  checked={(formData.decorationAreas || []).includes(option)}
                  onChange={handleInputChange}
                  className="h-4 w-4 mt-1 text-[#ff5a5f] focus:ring-[#ff5a5f] border-gray-300 rounded"
                />
                <label htmlFor={`decorationAreas-${i}`} className="ml-2 block text-sm text-light-primary dark:text-white">
                  {option}
                </label>
              </div>
            ))}
          </div>
          {formErrors.decorationAreas && (
            <p className="mt-1 text-sm text-red-500">{formErrors.decorationAreas}</p>
          )}
        </div>

        {/* Flower Types */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Flower Types
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {["Roses", "Lilies", "Orchids", "Carnations", "Marigold", "Lotus", "Artificial Flowers", "No Flowers"].map((option, i) => (
              <div key={i} className="flex items-start">
                <input
                  type="checkbox"
                  id={`flowerTypes-${i}`}
                  name="flowerTypes"
                  value={option}
                  checked={(formData.flowerTypes || []).includes(option)}
                  onChange={handleInputChange}
                  className="h-4 w-4 mt-1 text-[#ff5a5f] focus:ring-[#ff5a5f] border-gray-300 rounded"
                />
                <label htmlFor={`flowerTypes-${i}`} className="ml-2 block text-sm text-light-primary dark:text-white">
                  {option}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Decoration Elements */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Decoration Elements
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {["Balloons", "Fabric Drapes", "Flower Arrangements", "Candles", "Lanterns", "Fairy Lights", "Props", "Backdrops"].map((option, i) => (
              <div key={i} className="flex items-start">
                <input
                  type="checkbox"
                  id={`decorElements-${i}`}
                  name="decorElements"
                  value={option}
                  checked={(formData.decorElements || []).includes(option)}
                  onChange={handleInputChange}
                  className="h-4 w-4 mt-1 text-[#ff5a5f] focus:ring-[#ff5a5f] border-gray-300 rounded"
                />
                <label htmlFor={`decorElements-${i}`} className="ml-2 block text-sm text-light-primary dark:text-white">
                  {option}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Special Requests */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-light-primary dark:text-white">
            Special Requests
          </label>
          <textarea
            name="specialRequests"
            value={formData.specialRequests || ''}
            onChange={handleInputChange}
            placeholder="Any specific decoration elements or requirements..."
            rows="3"
            className="w-full p-3 rounded-lg border border-gray-300 dark:border-gray-600
                     bg-white dark:bg-dark-primary text-light-primary dark:text-white
                     focus:ring-2 focus:ring-[#ff5a5f] focus:border-transparent"
          />
        </div>
      </div>

      <div className="mt-8 flex justify-end">
        <button
          type="button"
          onClick={validateAndSubmit}
          className="px-6 py-3 rounded-lg bg-gradient-to-r from-[#ff5a5f] to-[#ff8c8f] text-white font-medium hover:shadow-md transition-all duration-300 flex items-center"
        >
          {previousData ? (
            <>
              <Check className="mr-2" size={18} />
              Update Decoration Details
            </>
          ) : (
            <>
              <Sparkles className="mr-2" size={18} />
              Save Decoration Details
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default DecorationForm;
