import * as React from 'react';
import Svg, { Path, G } from 'react-native-svg';

interface Props {
  width?: number;
  height?: number;
  color?: string;
}

const AppleLogo: React.FC<Props> = ({ width = 24, height = 24, color = '#000000' }) => (
  <Svg width={width} height={height} viewBox="0 0 24 24">
    <G stroke="none" strokeWidth={1} fill="none" fillRule="evenodd">
      <Path
        d="M17.5,12.5 C17.5,10.5 18.5,9 19.5,8 C18.5,7 17,6 15.5,6 C14,6 13,7 12,7 C11,7 10,6 8.5,6 C7,6 5.5,7 4.5,8 C5.5,9 6.5,10.5 6.5,12.5 C6.5,14.5 5.5,16 4.5,17 C5.5,18 7,19 8.5,19 C10,19 11,18 12,18 C13,18 14,19 15.5,19 C17,19 18.5,18 19.5,17 C18.5,16 17.5,14.5 17.5,12.5 Z"
        fill={color}
      />
    </G>
  </Svg>
);

export default AppleLogo; 