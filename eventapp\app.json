{"expo": {"name": "EaseMyEvent", "slug": "easemyevent", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "easemyevent", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "easemyevent", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-font", "expo-web-browser"], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "4355b0c9-1554-407b-bdc3-567a40d50003"}}, "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/4355b0c9-1554-407b-bdc3-567a40d50003"}, "android": {"package": "com.light222.easemyevent"}}}