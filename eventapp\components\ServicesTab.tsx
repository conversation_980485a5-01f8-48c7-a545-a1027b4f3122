import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
  Platform,
} from 'react-native';
import serviceData from '../data/ServiceData.json';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const CARD_WIDTH = SCREEN_WIDTH * 0.85;

interface Service {
  id?: string;
  title: string;
  description: string;
  longDescription?: string;
  icon?: string;
  features: string[];
  packages: {
    name: string;
    price: string;
    includes: string[];
  }[];
  image?: string;
  faq?: {
    question: string;
    answer: string;
  }[];
}

const ServicesTab: React.FC = () => {
  // Get services from both the services array and individual service objects
  const arrayServices = serviceData.services as Service[];
  const individualServices = Object.entries(serviceData)
    .filter(([key]) => key !== 'services')
    .map(([key, value]) => ({
      id: key,
      ...value
    })) as Service[];

  // Combine both types of services
  const allServices = [...arrayServices, ...individualServices];

  const renderServiceCard = (service: Service) => (
    <TouchableOpacity
      key={service.id || service.title}
      style={styles.serviceCard}
      onPress={() => {
        // Handle service selection
        console.log('Selected service:', service.title);
      }}
    >
      <View style={styles.cardHeader}>
        {service.icon && <Text style={styles.serviceIcon}>{service.icon}</Text>}
        <Text style={styles.serviceTitle}>{service.title}</Text>
      </View>

      <Text style={styles.serviceDescription}>
        {service.longDescription || service.description}
      </Text>

      <View style={styles.featuresContainer}>
        {service.features.map((feature, index) => (
          <View key={index} style={styles.featureItem}>
            <Text style={styles.featureText}>• {feature}</Text>
          </View>
        ))}
      </View>

      <View style={styles.packagesContainer}>
        {service.packages.map((pkg, index) => (
          <View key={index} style={styles.packageItem}>
            <Text style={styles.packageName}>{pkg.name}</Text>
            <Text style={styles.packagePrice}>{pkg.price}</Text>
            <View style={styles.includesContainer}>
              {pkg.includes.map((item, idx) => (
                <Text key={idx} style={styles.includesText}>✓ {item}</Text>
              ))}
            </View>
          </View>
        ))}
      </View>

      {service.faq && service.faq.length > 0 && (
        <View style={styles.faqContainer}>
          <Text style={styles.faqTitle}>Frequently Asked Questions</Text>
          {service.faq.map((item, index) => (
            <View key={index} style={styles.faqItem}>
              <Text style={styles.faqQuestion}>Q: {item.question}</Text>
              <Text style={styles.faqAnswer}>A: {item.answer}</Text>
            </View>
          ))}
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <ScrollView
      style={styles.container}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.contentContainer}
    >
      <Text style={styles.header}>Our Services</Text>
      <Text style={styles.subHeader}>Discover our comprehensive range of event services</Text>
      
      {allServices.map(renderServiceCard)}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  contentContainer: {
    padding: 16,
  },
  header: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  subHeader: {
    fontSize: 16,
    color: '#666',
    marginBottom: 24,
    textAlign: 'center',
  },
  serviceCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    width: CARD_WIDTH,
    alignSelf: 'center',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  serviceIcon: {
    fontSize: 32,
    marginRight: 12,
  },
  serviceTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  serviceDescription: {
    fontSize: 16,
    color: '#666',
    marginBottom: 16,
    lineHeight: 22,
  },
  featuresContainer: {
    marginBottom: 20,
  },
  featureItem: {
    marginBottom: 8,
  },
  featureText: {
    fontSize: 14,
    color: '#444',
    lineHeight: 20,
  },
  packagesContainer: {
    marginBottom: 20,
  },
  packageItem: {
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  packageName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  packagePrice: {
    fontSize: 16,
    color: '#ff5a5f',
    fontWeight: '500',
    marginBottom: 8,
  },
  includesContainer: {
    marginTop: 8,
  },
  includesText: {
    fontSize: 14,
    color: '#555',
    marginBottom: 4,
  },
  faqContainer: {
    marginTop: 8,
  },
  faqTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  faqItem: {
    marginBottom: 16,
  },
  faqQuestion: {
    fontSize: 15,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  faqAnswer: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
});

export default ServicesTab; 