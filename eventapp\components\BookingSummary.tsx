import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Platform, Alert } from 'react-native';
import { router } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface BasicDetails {
  eventDate: string;
  eventTime: string;
  eventLocation: string;
  numberOfPersons: string;
  services: {
    Decoration: boolean;
    Catering: boolean;
    Priest: boolean;
    Photography: boolean;
    Videography: boolean;
    Music: boolean;
    Transportation: boolean;
    Invitation: boolean;
    Gifts: boolean;
    Lighting: boolean;
  };
  additionalNotes: string;
}

interface BookingSummaryProps {
  selections: Record<string, any>;
  basicDetails: BasicDetails;
  eventType: string;
  onEdit: () => void;
  onConfirm: () => Promise<void>;
}

const BookingSummary: React.FC<BookingSummaryProps> = ({
  selections,
  basicDetails,
  eventType,
  onEdit,
  onConfirm,
}) => {
  const calculateTotal = () => {
    return Object.values(selections).reduce((total, selection: any) => {
      return total + (selection.price || 0);
    }, 0);
  };

  const handleConfirmBooking = async () => {
    try {
      // Save booking to storage
      const newBooking = {
        id: Date.now(),
        eventType: eventType.replace(/-/g, ' '),
        selections,
        totalAmount: calculateTotal(),
        status: 'Confirmed',
        date: new Date().toISOString(),
      };

      // Handle storage based on platform
      if (Platform.OS === 'web') {
        // Web storage
        const existingBookingsStr = localStorage.getItem('bookings');
        const existingBookings = existingBookingsStr ? JSON.parse(existingBookingsStr) : [];
        localStorage.setItem('bookings', JSON.stringify([...existingBookings, newBooking]));
      } else {
        // Mobile storage
        const existingBookingsStr = await AsyncStorage.getItem('bookings');
        const existingBookings = existingBookingsStr ? JSON.parse(existingBookingsStr) : [];
        await AsyncStorage.setItem('bookings', JSON.stringify([...existingBookings, newBooking]));
      }

      // Navigate based on platform
      if (Platform.OS === 'web') {
        router.push('/bookings');
      } else {
        router.push('/(tabs)');
      }
    } catch (error) {
      console.error('Error saving booking:', error);
      // Show error message to user
      Alert.alert('Error', 'Failed to save booking. Please try again.');
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Event Details</Text>
          <View style={styles.detailRow}>
            <Text style={styles.label}>Event Type:</Text>
            <Text style={styles.value}>{eventType.replace(/-/g, ' ')}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.label}>Date:</Text>
            <Text style={styles.value}>{basicDetails.eventDate}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.label}>Time:</Text>
            <Text style={styles.value}>{basicDetails.eventTime}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.label}>Location:</Text>
            <Text style={styles.value}>{basicDetails.eventLocation}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.label}>Number of Persons:</Text>
            <Text style={styles.value}>{basicDetails.numberOfPersons}</Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Selected Services</Text>
          {Object.entries(selections).map(([category, selection]: [string, any]) => (
            <View key={category} style={styles.serviceCard}>
              <Text style={styles.serviceTitle}>{category}</Text>
              {selection.title && (
                <Text style={styles.serviceDetail}>{selection.title}</Text>
              )}
              {selection.description && (
                <Text style={styles.serviceDescription}>{selection.description}</Text>
              )}
              {selection.price && (
                <Text style={styles.servicePrice}>₹{selection.price}</Text>
              )}
            </View>
          ))}
        </View>

        {basicDetails.additionalNotes && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Additional Notes</Text>
            <Text style={styles.notes}>{basicDetails.additionalNotes}</Text>
          </View>
        )}

        <View style={styles.totalSection}>
          <Text style={styles.totalLabel}>Total Amount:</Text>
          <Text style={styles.totalAmount}>₹{calculateTotal()}</Text>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.button, styles.secondaryButton]}
          onPress={onEdit}
        >
          <Text style={styles.secondaryButtonText}>Edit Booking</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.button, styles.primaryButton]}
          onPress={handleConfirmBooking}
        >
          <Text style={styles.primaryButtonText}>Confirm Booking</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    color: '#666',
  },
  value: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  serviceCard: {
    marginBottom: 16,
    padding: 16,
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
  },
  serviceTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  serviceDetail: {
    fontSize: 16,
    color: '#666',
    marginBottom: 4,
  },
  serviceDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  servicePrice: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ff5a5f',
  },
  notes: {
    fontSize: 16,
    color: '#666',
    fontStyle: 'italic',
  },
  totalSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  totalAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ff5a5f',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  button: {
    flex: 1,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 8,
  },
  primaryButton: {
    backgroundColor: '#ff5a5f',
  },
  secondaryButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ff5a5f',
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonText: {
    color: '#ff5a5f',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default BookingSummary; 