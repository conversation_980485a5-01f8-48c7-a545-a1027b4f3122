{"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "7.26.6", "description": "Remove nullish coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.26.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/helper-plugin-test-runner": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}