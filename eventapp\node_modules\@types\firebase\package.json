{"name": "@types/firebase", "version": "2.4.32", "description": "TypeScript definitions for Firebase API", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/vbortone"}, {"name": "Shin1 <PERSON><PERSON>mura", "url": "https://github.com/in-async/"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/dsebastien"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ciekawy"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "5df124983e0d73736b97992a93171d70d09dd67ac797a1e42a84a9f465a90aad", "typeScriptVersion": "2.0"}