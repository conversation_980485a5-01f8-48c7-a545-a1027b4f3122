export interface ServicePackage {
  name: string;
  price: string;
  includes: string[];
}

export interface FAQ {
  question: string;
  answer: string;
}

export interface Service {
  id: string;
  title: string;
  description: string;
  longDescription: string;
  icon: string;
  features: string[];
  packages: ServicePackage[];
  image: string;
  faq: FAQ[];
}

export interface ServiceData {
  services: Service[];
} 