import React from "react";
import { PartyPopper, Home, Users } from "lucide-react";

const services = [
  {
    icon: <PERSON>Popper,
    title: "Birthday Celebrations",
    description: "Make your birthday special with customized themes and entertainment."
  },
  {
    icon: Home,
    title: "House Warming",
    description: "Traditional or modern house warming ceremonies with complete setup."
  },
  {
    icon: Users,
    title: "Custom Events",
    description: "From anniversaries to baby showers, we plan any celebration."
  }
];

const Services = () => {
  return (
    <section id="services" className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">
            Our <span className="gradient-text-solid-end">Services</span>
          </h2>
          <p className="text-slate-600 max-w-2xl mx-auto">
            Making your celebrations memorable and stress-free
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {services.map((service, index) => (
            <div 
              key={index}
              className="group bg-gradient-to-b from-white to-celebrate-cream/10 p-8 rounded-xl border border-celebrate-cream shadow-[0_4px_20px_-8px_rgba(0,0,0,0.1)] hover:shadow-[0_20px_40px_-12px_rgba(0,0,0,0.1)] transition-all duration-300 hover:-translate-y-1"
            >
              <div className="flex items-center justify-center w-14 h-14 mb-6 rounded-full bg-celebrate-cream group-hover:bg-celebrate-orange/20 transition-colors duration-300">
                <service.icon className="h-7 w-7 text-celebrate-orange" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-slate-800 group-hover:text-celebrate-orange transition-colors duration-300">
                {service.title}
              </h3>
              <p className="text-slate-600">
                {service.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Services;
