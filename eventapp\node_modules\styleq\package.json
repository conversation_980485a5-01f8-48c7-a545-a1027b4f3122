{"version": "0.1.3", "name": "styleq", "main": "styleq.js", "module": "dist/styleq.js", "sideEffects": false, "license": "MIT", "description": "A quick JavaScript runtime for Atomic CSS compilers.", "repository": "https://github.com/necolas/styleq", "author": "<PERSON>", "files": ["dist", "*.js", "*.ts"], "scripts": {"benchmark": "node test/benchmark.node.js", "build": "babel src --out-dir dist --config-file ./configs/.babelrc", "flow": "flow --flowconfig-name ./configs/.flowconfig", "jest": "jest ./test", "lint": "npm run lint:report -- --fix", "lint:report": "eslint src/**/*.js --config ./configs/.eslintrc", "prepare": "npm run test && npm run build", "prettier": "prettier --write \"**/*.js\" --ignore-path ./configs/.prettierignore", "prettier:report": "prettier --check \"**/*.js\" --ignore-path ./configs/.prettierignore", "test": "npm run flow && npm run prettier:report && npm run lint:report && npm run jest"}, "devDependencies": {"@babel/cli": "^7.13.10", "@babel/core": "^7.15.5", "@babel/eslint-parser": "^7.15.8", "@babel/plugin-syntax-flow": "^7.12.13", "@babel/preset-env": "^7.15.6", "@babel/preset-flow": "^7.14.5", "@babel/types": "^7.15.6", "benchmark": "^2.1.4", "eslint": "^7.32.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-flowtype": "^7.0.0", "flow-bin": "^0.195.2", "jest": "^27.2.4", "prettier": "^2.4.1"}, "jest": {"snapshotFormat": {"printBasicPrototype": false}, "transform": {"\\.js$": ["babel-jest", {"configFile": "./configs/.babelrc"}]}}, "prettier": {"singleQuote": true}}