{"name": "event", "version": "0.1.0", "private": true, "dependencies": {"@firebase/auth": "^1.10.0", "@radix-ui/react-slot": "^1.1.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^11.6.0", "framer-motion": "^12.6.3", "lucide-react": "^0.484.0", "react": "^19.0.0", "react-datepicker": "^8.2.1", "react-dom": "^19.0.0", "react-router-dom": "^7.4.0", "react-scripts": "^5.0.1", "tailwind-merge": "^3.1.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^19.1.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "tailwindcss": "^3.3.3"}}