import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { Menu, X, MapPin, User } from "lucide-react";
// import { useTheme } from "../../ThemeContext";
import eventLogo from '../../assets/event-logo.svg';
import { auth } from '../../lib/firebase';
import { onAuthStateChanged } from 'firebase/auth';
import LoginButton from '../ui/LoginButton';

const Header = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [profileOpen, setProfileOpen] = useState(false);
  const [location, setLocation] = useState(null);
  const [locationDropdownOpen, setLocationDropdownOpen] = useState(false);
  const [user, setUser] = useState(null);
  // const { isDark, toggleTheme } = useTheme();

  // Predefined cities
  const cities = [
    { name: "Hyderabad", state: "HYD" },
    { name: "Banglore", state: "BLR" },
  ];

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (user) {
        // Ensure we have the latest user data
        user.reload().then(() => {
          setUser(user);
        }).catch((error) => {
          console.error('Error reloading user:', error);
          setUser(user);
        });
      } else {
        setUser(null);
      }
    });

    return () => unsubscribe();
  }, []);

  // Handle location selection
  const handleLocationSelect = (city) => {
    setLocation(city);
    setLocationDropdownOpen(false);
    localStorage.setItem('selectedLocation', JSON.stringify(city));
  };

  // Load saved location on mount
  useEffect(() => {
    const savedLocation = localStorage.getItem('selectedLocation');
    if (savedLocation) {
      setLocation(JSON.parse(savedLocation));
    }
  }, []);

  // Handle navigation click
  const handleNavClick = () => {
    setMenuOpen(false);
  };

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.location-dropdown')) {
        setLocationDropdownOpen(false);
      }
      if (!event.target.closest('.profile-dropdown')) {
        setProfileOpen(false);
      }
      if (!event.target.closest('.mobile-menu') && !event.target.closest('.menu-button')) {
        setMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const getAvatarUrl = (user) => {
    if (user?.photoURL) {
      return user.photoURL;
    }
    // Generate a consistent avatar based on user's email or display name
    const seed = user?.email || user?.displayName || 'default-user';
    return `https://api.dicebear.com/7.x/avataaars/svg?seed=${encodeURIComponent(seed)}&backgroundColor=b6e3f4`;
  };

  const navigationLinks = [
    { to: "/events", text: "Events" },
    { to: "/services", text: "Services" },
    { to: "/blogs", text: "Blogs" },
    { to: "/e-invites", text: "E-invites" },
    { to: "/planner", text: "Personal Planner" }
  ];

  return (
    <header className="bg-light-primary dark:bg-dark-primary p-4 shadow-md relative">
      <div className="max-w-7xl mx-auto flex justify-between items-center">
        {/* Left side */}
        <div className="flex items-center">
          <button
            className="md:hidden text-light-primary dark:text-white mr-4 menu-button"
            onClick={() => setMenuOpen(!menuOpen)}
          >
            {menuOpen ? <X size={28} /> : <Menu size={28} />}
          </button>

          <Link to="/" className="flex items-center mr-8" onClick={handleNavClick}>
            <img src={eventLogo} alt="Logo" className="h-8 w-8" />
            <h1 className="text-xl font-bold text-light-primary dark:text-white ml-2">EaseMyEvent</h1>
          </Link>

          <nav className={`absolute md:static top-16 left-0 w-full md:w-auto bg-light-primary dark:bg-dark-primary md:bg-transparent
            ${menuOpen ? "block" : "hidden"} md:flex items-center md:space-x-8 p-4 md:p-0 z-50`}>
            {navigationLinks.map((link) => (
              <Link
                key={link.to}
                to={link.to}
                className="text-light-primary dark:text-white font-medium hover:text-warm-orange block md:inline py-2 md:py-0 transition-colors"
                onClick={handleNavClick}
              >
                {link.text}
              </Link>
            ))}
          </nav>
        </div>

        {/* Right side */}
        <div className="flex items-center space-x-4">
          <LocationDropdown
            location={location}
            cities={cities}
            isOpen={locationDropdownOpen}
            setIsOpen={setLocationDropdownOpen}
            onSelect={handleLocationSelect}
          />

          {/* <ThemeToggle isDark={isDark} toggleTheme={toggleTheme} /> */}

          {user ? (
            <ProfileDropdown
              avatarUrl={getAvatarUrl(user)}
              isOpen={profileOpen}
              setIsOpen={setProfileOpen}
              user={user}
            />
          ) : (
            <LoginButton />
          )}
        </div>
      </div>
    </header>
  );
};

const LocationDropdown = ({ location, cities, isOpen, setIsOpen, onSelect }) => (
  <div className="relative location-dropdown">
    <button
      className="flex items-center space-x-1 text-light-primary dark:text-white hover:text-[#ff5a5f]"
      onClick={() => setIsOpen(!isOpen)}
    >
      <MapPin size={20} />
      <span className="hidden md:inline">
        {location ? `${location.name}, ${location.state}` : "Set Location"}
      </span>
    </button>

    {isOpen && (
      <div className="absolute right-0 mt-2 w-64 bg-light-primary dark:bg-dark-secondary shadow-lg rounded-lg p-2 z-50">
        <div className="max-h-60 overflow-y-auto">
          {cities.map((city, index) => (
            <button
              key={index}
              className="w-full text-left p-2 text-light-primary dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition"
              onClick={() => onSelect(city)}
            >
              <div className="flex items-center">
                <MapPin size={16} className="mr-2" />
                <span>{city.name}, {city.state}</span>
              </div>
            </button>
          ))}
        </div>
      </div>
    )}
  </div>
);

// const ThemeToggle = ({ isDark, toggleTheme }) => (
//   <button
//     onClick={toggleTheme}
//     className="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
//   >
//     {isDark ?
//       <Sun className="text-light-primary dark:text-white" size={20} /> :
//       <Moon className="text-light-primary dark:text-white" size={20} />
//     }
//   </button>
// );

const ProfileDropdown = ({ avatarUrl, isOpen, setIsOpen, user }) => (
  <div className="relative profile-dropdown">
    {avatarUrl ? (
      <img
        src={avatarUrl}
        alt="Profile"
        className="w-10 h-10 rounded-full cursor-pointer border-2 border-gray-200 dark:border-gray-700 hover:border-[#ff5a5f] transition-colors object-cover"
        onClick={() => setIsOpen(!isOpen)}
        onError={(e) => {
          e.target.onerror = null;
          e.target.src = `https://api.dicebear.com/7.x/avataaars/svg?seed=${encodeURIComponent(user?.email || 'default')}&backgroundColor=b6e3f4`;
        }}
      />
    ) : (
      <div 
        className="w-10 h-10 rounded-full cursor-pointer border-2 border-gray-200 dark:border-gray-700 hover:border-[#ff5a5f] transition-colors flex items-center justify-center bg-gray-100 dark:bg-gray-700"
        onClick={() => setIsOpen(!isOpen)}
      >
        <User size={20} className="text-gray-500 dark:text-gray-400" />
      </div>
    )}
    {isOpen && (
      <div className="absolute right-0 mt-2 w-48 bg-light-primary dark:bg-dark-secondary shadow-lg rounded-lg p-2 z-50">
        <div className="p-3 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            {avatarUrl ? (
              <img
                src={avatarUrl}
                alt="Profile"
                className="w-12 h-12 rounded-full border-2 border-gray-200 dark:border-gray-700 flex-shrink-0 object-cover"
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = `https://api.dicebear.com/7.x/avataaars/svg?seed=${encodeURIComponent(user?.email || 'default')}&backgroundColor=b6e3f4`;
                }}
              />
            ) : (
              <div className="w-12 h-12 rounded-full border-2 border-gray-200 dark:border-gray-700 flex-shrink-0 flex items-center justify-center bg-gray-100 dark:bg-gray-700">
                <User size={24} className="text-gray-500 dark:text-gray-400" />
              </div>
            )}
            <div className="min-w-0 flex-1">
              <p className="text-light-primary dark:text-white font-medium truncate">
                {user.displayName || user.email?.split('@')[0] || 'User'}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                {user.email}
              </p>
            </div>
          </div>
        </div>
        <div className="py-2">
          <Link
            to="/profile"
            className="block px-4 py-2 text-light-primary dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition"
          >
            Profile
          </Link>
          <Link
            to="/my-bookings"
            className="block px-4 py-2 text-light-primary dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition"
          >
            My Bookings
          </Link>
          <Link
            to="/settings"
            className="block px-4 py-2 text-light-primary dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition"
          >
            Settings
          </Link>
          <button
            onClick={() => auth.signOut()}
            className="w-full text-left px-4 py-2 text-red-600 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition"
          >
            Sign Out
          </button>
        </div>
      </div>
    )}
  </div>
);

export default Header;
