import React from 'react';

const Blogs = () => {
  const blogs = [
    {
      title: "Top Wedding Trends for 2024",
      excerpt: "Discover the latest trends in wedding planning and decoration",
      date: "March 15, 2024",
      category: "Weddings"
    },
    {
      title: "How to Plan a Successful Corporate Event",
      excerpt: "Essential tips for organizing memorable corporate events",
      date: "March 12, 2024",
      category: "Corporate"
    },
    {
      title: "Creative Birthday Party Ideas",
      excerpt: "Unique ideas to make your birthday celebration special",
      date: "March 10, 2024",
      category: "Parties"
    }
  ];

  return (
    <div className="py-8">
      <h2 className="text-3xl font-bold text-light-primary dark:text-white mb-6">
        Event Planning Blog
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {blogs.map((blog, index) => (
          <article
            key={index}
            className="bg-light-secondary dark:bg-dark-secondary rounded-lg shadow-md overflow-hidden"
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm text-[#ff5a5f]">{blog.category}</span>
                <span className="text-sm text-light-secondary dark:text-gray-400">{blog.date}</span>
              </div>
              <h3 className="text-xl font-semibold text-light-primary dark:text-white mb-2">
                {blog.title}
              </h3>
              <p className="text-light-secondary dark:text-gray-400 mb-4">
                {blog.excerpt}
              </p>
              <button className="text-warm-orange hover:text-warm-pink transition-colors">
                Read More →
              </button>
            </div>
          </article>
        ))}
      </div>
    </div>
  );
};

export default Blogs;