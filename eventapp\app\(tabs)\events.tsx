import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, ActivityIndicator, RefreshControl, PanResponder, SafeAreaView, Dimensions, useColorScheme } from 'react-native';
import { router } from 'expo-router';
import EventData from '@/data/EventData.json';
import { LinearGradient } from 'expo-linear-gradient';
import { ArrowRight } from 'lucide-react-native';
import Colors from '@/constants/Colors';

interface EventType {
  title: string;
  description: string;
  path: string;
  images: string[];
  icon: string;
}

interface ImageIndexState {
  [key: number]: number;
}

const FALLBACK_IMAGE = 'https://via.placeholder.com/400x300?text=Event+Image';

export default function EventsPage() {
  const [eventTypes, setEventTypes] = useState<EventType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState<ImageIndexState>({});
  const [imageErrors, setImageErrors] = useState<{[key: string]: boolean}>({});
  const colorScheme = useColorScheme() ?? 'light';
  const colors = Colors[colorScheme];

  const loadEventData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Convert EventData to array format with null checks
      const events = Object.entries(EventData)
        .filter(([_, value]) => value?.metadata) // Filter out invalid entries
        .map(([key, value]) => ({
          title: value.metadata?.title || 'Untitled Event',
          description: value.metadata?.description || 'No description available',
          path: value.metadata?.path || `/event/${key}`,
          images: value.metadata?.images || [FALLBACK_IMAGE],
          icon: value.metadata?.icon || '🎉'
        }));
      
      setEventTypes(events);
    } catch (err) {
      setError('Failed to load events. Please try again.');
      console.error('Error loading events:', err);
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadEventData();
  }, []);

  const handleImageError = (imageUrl: string) => {
    setImageErrors(prev => ({
      ...prev,
      [imageUrl]: true
    }));
  };

  const handlePrevImage = (eventIndex: number): void => {
    const event = eventTypes[eventIndex];
    if (!event?.images?.length) return;

    setCurrentImageIndex(prev => ({
      ...prev,
      [eventIndex]: ((prev[eventIndex] || 0) - 1 + event.images.length) % event.images.length
    }));
  };

  const handleNextImage = (eventIndex: number): void => {
    const event = eventTypes[eventIndex];
    if (!event?.images?.length) return;

    setCurrentImageIndex(prev => ({
      ...prev,
      [eventIndex]: ((prev[eventIndex] || 0) + 1) % event.images.length
    }));
  };

  const createPanResponder = (eventIndex: number) => {
    return PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderRelease: (_, gestureState) => {
        const { dx } = gestureState;
        const SWIPE_THRESHOLD = 50;

        if (Math.abs(dx) > SWIPE_THRESHOLD) {
          if (dx > 0) {
            handlePrevImage(eventIndex);
          } else {
            handleNextImage(eventIndex);
          }
        }
      },
    });
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadEventData();
  };

  if (isLoading && !refreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#ff5a5f" />
        <Text style={styles.loadingText}>Loading events...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={loadEventData}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView 
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#ff5a5f']}
            tintColor="#ff5a5f"
          />
        }
      >
        {/* Hero Section */}
        <LinearGradient
          colors={['#FFF7E6', '#FFFDE4', '#FFFFFF']}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          style={styles.heroSection}
        >
          <View style={styles.heroContent}>
            <View style={styles.heroTextContainer}>
              <Text style={styles.heroTitle}>
                Discover Amazing Events
              </Text>
              <Text style={styles.heroSubtitle}>
                Browse through our curated collection of events and find the perfect one for your next celebration
              </Text>
            </View>
          </View>
        </LinearGradient>

        {/* Event Types Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Browse Events
          </Text>
          <Text style={[styles.sectionSubtitle, { color: colors.darkGray }]}>
            Discover and book your perfect event with our curated selection of experiences
          </Text>
          
          {eventTypes.map((event, idx) => {
            const panResponder = createPanResponder(idx);
            const currentImage = event.images[currentImageIndex[idx] || 0] || FALLBACK_IMAGE;
            
            return (
              <TouchableOpacity 
                key={idx}
                style={[styles.eventTypeCard, { backgroundColor: colors.card }]}
                onPress={() => {
                  const path = event.path.split('?')[0] as any;
                  const params = new URLSearchParams(event.path.split('?')[1]);
                  router.push({
                    pathname: path,
                    params: Object.fromEntries(params)
                  });
                }}
              >
                <View style={styles.imageContainer} {...panResponder.panHandlers}>
                  <Image 
                    source={{ 
                      uri: imageErrors[currentImage] ? FALLBACK_IMAGE : currentImage
                    }} 
                    style={styles.eventTypeImage}
                    onError={() => handleImageError(currentImage)}
                  />
                  {event.images.length > 1 && (
                    <View style={styles.dots}>
                      {event.images.map((_, imgIdx) => (
                        <View
                          key={imgIdx}
                          style={[
                            styles.dot,
                            (currentImageIndex[idx] || 0) === imgIdx && styles.activeDot
                          ]}
                        />
                      ))}
                    </View>
                  )}
                </View>
                <View style={styles.eventTypeContent}>
                  <View style={styles.eventTypeHeader}>
                    <Text style={[styles.eventTypeTitle, { color: colors.text }]}>
                      {event.title}
                    </Text>
                    <ArrowRight size={20} color={colors.primary} />
                  </View>
                  <Text style={[styles.eventTypeDescription, { color: colors.darkGray }]}>
                    {event.description}
                  </Text>
                </View>
              </TouchableOpacity>
            );
          })}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  heroSection: {
    width: '100%',
    minHeight: 200,
    borderBottomLeftRadius: 32,
    borderBottomRightRadius: 32,
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: '#FFF7E6',
    paddingVertical: 40,
  },
  heroContent: {
    flexDirection: 'column',
    alignItems: 'center',
    maxWidth: 1200,
    marginHorizontal: 'auto',
    width: '100%',
  },
  heroTextContainer: {
    width: '100%',
    maxWidth: 800,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 2,
    padding: 16,
  },
  heroTitle: {
    fontSize: 38,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
    color: '#1a1a1a',
    lineHeight: 44,
    textShadowColor: 'rgba(255, 255, 255, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  heroSubtitle: {
    fontSize: 17,
    textAlign: 'center',
    marginBottom: 32,
    color: '#333333',
    maxWidth: 600,
    lineHeight: 24,
    textShadowColor: 'rgba(255, 255, 255, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },
  section: {
    padding: 16,
    marginTop: 32,
  },
  sectionTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    marginBottom: 24,
  },
  eventTypeCard: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 16,
    marginBottom: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    padding: 12,
  },
  imageContainer: {
    position: 'relative',
    width: 140,
    height: 140,
    marginRight: 12,
  },
  eventTypeImage: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  eventTypeContent: {
    flex: 1,
    justifyContent: 'center',
  },
  eventTypeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  eventTypeTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 8,
  },
  eventTypeDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  dots: {
    position: 'absolute',
    bottom: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255,255,255,0.5)',
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#ff5a5f',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#ff5a5f',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
}); 