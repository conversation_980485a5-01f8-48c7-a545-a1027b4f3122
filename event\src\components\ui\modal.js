import React, { useEffect, useRef } from 'react';
import { X } from 'lucide-react';
import { cn } from '../lib/Utils.tsx';

const Modal = ({
  isOpen,
  onClose,
  title,
  children,
  className,
  size = 'md' // sm, md, lg, xl
}) => {
  const modalRef = useRef(null);

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      // Prevent scrolling when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'auto';
    };
  }, [isOpen, onClose]);

  // Close modal when pressing Escape key
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const sizeClasses = {
    sm: 'max-w-[90%] sm:max-w-md',
    md: 'max-w-[95%] sm:max-w-lg',
    lg: 'max-w-[98%] sm:max-w-xl md:max-w-2xl',
    xl: 'max-w-[98%] sm:max-w-xl md:max-w-3xl lg:max-w-4xl',
  };

  return (
    <div className="fixed inset-0 z-50 flex items-start sm:items-center justify-center bg-black bg-opacity-50 p-2 sm:p-4 animate-fade-in overflow-y-auto">
      <div
        ref={modalRef}
        className={cn(
          "bg-white dark:bg-dark-secondary rounded-lg shadow-xl w-full my-4 sm:my-8",
          sizeClasses[size],
          className
        )}
      >
        <div className="flex justify-between items-center p-3 sm:p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg sm:text-xl font-semibold text-light-primary dark:text-white">
            {title}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
            aria-label="Close"
          >
            <X size={20} className="sm:w-6 sm:h-6" />
          </button>
        </div>
        <div className="p-3 sm:p-6 overflow-y-auto max-h-[calc(100vh-120px)] sm:max-h-[calc(100vh-160px)]">
          {children}
        </div>
      </div>
    </div>
  );
};

export default Modal;
