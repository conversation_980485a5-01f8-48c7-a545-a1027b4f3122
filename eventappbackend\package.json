{"name": "eventapp-backend", "version": "1.0.0", "description": "Node.js backend server for event app with Firebase integration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "init-db": "node scripts/initDatabase.js", "validate-schema": "node -e \"const SchemaValidator = require('./utils/schemaValidator'); console.log('Schema validation utility loaded successfully');\""}, "keywords": ["nodejs", "express", "firebase", "event-management", "rest-api"], "author": "Event App Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "firebase-admin": "^11.11.1", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}