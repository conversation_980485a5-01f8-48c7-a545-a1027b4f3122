import React, { useState, useEffect } from 'react';
import { auth } from '../../lib/firebase';
import { 
  GoogleAuthProvider, 
  signInWithPopup,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword 
} from 'firebase/auth';
import { motion, AnimatePresence } from 'framer-motion';

const LoginButton = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isSignUp, setIsSignUp] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleGoogleLogin = async () => {
    try {
      const provider = new GoogleAuthProvider();
      await signInWithPopup(auth, provider);
      setIsOpen(false);
      setError('');
    } catch (error) {
      console.error('Google login error:', error);
      setError('Failed to sign in with Google. Please try again.');
    }
  };

  const handleEmailAuth = async (e) => {
    e.preventDefault();
    setError('');
    
    try {
      if (isSignUp) {
        await createUserWithEmailAndPassword(auth, email, password);
      } else {
        await signInWithEmailAndPassword(auth, email, password);
      }
      setIsOpen(false);
      setEmail('');
      setPassword('');
    } catch (error) {
      console.error('Email auth error:', error);
      switch (error.code) {
        case 'auth/email-already-in-use':
          setError('This email is already registered. Please sign in instead.');
          break;
        case 'auth/weak-password':
          setError('Password should be at least 6 characters long.');
          break;
        case 'auth/invalid-email':
          setError('Please enter a valid email address.');
          break;
        case 'auth/user-not-found':
        case 'auth/wrong-password':
          setError('Invalid email or password.');
          break;
        default:
          setError('An error occurred. Please try again.');
      }
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    setError('');
    setEmail('');
    setPassword('');
  };

  return (
    <div className="relative -m-1 sm:m-0 ml-3 sm:ml-4">
      <button
        onClick={() => setIsOpen(true)}
        className="btn btn-primary px-3 sm:px-6 py-1.5 sm:py-2.5 rounded-full font-medium transition-all duration-300 hover:scale-105 shadow-sm hover:shadow-md text-sm sm:text-base"
      >
        Login
      </button>

      <AnimatePresence>
        {isOpen && (
          <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
              onClick={handleClose}
            />
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              className={`bg-light-primary dark:bg-dark-secondary rounded-xl p-4 sm:p-6 shadow-custom hover:shadow-hover 
                w-full max-w-[90vw] sm:max-w-[28rem] max-h-[90vh] overflow-y-auto
                relative z-50`}
            >
              <div className="relative">
                <h2 className="text-xl sm:text-2xl font-bold mb-4 sm:mb-6 text-center text-light-primary dark:text-white">
                  {isSignUp ? 'Create Account' : 'Welcome Back'}
                </h2>
                
                {error && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mb-4 p-3 bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-lg text-sm"
                  >
                    {error}
                  </motion.div>
                )}

                <form onSubmit={handleEmailAuth} className="space-y-3 sm:space-y-4 mb-4 sm:mb-6">
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-light-secondary dark:text-gray-300 mb-1">
                      Email
                    </label>
                    <input
                      type="email"
                      id="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full px-3 sm:px-4 py-2 sm:py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg 
                        focus:outline-none focus:ring-2 focus:ring-warm-coral dark:focus:ring-warm-coral 
                        bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm sm:text-base
                        transition-colors duration-200"
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="password" className="block text-sm font-medium text-light-secondary dark:text-gray-300 mb-1">
                      Password
                    </label>
                    <input
                      type="password"
                      id="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="w-full px-3 sm:px-4 py-2 sm:py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg 
                        focus:outline-none focus:ring-2 focus:ring-warm-coral dark:focus:ring-warm-coral 
                        bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm sm:text-base
                        transition-colors duration-200"
                      required
                    />
                  </div>
                  <button
                    type="submit"
                    className="w-full py-2 sm:py-2.5 bg-gradient-to-r from-warm-coral to-warm-pink text-white rounded-full 
                      hover:from-warm-orange hover:to-warm-pink transition-all duration-300 
                      shadow-md hover:shadow-lg transform hover:-translate-y-0.5 
                      focus:outline-none focus:ring-2 focus:ring-warm-coral focus:ring-opacity-50 font-medium
                      text-sm sm:text-base"
                  >
                    {isSignUp ? 'Sign Up' : 'Sign In'}
                  </button>
                </form>

                <div className="relative mb-4 sm:mb-6">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300 dark:border-gray-600"></div>
                  </div>
                  <div className="relative flex justify-center text-xs sm:text-sm">
                    <span className="px-2 bg-light-primary dark:bg-dark-secondary text-gray-500 dark:text-gray-400">
                      Or continue with
                    </span>
                  </div>
                </div>

                <button
                  onClick={handleGoogleLogin}
                  className="w-full flex items-center justify-center gap-2 sm:gap-3 px-3 sm:px-4 py-2 sm:py-2.5 
                    border border-gray-300 dark:border-gray-600 rounded-lg 
                    hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200
                    text-gray-700 dark:text-gray-200 text-sm sm:text-base"
                >
                  <img src="https://www.google.com/favicon.ico" alt="Google" className="w-4 h-4 sm:w-5 sm:h-5" />
                  Continue with Google
                </button>

                <button
                  onClick={() => setIsSignUp(!isSignUp)}
                  className="w-full text-center mt-3 sm:mt-4 text-xs sm:text-sm text-warm-coral dark:text-warm-coral 
                    hover:text-warm-orange dark:hover:text-warm-orange transition-colors duration-200"
                >
                  {isSignUp 
                    ? 'Already have an account? Sign in' 
                    : "Don't have an account? Sign up"}
                </button>

                <button
                  onClick={handleClose}
                  className="absolute -top-2 -right-2 sm:top-0 sm:right-0 p-2 text-gray-500 dark:text-gray-400 
                    hover:text-gray-700 dark:hover:text-gray-200 transition-colors duration-200
                    focus:outline-none focus:ring-2 focus:ring-warm-coral focus:ring-opacity-50 rounded-lg
                    text-sm sm:text-base"
                >
                  ✕
                </button>
              </div>
            </motion.div>
          </div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default LoginButton; 