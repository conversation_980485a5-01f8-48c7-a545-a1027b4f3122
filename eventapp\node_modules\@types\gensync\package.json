{"name": "@types/gensync", "version": "1.0.4", "description": "TypeScript definitions for gensync", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/gensync", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/jakebailey"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "nicolo-ribaudo", "url": "https://github.com/nicolo-ribaudo"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/gensync"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "428e4d6019e8448e51e31690fad5420aed1cdea9deca7b5fb5438b675e80635d", "typeScriptVersion": "4.5"}