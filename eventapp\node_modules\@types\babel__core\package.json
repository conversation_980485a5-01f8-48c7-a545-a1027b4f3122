{"name": "@types/babel__core", "version": "7.20.5", "description": "TypeScript definitions for @babel/core", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__core", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "yortus", "url": "https://github.com/yortus"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/marvinhagemeister"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/mgroenhoff"}, {"name": "<PERSON>", "githubUsername": "Jessidhia", "url": "https://github.com/Jessidhia"}, {"name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr", "url": "https://github.com/ifiokjr"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/babel__core"}, "scripts": {}, "dependencies": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}, "typesPublisherContentHash": "3ece429b02ff9f70503a5644f2b303b04d10e6da7940c91a9eff5e52f2c76b91", "typeScriptVersion": "4.5"}