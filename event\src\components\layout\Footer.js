import React from 'react';
import { useNavigate } from 'react-router-dom';

const Footer = () => {
  const navigate = useNavigate();

  const scrollToServices = () => {
    const servicesSection = document.getElementById('services');
    if (servicesSection) {
      servicesSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const copyPhoneNumber = () => {
    navigator.clipboard.writeText('+91 8008721277')
      .then(() => {
        // You could add a toast notification here if you want
        alert('Phone number copied to clipboard!');
      })
      .catch(err => {
        console.error('Failed to copy phone number:', err);
      });
  };

  const navigateToPrivacyPolicy = () => {
    navigate('/privacy-policy');
  };

  const navigateToCookiePolicy = () => {
    navigate('/cookie-policy');
  };

  const navigateToTermsOfService = () => {
    navigate('/terms-of-service');
  };

  return (
    <footer className="bg-light-primary dark:bg-dark-primary mt-auto">
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-lg font-bold text-light-primary dark:text-white mb-4">About Us</h3>
            <p className="text-light-secondary dark:text-gray-400">
              EaseMyEvent is a tech-driven platform simplifying event planning by directly connecting users with verified vendors. With EaseMyEvent, planning your perfect event is just a few clicks away.
            </p>
          </div>
          <div>
            <h3 className="text-lg font-bold text-light-primary dark:text-white mb-4">Quick Links</h3>
            <div className="space-y-2">
              <button onClick={scrollToServices} className="block text-light-secondary dark:text-gray-400 hover:text-[#ff5a5f] cursor-pointer w-full text-left">Events</button>
              <button onClick={scrollToServices} className="block text-light-secondary dark:text-gray-400 hover:text-[#ff5a5f] cursor-pointer w-full text-left">Services</button>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-bold text-light-primary dark:text-white mb-4">Contact</h3>
            <div className="space-y-2 text-light-secondary dark:text-gray-400">
              <a href="mailto:<EMAIL>" className="hover:text-[#ff5a5f]">Email: <EMAIL></a>
              <button onClick={copyPhoneNumber} className="hover:text-[#ff5a5f] cursor-pointer">Phone: +91 8008721277</button>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-bold text-light-primary dark:text-white mb-4">Legal</h3>
            <div className="space-y-2">
              <button onClick={navigateToPrivacyPolicy} className="block text-light-secondary dark:text-gray-400 hover:text-[#ff5a5f] cursor-pointer w-full text-left">
                Privacy Policy
              </button>
              <button onClick={navigateToTermsOfService} className="block text-light-secondary dark:text-gray-400 hover:text-[#ff5a5f] cursor-pointer w-full text-left">
                Terms of Service
              </button>
              <button onClick={navigateToCookiePolicy} className="block text-light-secondary dark:text-gray-400 hover:text-[#ff5a5f] cursor-pointer w-full text-left">
                Cookie Policy
              </button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;