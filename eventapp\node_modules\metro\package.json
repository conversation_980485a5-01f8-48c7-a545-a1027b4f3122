{"name": "metro", "version": "0.82.1", "description": "🚇 The JavaScript bundler for React Native.", "main": "src/index.js", "bin": "src/cli.js", "exports": {".": "./src/index.js", "./package.json": "./package.json", "./private/*": "./src/*.js", "./src": "./src/index.js", "./src/*.js": "./src/*.js", "./src/*": "./src/*.js"}, "repository": {"type": "git", "url": "**************:facebook/metro.git"}, "scripts": {"prepare-release": "test -d build && rm -rf src.real && mv src src.real && mv build src", "cleanup-release": "test ! -e build && mv src build && mv src.real src"}, "dependencies": {"@babel/code-frame": "^7.24.7", "@babel/core": "^7.25.2", "@babel/generator": "^7.25.0", "@babel/parser": "^7.25.3", "@babel/template": "^7.25.0", "@babel/traverse": "^7.25.3", "@babel/types": "^7.25.2", "accepts": "^1.3.7", "chalk": "^4.0.0", "ci-info": "^2.0.0", "connect": "^3.6.5", "debug": "^4.4.0", "error-stack-parser": "^2.0.6", "flow-enums-runtime": "^0.0.6", "graceful-fs": "^4.2.4", "hermes-parser": "0.25.1", "image-size": "^1.0.2", "invariant": "^2.2.4", "jest-worker": "^29.7.0", "jsc-safe-url": "^0.2.2", "lodash.throttle": "^4.1.1", "metro-babel-transformer": "0.82.1", "metro-cache": "0.82.1", "metro-cache-key": "0.82.1", "metro-config": "0.82.1", "metro-core": "0.82.1", "metro-file-map": "0.82.1", "metro-resolver": "0.82.1", "metro-runtime": "0.82.1", "metro-source-map": "0.82.1", "metro-symbolicate": "0.82.1", "metro-transform-plugins": "0.82.1", "metro-transform-worker": "0.82.1", "mime-types": "^2.1.27", "nullthrows": "^1.1.1", "serialize-error": "^2.1.0", "source-map": "^0.5.6", "throat": "^5.0.0", "ws": "^7.5.10", "yargs": "^17.6.2"}, "devDependencies": {"@babel/plugin-transform-flow-strip-types": "^7.25.2", "@babel/plugin-transform-modules-commonjs": "^7.24.8", "@react-native/babel-preset": "0.78.0", "@react-native/metro-babel-transformer": "0.78.0", "babel-jest": "^29.7.0", "dedent": "^0.7.0", "jest-snapshot": "^29.7.0", "jest-snapshot-serializer-raw": "^1.2.0", "metro-babel-register": "0.82.1", "metro-memory-fs": "0.82.1", "mock-req": "^0.2.0", "mock-res": "^0.6.0", "stack-trace": "^0.0.10"}, "license": "MIT", "engines": {"node": ">=18.18"}}