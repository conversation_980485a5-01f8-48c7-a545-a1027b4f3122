import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image } from 'react-native';
import { useRouter } from 'expo-router';
import serviceData from '../../data/ServiceData.json';
import { ServiceData } from '../../types/service';

export default function ServicesPage() {
  const router = useRouter();
  const services = (serviceData as ServiceData)?.services || [];

  const handleServicePress = (serviceId: string) => {
    router.push({
      pathname: '/service/[id]',
      params: { id: serviceId }
    });
  };

  return (
    <ScrollView style={styles.scrollView}>
      <View style={styles.grid}>
        {services.length > 0 ? (
          services.map((service) => (
            <TouchableOpacity
              key={service.id}
              style={styles.card}
              onPress={() => handleServicePress(service.id)}
              activeOpacity={0.85}
            >
              <View style={styles.imageContainer}>
                <Image
                  source={{ uri: service.image || 'https://via.placeholder.com/400x300?text=Service+Image' }}
                  style={styles.cardImage}
                  resizeMode="cover"
                />
              </View>
              <Text style={styles.icon}>{service.icon}</Text>
              <Text style={styles.title}>{service.title}</Text>
              <Text style={styles.description} numberOfLines={2}>
                {service.description}
              </Text>
              <View style={styles.features}>
                {service.features?.slice(0, 2).map((feature, i) => (
                  <Text key={i} style={styles.featureItem}>• {feature}</Text>
                ))}
              </View>
            </TouchableOpacity>
          ))
        ) : (
          <Text style={styles.errorText}>No services available</Text>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    backgroundColor: '#fff',
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 8,
  },
  card: {
    width: '45%',
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 0,
    margin: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 4,
    overflow: 'hidden',
  },
  imageContainer: {
    width: '100%',
    aspectRatio: 1.2,
    backgroundColor: '#f0f0f0',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    overflow: 'hidden',
  },
  cardImage: {
    width: '100%',
    height: '100%',
  },
  icon: {
    fontSize: 28,
    marginTop: -18,
    alignSelf: 'flex-end',
    marginRight: 12,
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 4,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 2,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#222',
    marginLeft: 12,
    marginTop: 4,
  },
  description: {
    fontSize: 13,
    color: '#666',
    marginBottom: 8,
    marginLeft: 12,
    marginRight: 12,
  },
  features: {
    marginTop: 2,
    marginLeft: 12,
    marginBottom: 8,
  },
  featureItem: {
    fontSize: 12,
    color: '#555',
    marginBottom: 2,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 20,
  },
}); 