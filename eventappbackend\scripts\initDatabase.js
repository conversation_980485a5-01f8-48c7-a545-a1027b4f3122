/**
 * Database Initialization Script
 * 
 * This script:
 * 1. Creates necessary Firestore indexes
 * 2. Seeds initial data
 * 3. Sets up database structure
 * 4. Validates schema compliance
 */

const { getDb, firestoreHelpers, collections } = require('../config/firebase');
const SchemaValidator = require('../utils/schemaValidator');
const eventData = require('../../eventapp/data/EventData.json');
const serviceData = require('../../eventapp/data/ServiceData.json');

class DatabaseInitializer {
  constructor() {
    this.db = null;
  }

  async initialize() {
    try {
      console.log('🚀 Starting database initialization...');
      
      // Initialize Firebase connection
      this.db = getDb();
      console.log('✅ Firebase connection established');
      
      // Create indexes
      await this.createIndexes();
      
      // Seed initial data
      await this.seedData();
      
      // Validate existing data
      await this.validateData();
      
      console.log('🎉 Database initialization completed successfully!');
      
    } catch (error) {
      console.error('❌ Database initialization failed:', error);
      throw error;
    }
  }

  /**
   * Create Firestore indexes for optimal querying
   * Note: In production, these should be created via Firebase Console or CLI
   */
  async createIndexes() {
    console.log('📊 Creating database indexes...');
    
    // Note: Firestore indexes are typically created automatically when queries are run
    // or manually via Firebase Console. This is more for documentation.
    
    const indexDefinitions = [
      // Bookings indexes
      {
        collection: 'bookings',
        fields: [
          { field: 'status', order: 'asc' },
          { field: 'createdAt', order: 'desc' }
        ]
      },
      {
        collection: 'bookings',
        fields: [
          { field: 'eventType', order: 'asc' },
          { field: 'eventDate', order: 'asc' }
        ]
      },
      {
        collection: 'bookings',
        fields: [
          { field: 'contactEmail', order: 'asc' },
          { field: 'createdAt', order: 'desc' }
        ]
      },
      
      // Services indexes
      {
        collection: 'services',
        fields: [
          { field: 'category', order: 'asc' },
          { field: 'rating', order: 'desc' }
        ]
      },
      {
        collection: 'services',
        fields: [
          { field: 'isActive', order: 'asc' },
          { field: 'createdAt', order: 'desc' }
        ]
      },
      
      // Vendors indexes
      {
        collection: 'vendors',
        fields: [
          { field: 'isActive', order: 'asc' },
          { field: 'isVerified', order: 'asc' },
          { field: 'rating', order: 'desc' }
        ]
      },
      
      // Reviews indexes
      {
        collection: 'reviews',
        fields: [
          { field: 'vendorId', order: 'asc' },
          { field: 'isVerified', order: 'asc' },
          { field: 'createdAt', order: 'desc' }
        ]
      }
    ];
    
    console.log(`📋 Index definitions prepared for ${indexDefinitions.length} collections`);
    console.log('💡 Note: Create these indexes in Firebase Console for production use');
    
    // Log index creation commands for Firebase CLI
    console.log('\n🔧 Firebase CLI commands to create indexes:');
    indexDefinitions.forEach(index => {
      const fields = index.fields.map(f => `${f.field}:${f.order}`).join(',');
      console.log(`firebase firestore:indexes:create --collection-group=${index.collection} --query-scope=COLLECTION --fields="${fields}"`);
    });
  }

  /**
   * Seed initial data from JSON files
   */
  async seedData() {
    console.log('🌱 Seeding initial data...');
    
    try {
      // Seed events
      await this.seedEvents();
      
      // Seed services
      await this.seedServices();
      
      // Create sample admin user (if needed)
      await this.createSampleData();
      
    } catch (error) {
      console.error('❌ Error seeding data:', error);
      throw error;
    }
  }

  /**
   * Seed event data
   */
  async seedEvents() {
    console.log('📅 Seeding event data...');
    
    const existingEvents = await firestoreHelpers.getCollection(collections.EVENTS, 1);
    if (existingEvents.length > 0) {
      console.log('⏭️  Events already exist, skipping seed');
      return;
    }

    const promises = Object.keys(eventData).map(async (eventType) => {
      const eventDoc = {
        type: eventType,
        ...eventData[eventType],
        isStatic: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      // Validate before saving
      const validation = SchemaValidator.validateEvent(eventDoc);
      if (!validation.isValid) {
        console.warn(`⚠️  Event ${eventType} validation failed:`, validation.errors);
        return null;
      }
      
      const docId = await firestoreHelpers.addDocument(collections.EVENTS, eventDoc);
      console.log(`✅ Seeded event: ${eventType} (${docId})`);
      return docId;
    });

    const results = await Promise.all(promises);
    const successCount = results.filter(r => r !== null).length;
    console.log(`🎯 Seeded ${successCount} events successfully`);
  }

  /**
   * Seed service data
   */
  async seedServices() {
    console.log('🛍️  Seeding service data...');
    
    const existingServices = await firestoreHelpers.getCollection(collections.SERVICES, 1);
    if (existingServices.length > 0) {
      console.log('⏭️  Services already exist, skipping seed');
      return;
    }

    if (!serviceData.services || !Array.isArray(serviceData.services)) {
      console.warn('⚠️  Invalid service data format');
      return;
    }

    const promises = serviceData.services.map(async (service) => {
      const serviceDoc = {
        ...service,
        isStatic: true,
        isActive: true,
        rating: 0,
        reviewCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      // Validate before saving
      const validation = SchemaValidator.validateService(serviceDoc);
      if (!validation.isValid) {
        console.warn(`⚠️  Service ${service.id} validation failed:`, validation.errors);
        return null;
      }
      
      const docId = await firestoreHelpers.addDocument(collections.SERVICES, serviceDoc);
      console.log(`✅ Seeded service: ${service.title} (${docId})`);
      return docId;
    });

    const results = await Promise.all(promises);
    const successCount = results.filter(r => r !== null).length;
    console.log(`🎯 Seeded ${successCount} services successfully`);
  }

  /**
   * Create sample data for testing
   */
  async createSampleData() {
    console.log('🧪 Creating sample data...');
    
    // Sample booking for testing
    const sampleBooking = {
      bookingNumber: 'EVT-SAMPLE-001',
      eventType: 'Birthday',
      eventDate: '2024-12-25',
      eventTime: '18:00',
      location: 'Sample Venue, Mumbai',
      guestCount: '21-50 guests',
      services: ['Decoration', 'Catering', 'Photography'],
      contactName: 'John Doe',
      contactEmail: '<EMAIL>',
      contactPhone: '9876543210',
      status: 'pending',
      estimatedCost: 75000,
      specialRequests: 'Sample booking for testing purposes',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    try {
      const existingBookings = await firestoreHelpers.queryDocuments(
        collections.BOOKINGS,
        [{ field: 'bookingNumber', operator: '==', value: 'EVT-SAMPLE-001' }]
      );

      if (existingBookings.length === 0) {
        const validation = SchemaValidator.validateBooking(sampleBooking);
        if (validation.isValid) {
          const bookingId = await firestoreHelpers.addDocument(collections.BOOKINGS, sampleBooking);
          console.log(`✅ Created sample booking: ${bookingId}`);
        } else {
          console.warn('⚠️  Sample booking validation failed:', validation.errors);
        }
      }
    } catch (error) {
      console.warn('⚠️  Could not create sample booking:', error.message);
    }
  }

  /**
   * Validate existing data against schemas
   */
  async validateData() {
    console.log('🔍 Validating existing data...');
    
    try {
      // Validate events
      const events = await firestoreHelpers.getCollection(collections.EVENTS, 10);
      let validEvents = 0;
      events.forEach(event => {
        const validation = SchemaValidator.validateEvent(event);
        if (validation.isValid) {
          validEvents++;
        } else {
          console.warn(`⚠️  Event ${event.id} validation issues:`, validation.errors);
        }
      });
      console.log(`✅ ${validEvents}/${events.length} events are valid`);

      // Validate services
      const services = await firestoreHelpers.getCollection(collections.SERVICES, 10);
      let validServices = 0;
      services.forEach(service => {
        const validation = SchemaValidator.validateService(service);
        if (validation.isValid) {
          validServices++;
        } else {
          console.warn(`⚠️  Service ${service.id} validation issues:`, validation.errors);
        }
      });
      console.log(`✅ ${validServices}/${services.length} services are valid`);

    } catch (error) {
      console.warn('⚠️  Data validation encountered issues:', error.message);
    }
  }
}

// Run initialization if called directly
if (require.main === module) {
  const initializer = new DatabaseInitializer();
  initializer.initialize()
    .then(() => {
      console.log('🏁 Database initialization script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Database initialization script failed:', error);
      process.exit(1);
    });
}

module.exports = DatabaseInitializer;
