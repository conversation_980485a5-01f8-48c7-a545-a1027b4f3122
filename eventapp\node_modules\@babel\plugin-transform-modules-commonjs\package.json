{"name": "@babel/plugin-transform-modules-commonjs", "version": "7.26.3", "description": "This plugin transforms ES2015 modules to CommonJS", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-modules-commonjs"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-module-transforms": "^7.26.0", "@babel/helper-plugin-utils": "^7.25.9"}, "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/helper-plugin-test-runner": "^7.25.9", "@babel/plugin-external-helpers": "^7.25.9"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-commonjs", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}