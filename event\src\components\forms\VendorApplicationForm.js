import React, { useState } from 'react';
import { Button } from '../ui/button';

const VendorApplicationForm = ({ onSubmitSuccess }) => {
  const [formState, setFormState] = useState({
    fullName: '',
    email: '',
    phone: '',
    businessName: '',
    serviceType: '',
    experience: '',
    website: '',
    location: '',
    description: '',
    submitting: false,
    success: false,
    error: null
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormState(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    setFormState(prev => ({ ...prev, submitting: true, error: null }));

    try {
      // Get form data
      const form = e.target;
      const formData = new FormData(form);

      // Add form-name field which Netlify requires
      formData.append('form-name', 'vendor-application');

      // Submit the form data to Netlify
      const response = await fetch('/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams(formData).toString()
      });

      if (!response.ok) {
        throw new Error(`Form submission failed: ${response.status}`);
      }

      // Reset form and show success message
      setFormState({
        fullName: '',
        email: '',
        phone: '',
        businessName: '',
        serviceType: '',
        experience: '',
        website: '',
        location: '',
        description: '',
        submitting: false,
        success: true,
        error: null
      });

      if (onSubmitSuccess) {
        // Wait a moment before closing the modal to show success message
        setTimeout(() => {
          onSubmitSuccess();
        }, 3000);
      }
    } catch (error) {
      console.error('Form submission error:', error);
      setFormState(prev => ({
        ...prev,
        submitting: false,
        error: 'There was an error submitting your application. Please try again.'
      }));
    }
  };

  const serviceTypes = [
    'Catering',
    'Decoration',
    'Photography',
    'Videography',
    'Music/DJ',
    'Venue',
    'Priest/Religious Services',
    'Event Planning',
    'Other'
  ];

  const experienceLevels = [
    'Less than 1 year',
    '1-3 years',
    '3-5 years',
    '5-10 years',
    'More than 10 years'
  ];

  if (formState.success) {
    return (
      <div className="text-center py-6 sm:py-8 px-4 sm:px-0">
        <div className="mb-4 text-celebrate-orange">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 sm:h-16 sm:w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 className="text-xl sm:text-2xl font-bold mb-2">Application Submitted!</h3>
        <p className="text-gray-600 mb-6 text-sm sm:text-base">Thank you for your interest in joining our vendor network. We'll review your application and get back to you soon.</p>
        <Button
          onClick={onSubmitSuccess}
          className="bg-celebrate-orange hover:bg-celebrate-red text-white w-full sm:w-auto px-6 py-2 sm:py-3"
        >
          Close
        </Button>
      </div>
    );
  }

  return (
    <form
      onSubmit={handleSubmit}
      className="space-y-4 sm:space-y-6"
      name="vendor-application"
      method="POST"
      data-netlify="true"
      netlify-honeypot="bot-field"
    >
      {/* Hidden field for Netlify forms */}
      <input type="hidden" name="form-name" value="vendor-application" />
      <p className="hidden">
        <label>Don't fill this out if you're human: <input name="bot-field" /></label>
      </p>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
        <div>
          <label htmlFor="fullName" className="block text-sm sm:text-base font-medium text-gray-700 mb-1">
            Full Name *
          </label>
          <input
            type="text"
            id="fullName"
            name="fullName"
            value={formState.fullName}
            onChange={handleChange}
            required
            className="w-full px-3 sm:px-4 py-2 sm:py-3 text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-celebrate-orange focus:border-celebrate-orange transition-all"
          />
        </div>

        <div>
          <label htmlFor="email" className="block text-sm sm:text-base font-medium text-gray-700 mb-1">
            Email Address *
          </label>
          <input
            type="email"
            id="email"
            name="email"
            value={formState.email}
            onChange={handleChange}
            required
            className="w-full px-3 sm:px-4 py-2 sm:py-3 text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-celebrate-orange focus:border-celebrate-orange transition-all"
          />
        </div>

        <div>
          <label htmlFor="phone" className="block text-sm sm:text-base font-medium text-gray-700 mb-1">
            Phone Number *
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            value={formState.phone}
            onChange={handleChange}
            required
            className="w-full px-3 sm:px-4 py-2 sm:py-3 text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-celebrate-orange focus:border-celebrate-orange transition-all"
          />
        </div>

        <div>
          <label htmlFor="businessName" className="block text-sm sm:text-base font-medium text-gray-700 mb-1">
            Business Name *
          </label>
          <input
            type="text"
            id="businessName"
            name="businessName"
            value={formState.businessName}
            onChange={handleChange}
            required
            className="w-full px-3 sm:px-4 py-2 sm:py-3 text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-celebrate-orange focus:border-celebrate-orange transition-all"
          />
        </div>

        <div>
          <label htmlFor="serviceType" className="block text-sm sm:text-base font-medium text-gray-700 mb-1">
            Service Type *
          </label>
          <select
            id="serviceType"
            name="serviceType"
            value={formState.serviceType}
            onChange={handleChange}
            required
            className="w-full px-3 sm:px-4 py-2 sm:py-3 text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-celebrate-orange focus:border-celebrate-orange transition-all"
          >
            <option value="">Select a service type</option>
            {serviceTypes.map(type => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="experience" className="block text-sm sm:text-base font-medium text-gray-700 mb-1">
            Years of Experience *
          </label>
          <select
            id="experience"
            name="experience"
            value={formState.experience}
            onChange={handleChange}
            required
            className="w-full px-3 sm:px-4 py-2 sm:py-3 text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-celebrate-orange focus:border-celebrate-orange transition-all"
          >
            <option value="">Select experience level</option>
            {experienceLevels.map(level => (
              <option key={level} value={level}>{level}</option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="website" className="block text-sm sm:text-base font-medium text-gray-700 mb-1">
            Website/Social Media (Optional)
          </label>
          <input
            type="url"
            id="website"
            name="website"
            value={formState.website}
            onChange={handleChange}
            className="w-full px-3 sm:px-4 py-2 sm:py-3 text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-celebrate-orange focus:border-celebrate-orange transition-all"
            placeholder="https://"
          />
        </div>

        <div>
          <label htmlFor="location" className="block text-sm sm:text-base font-medium text-gray-700 mb-1">
            Business Location *
          </label>
          <input
            type="text"
            id="location"
            name="location"
            value={formState.location}
            onChange={handleChange}
            required
            placeholder="City, State"
            className="w-full px-3 sm:px-4 py-2 sm:py-3 text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-celebrate-orange focus:border-celebrate-orange transition-all"
          />
        </div>
      </div>

      <div className="col-span-2">
        <label htmlFor="description" className="block text-sm sm:text-base font-medium text-gray-700 mb-1">
          Tell us about your services (Optional)
        </label>
        <textarea
          id="description"
          name="description"
          value={formState.description}
          onChange={handleChange}
          rows="4"
          className="w-full px-3 sm:px-4 py-2 sm:py-3 text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-celebrate-orange focus:border-celebrate-orange transition-all"
          placeholder="Describe your services, specialties, and what makes you unique..."
        />
      </div>

      {formState.error && (
        <div className="text-red-500 text-sm">{formState.error}</div>
      )}

      <div className="flex justify-center sm:justify-end mt-6">
        <Button
          type="submit"
          disabled={formState.submitting}
          className="w-full sm:w-auto bg-gradient-to-r from-celebrate-orange to-celebrate-red text-white px-6 py-3 text-base sm:text-lg font-medium"
        >
          {formState.submitting ? 'Submitting...' : 'Submit Application'}
        </Button>
      </div>
    </form>
  );
};

export default VendorApplicationForm;
