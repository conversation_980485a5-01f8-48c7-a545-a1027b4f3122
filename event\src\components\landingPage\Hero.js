import React from "react";
import { Button } from "../ui/button";
import { useNavigate } from "react-router-dom";

const Hero = () => {
  const navigate = useNavigate();

  const scrollToHowItWorks = () => {
    const howItWorksSection = document.getElementById('how-it-works');
    if (howItWorksSection) {
      howItWorksSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="relative py-20 md:py-28 overflow-hidden bg-gradient-to-b from-celebrate-cream to-white w-full min-w-full">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-8 appear-animation">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
              Celebrate Your Special Moments <span className="gradient-text-solid-end">Hassle-Free</span>
            </h1>
            <p className="text-lg md:text-xl text-slate-700 max-w-lg">
              From birthdays to house warmings to traditional Hindu poojas,
              we make organizing events just a few clicks away—at prices lower than traditional planners.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                className="bg-celebrate-orange hover:bg-celebrate-red text-white text-lg py-6 px-8"
                onClick={() => navigate('/events')}
              >
                Book an Event
              </Button>
              <Button
                variant="outline"
                className="border-celebrate-orange text-celebrate-orange hover:text-celebrate-red hover:border-celebrate-red text-lg py-6 px-8"
                onClick={scrollToHowItWorks}
              >
                How It Works
              </Button>
            </div>
          </div>
          <div className="relative">
            <div className="absolute -top-8 -left-8 w-24 h-24 bg-celebrate-gold/20 rounded-full animate-float"></div>
            <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-celebrate-orange/20 rounded-full animate-float" style={{ animationDelay: '1s' }}></div>
            <div className="relative z-10 rounded-2xl overflow-hidden shadow-2xl">
              <img
                src="https://images.unsplash.com/photo-1511795409834-ef04bbd61622?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1169&q=80"
                alt="Celebration"
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
