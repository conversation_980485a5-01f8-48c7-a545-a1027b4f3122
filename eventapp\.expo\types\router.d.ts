/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/bookings`; params?: Router.UnknownInputParams; } | { pathname: `/edit-profile`; params?: Router.UnknownInputParams; } | { pathname: `/einvites`; params?: Router.UnknownInputParams; } | { pathname: `/template-editor`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/events` | `/events`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/services` | `/services`; params?: Router.UnknownInputParams; } | { pathname: `/auth`; params?: Router.UnknownInputParams; } | { pathname: `/auth/login`; params?: Router.UnknownInputParams; } | { pathname: `/auth/signup`; params?: Router.UnknownInputParams; } | { pathname: `/event/booking`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/event/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/invitation/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/service/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/bookings`; params?: Router.UnknownOutputParams; } | { pathname: `/edit-profile`; params?: Router.UnknownOutputParams; } | { pathname: `/einvites`; params?: Router.UnknownOutputParams; } | { pathname: `/template-editor`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/events` | `/events`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/services` | `/services`; params?: Router.UnknownOutputParams; } | { pathname: `/auth`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/login`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/signup`; params?: Router.UnknownOutputParams; } | { pathname: `/event/booking`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } } | { pathname: `/event/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/invitation/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/service/[id]`, params: Router.UnknownOutputParams & { id: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/bookings${`?${string}` | `#${string}` | ''}` | `/edit-profile${`?${string}` | `#${string}` | ''}` | `/einvites${`?${string}` | `#${string}` | ''}` | `/template-editor${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/events${`?${string}` | `#${string}` | ''}` | `/events${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/profile${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/services${`?${string}` | `#${string}` | ''}` | `/services${`?${string}` | `#${string}` | ''}` | `/auth${`?${string}` | `#${string}` | ''}` | `/auth/login${`?${string}` | `#${string}` | ''}` | `/auth/signup${`?${string}` | `#${string}` | ''}` | `/event/booking${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/bookings`; params?: Router.UnknownInputParams; } | { pathname: `/edit-profile`; params?: Router.UnknownInputParams; } | { pathname: `/einvites`; params?: Router.UnknownInputParams; } | { pathname: `/template-editor`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/events` | `/events`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/services` | `/services`; params?: Router.UnknownInputParams; } | { pathname: `/auth`; params?: Router.UnknownInputParams; } | { pathname: `/auth/login`; params?: Router.UnknownInputParams; } | { pathname: `/auth/signup`; params?: Router.UnknownInputParams; } | { pathname: `/event/booking`; params?: Router.UnknownInputParams; } | `/+not-found` | `/event/${Router.SingleRoutePart<T>}` | `/invitation/${Router.SingleRoutePart<T>}` | `/service/${Router.SingleRoutePart<T>}` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/event/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/invitation/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/service/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
    }
  }
}
