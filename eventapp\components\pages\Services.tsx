import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';

const services = [
  {
    title: 'Event Planning',
    description: 'Complete event planning and coordination services',
    features: ['Venue Selection', 'Vendor Management', 'Timeline Planning'],
  },
  {
    title: 'Decoration',
    description: 'Custom decoration and theme setup',
    features: ['Theme Design', 'Floral Arrangements', 'Lighting Setup'],
  },
  {
    title: 'Catering',
    description: 'Professional catering services',
    features: ['Custom Menus', 'Dietary Options', 'Professional Service'],
  },
];

export default function ServicesPage() {
  return (
    <ScrollView style={styles.container}>
      <Text style={styles.header}>Our Services</Text>
      {services.map((service, idx) => (
        <View key={idx} style={styles.card}>
          <Text style={styles.title}>{service.title}</Text>
          <Text style={styles.description}>{service.description}</Text>
          <View style={styles.features}>
            {service.features.map((feature, i) => (
              <Text key={i} style={styles.featureItem}>• {feature}</Text>
            ))}
          </View>
        </View>
      ))}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  card: {
    backgroundColor: '#f7f7f7',
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 6,
  },
  description: {
    fontSize: 15,
    color: '#555',
    marginBottom: 8,
  },
  features: {
    marginLeft: 8,
  },
  featureItem: {
    fontSize: 14,
    color: '#333',
    marginBottom: 2,
  },
}); 