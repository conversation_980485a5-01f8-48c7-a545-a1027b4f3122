import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image } from 'react-native';

const eventTypes = [
  {
    title: 'House Warming',
    description: 'Traditional house warming ceremony planning',
    image: 'https://images.pexels.com/photos/7180617/pexels-photo-7180617.jpeg',
  },
  {
    title: 'Weddings',
    description: 'Create your perfect wedding day',
    image: 'https://images.pexels.com/photos/1114425/pexels-photo-1114425.jpeg',
  },
  {
    title: 'Corporate Events',
    description: 'Professional business event planning',
    image: 'https://images.pexels.com/photos/2774556/pexels-photo-2774556.jpeg',
  },
  {
    title: 'Birthday Parties',
    description: 'Memorable birthday celebrations',
    image: 'https://images.pexels.com/photos/7180617/pexels-photo-7180617.jpeg',
  },
  {
    title: 'Conferences',
    description: 'Large-scale conference organization',
    image: 'https://images.pexels.com/photos/7092613/pexels-photo-7092613.jpeg',
  },
];

export default function EventsPage() {
  return (
    <ScrollView style={styles.container}>
      <Text style={styles.header}>Browse Events</Text>
      <View style={styles.grid}>
        {eventTypes.map((event, idx) => (
          <View key={idx} style={styles.card}>
            <Image source={{ uri: event.image }} style={styles.image} />
            <Text style={styles.title}>{event.title}</Text>
            <Text style={styles.description}>{event.description}</Text>
            <TouchableOpacity style={styles.button}>
              <Text style={styles.buttonText}>Book Now</Text>
            </TouchableOpacity>
          </View>
        ))}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  card: {
    width: '48%',
    backgroundColor: '#f7f7f7',
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 1,
  },
  image: {
    width: '100%',
    height: 100,
    borderRadius: 8,
    marginBottom: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 6,
  },
  description: {
    fontSize: 15,
    color: '#555',
    marginBottom: 12,
  },
  button: {
    backgroundColor: '#ff5a5f',
    paddingVertical: 10,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
}); 