{"name": "react", "description": "React is a JavaScript library for building user interfaces.", "keywords": ["react"], "version": "19.0.0", "homepage": "https://react.dev/", "bugs": "https://github.com/facebook/react/issues", "license": "MIT", "files": ["LICENSE", "README.md", "index.js", "cjs/", "compiler-runtime.js", "jsx-runtime.js", "jsx-runtime.react-server.js", "jsx-dev-runtime.js", "jsx-dev-runtime.react-server.js", "react.react-server.js"], "main": "index.js", "exports": {".": {"react-server": "./react.react-server.js", "default": "./index.js"}, "./package.json": "./package.json", "./jsx-runtime": {"react-server": "./jsx-runtime.react-server.js", "default": "./jsx-runtime.js"}, "./jsx-dev-runtime": {"react-server": "./jsx-dev-runtime.react-server.js", "default": "./jsx-dev-runtime.js"}, "./compiler-runtime": {"react-server": "./compiler-runtime.js", "default": "./compiler-runtime.js"}}, "repository": {"type": "git", "url": "https://github.com/facebook/react.git", "directory": "packages/react"}, "engines": {"node": ">=0.10.0"}}