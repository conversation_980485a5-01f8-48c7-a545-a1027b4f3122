{"name": "@types/cookie", "version": "0.6.0", "description": "TypeScript definitions for cookie", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cookie", "license": "MIT", "contributors": [{"name": "Pine Mizune", "githubUsername": "pine", "url": "https://github.com/pine"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/cookie"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "0fbdf9c886fb40c8dc7677b8b7d0f9e02ac5235e163d90244a4fb9004736a575", "typeScriptVersion": "4.5"}