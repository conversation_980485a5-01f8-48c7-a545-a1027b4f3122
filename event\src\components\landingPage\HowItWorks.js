import React, { useEffect } from 'react';
import { Mouse<PERSON>ointer<PERSON>lick, Palette, Calendar, PartyPopper } from 'lucide-react';

const steps = [
  {
    title: "Browse & Select",
    description: "Browse our comprehensive selection of premium event packages tailored to your specific needs.",
    icon: MousePointerClick
  },
  {
    title: "Personalize Your Experience",
    description: "Customize your event package with our extensive range of services and amenities to create your perfect occasion.",
    icon: Palette
  },
  {
    title: "Confirm Details",
    description: "Review and confirm your selections, then proceed with our streamlined booking process.",
    icon: Calendar
  },
  {
    title: "Enjoy Your Event",
    description: "Sit back and enjoy your event while our professional team handles every detail with precision and care.",
    icon: PartyPopper
  }
];

const HowItWorks = () => {
  // Theme context is used for dark mode styling which is applied via CSS classes

  // Add scroll animation effect with improved threshold and margin for better mobile experience
  useEffect(() => {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.remove('opacity-0');
          entry.target.classList.add('opacity-100');
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px' // Trigger slightly before the element is fully in view
    });

    const timelineItems = document.querySelectorAll('.timeline-item');
    timelineItems.forEach(item => observer.observe(item));

    return () => {
      timelineItems.forEach(item => observer.unobserve(item));
    };
  }, []);

  return (
    <section id="how-it-works" className="section-padding bg-white">
      <div className="container-custom">
        <h2 className="section-title">How It Works</h2>
        <p className="section-subtitle">
          Transform your vision into reality with our seamless event planning process
        </p>

        <div className="relative mt-8 sm:mt-12">
          {/* Timeline line - visible on all screen sizes but styled differently */}
          <div className="absolute left-4 sm:left-1/2 top-0 bottom-0 w-1 bg-gradient-to-b from-primary/80 via-secondary to-accent/80 sm:-translate-x-1/2 rounded-full"></div>

          {/* Steps */}
          <div className="space-y-8 sm:space-y-10 md:space-y-16 relative">
            {steps.map((step, index) => (
              <div
                key={step.title}
                className="timeline-item flex sm:grid sm:grid-cols-2 sm:gap-6 md:gap-8 items-start sm:items-center relative opacity-0 transition-all duration-700 transform translate-y-4"
                style={{ transitionDelay: `${index * 100}ms` }}
              >
                {/* Timeline dot with pulse effect - positioned differently on mobile vs desktop */}
                <div className="absolute left-4 sm:left-1/2 top-6 sm:top-1/2 sm:-translate-y-1/2 sm:-translate-x-1/2 w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-primary border-4 border-white z-10">
                  <div className="absolute inset-0 rounded-full bg-primary/30 animate-ping"></div>
                </div>

                {/* Content section - different layout on mobile vs desktop */}
                <div className={`pl-12 sm:pl-0 ${index % 2 === 0 ? 'sm:text-right sm:pr-12 md:pr-16' : 'sm:order-2 sm:text-left sm:pl-12 md:pl-16'}`}>
                  <div className="timeline-card group hover:-translate-y-1 transition-transform duration-300">
                    {/* Mobile icon - shown inline with content on small screens */}
                    <div className="flex sm:hidden items-center mb-3">
                      <div className="bg-primary/10 rounded-full w-10 h-10 flex items-center justify-center mr-3">
                        <step.icon className="text-primary w-5 h-5" />
                      </div>
                      <h3 className="timeline-title mb-0">
                        <span className="timeline-number">{index + 1}.</span> {step.title}
                      </h3>
                    </div>

                    {/* Desktop title - hidden on mobile */}
                    <h3 className={`hidden sm:flex timeline-title sm:items-center ${index % 2 === 0 ? 'sm:justify-end' : 'sm:justify-start'}`}>
                      <span className="timeline-number">{index + 1}.</span> {step.title}
                    </h3>
                    <p className="timeline-description">{step.description}</p>
                  </div>
                </div>

                {/* Desktop icon container - hidden on mobile */}
                <div className={`hidden sm:flex items-center justify-center group ${index % 2 === 0 ? 'sm:order-2 sm:justify-start sm:pl-12 md:pl-16' : 'sm:justify-end sm:pr-12 md:pr-16'}`}>
                  <div className="timeline-icon-container group-hover:scale-110 transition-transform duration-300">
                    <step.icon className="timeline-icon" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Call to action button */}
        <div className="text-center mt-12 sm:mt-16">
          <button className="btn-primary inline-flex items-center text-sm sm:text-base px-5 py-2 sm:px-6 sm:py-3">
            Browse Events
          </button>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;