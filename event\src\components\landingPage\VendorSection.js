
import React, { useState } from 'react';
import { DollarSign, Users, Calendar, Star } from 'lucide-react';
import { Button } from '../ui/button';
import Modal from '../ui/modal';
import VendorApplicationForm from '../forms/VendorApplicationForm';
import vendorHandshake from '../../assets/vendor-handshake.jpg';

const benefits = [
  {
    title: "Grow Your Business",
    description: "Connect with new customers and expand your client base through our platform.",
    icon: DollarSign
  },
  {
    title: "Simple Booking System",
    description: "Our streamlined booking process makes it easy to manage your schedule.",
    icon: Calendar
  },
  {
    title: "Build Your Reputation",
    description: "Collect reviews and ratings to showcase your quality service.",
    icon: Star
  },
  {
    title: "Join a Community",
    description: "Connect with other vendors for collaboration and networking opportunities.",
    icon: Users
  }
];

const VendorSection = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => setIsModalOpen(true);
  const handleCloseModal = () => setIsModalOpen(false);

  return (
    <section id="vendors" className="section-padding bg-white">
      <div className="container-custom">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Join Our Vendor Network</h2>
            <p className="text-lg mb-6 text-gray-700">
              Become a EaseMyEvent vendor and bring your services to clients looking for exactly what you offer.
            </p>

            <div className="space-y-6 mb-8">
              {benefits.map((benefit) => (
                <div key={benefit.title} className="flex items-start">
                  <div className="mr-4 mt-1 bg-primary/10 rounded-full p-2">
                    <benefit.icon className="text-primary w-5 h-5" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">{benefit.title}</h3>
                    <p className="text-gray-600">{benefit.description}</p>
                  </div>
                </div>
              ))}
            </div>
            <div className="flex justify-center">
              <Button size="lg" onClick={handleOpenModal}>Apply as a Vendor</Button>
            </div>
          </div>

          <div className="relative">
            <div className="absolute -top-6 -left-6 w-40 h-40 bg-primary/20 rounded-full -z-10"></div>
            <div className="absolute -bottom-6 -right-6 w-24 h-24 bg-primary/30 rounded-full -z-10"></div>
            <img
              src={vendorHandshake}
              alt="Vendor partnership handshake"
              className="rounded-lg shadow-xl w-full h-auto object-cover"
            />
          </div>
        </div>
      </div>

      {/* Vendor Application Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        title="Vendor Application"
        size="lg"
        className="max-h-[95vh] sm:max-h-[90vh]"
      >
        <VendorApplicationForm onSubmitSuccess={handleCloseModal} />
      </Modal>
    </section>
  );
};

export default VendorSection;
